
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基本使用示例
展示MD Vector Processor的主要功能
"""

import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.data_loader import FileScanner, MDLoader
from src.preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from src.vectorizer import TextEmbedding, VectorTransformer
from src.indexer import IndexBuilder, VectorSearcher
from src.storage import VectorStore, MetadataManager
from src.utils import setup_logger, Timer, ConfigUtils

def load_example_config():
    """加载示例配置"""
    config_path = project_root / 'config' / 'config.yaml'
    return ConfigUtils.load_config(config_path)

def setup_example_logger():
    """设置示例日志"""
    config = load_example_config()
    return setup_logger(config, 'example')

def basic_file_processing():
    """基本文件处理示例"""
    logger = logging.getLogger('example')
    config = load_example_config()
    
    logger.info("开始文件处理示例...")
    
    # 初始化文件扫描器
    scanner = FileScanner(config)
    
    # 扫描文件
    logger.info("扫描Markdown文件...")
    total_files = scanner.get_file_count()
    logger.info(f"找到 {total_files} 个文件")
    
    # 初始化Markdown加载器
    loader = MDLoader(config)
    
    # 处理文件
    for batch in scanner.scan_files():
        for metadata in batch:
            content = loader.load_file(metadata)
            if content:
                logger.info(f"处理文件: {metadata.filename}")
                logger.info(f"- 章节数: {len(content.sections)}")
                logger.info(f"- 代码块数: {len(content.code_blocks)}")
                logger.info(f"- 链接数: {len(content.links)}")

def text_processing_example():
    """文本处理示例"""
    logger = logging.getLogger('example')
    config = load_example_config()
    
    logger.info("开始文本处理示例...")
    
    # 初始化处理器
    cleaner = TextCleaner(config)
    normalizer = TextNormalizer(config)
    tokenizer = TextTokenizer(config)
    
    # 示例文本
    text = """
    # 示例文档
    
    这是一个**测试**文档，包含一些 HTML <b>标签</b>。
    
    * 项目1
    * 项目2
    
    访问 [示例链接](http://example.com)
    
    ```python
    print("Hello, World!")
    ```
    """
    
    # 清理文本
    logger.info("清理文本...")
    cleaned_text = cleaner.clean_text(text)
    logger.info("清理后的文本:")
    logger.info(cleaned_text)
    
    # 标准化文本
    logger.info("\n标准化文本...")
    normalized_text = normalizer.normalize_text(cleaned_text)
    logger.info("标准化后的文本:")
    logger.info(normalized_text)
    
    # 分词
    logger.info("\n分词处理...")
    tokens = tokenizer.tokenize_text(normalized_text)
    logger.info("分词结果:")
    logger.info(tokens)

def vector_processing_example():
    """向量处理示例"""
    logger = logging.getLogger('example')
    config = load_example_config()
    
    logger.info("开始向量处理示例...")
    
    # 初始化向量化器
    embedder = TextEmbedding(config)
    transformer = VectorTransformer(config)
    
    # 示例文本
    texts = [
        "这是第一个测试文档",
        "这是第二个相关文档",
        "这是一个完全不同的文档"
    ]
    
    # 生成向量
    logger.info("生成文本向量...")
    vectors = embedder.encode_batch(texts)
    logger.info(f"生成向量维度: {vectors.shape}")
    
    # 向量转换
    logger.info("\n执行向量转换...")
    normalized_vectors = transformer.normalize_vectors(vectors)
    logger.info("标准化完成")
    
    # 降维可视化
    logger.info("\n执行降维可视化...")
    vis_vectors = transformer.visualize_vectors(vectors)
    logger.info(f"降维后维度: {vis_vectors.shape}")

def index_search_example():
    """索引和搜索示例"""
    logger = logging.getLogger('example')
    config = load_example_config()
    
    logger.info("开始索引和搜索示例...")
    
    # 初始化组件
    embedder = TextEmbedding(config)
    index_builder = IndexBuilder(config)
    searcher = VectorSearcher(config)
    
    # 示例数据
    texts = [
        "Python是一种流行的编程语言",
        "机器学习是人工智能的一个分支",
        "深度学习在图像识别中表现出色",
        "自然语言处理用于文本分析",
        "数据科学包括数据分析和建模"
    ]
    
    # 生成向量
    logger.info("生成向量...")
    vectors = embedder.encode_batch(texts)
    
    # 构建索引
    logger.info("构建索引...")
    index_builder.create_index(vectors.shape[1])
    index_builder.add_vectors(vectors)
    
    # 设置搜索器
    searcher.set_index(index_builder.index)
    metadata = {i: {'text': text} for i, text in enumerate(texts)}
    searcher.set_metadata(metadata)
    
    # 执行搜索
    logger.info("\n执行搜索...")
    query = "机器学习和人工智能"
    query_vector = embedder.encode_text(query)
    results = searcher.search(query_vector, k=3)
    
    logger.info(f"查询: {query}")
    logger.info("搜索结果:")
    for result in results:
        logger.info(f"- 文档: {result.metadata['text']}")
        logger.info(f"  相似度: {result.score:.4f}")

def storage_example():
    """存储示例"""
    logger = logging.getLogger('example')
    config = load_example_config()
    
    logger.info("开始存储示例...")
    
    # 初始化存储组件
    vector_store = VectorStore(config)
    metadata_manager = MetadataManager(config)
    
    # 示例数据
    vectors = np.random.rand(5, 384).astype('float32')
    metadata = {
        i: {
            'text': f'Document {i}',
            'timestamp': datetime.now().isoformat()
        }
        for i in range(5)
    }
    
    # 存储向量
    logger.info("存储向量...")
    vector_store.store_vectors(vectors)
    
    # 存储元数据
    logger.info("存储元数据...")
    metadata_manager.batch_store_metadata(metadata)
    
    # 检索数据
    logger.info("\n检索数据...")
    loaded_vectors, ids = vector_store.load_vectors()
    logger.info(f"加载的向量数量: {len(loaded_vectors)}")
    
    loaded_metadata = metadata_manager.get_all_metadata()
    logger.info(f"加载的元数据数量: {len(loaded_metadata)}")

def main():
    """主函数"""
    # 设置日志
    logger = setup_example_logger()
    
    try:
        # 运行示例
        with Timer("文件处理示例"):
            basic_file_processing()
        
        with Timer("文本处理示例"):
            text_processing_example()
        
        with Timer("向量处理示例"):
            vector_processing_example()
        
        with Timer("索引搜索示例"):
            index_search_example()
        
        with Timer("存储示例"):
            storage_example()
        
    except Exception as e:
        logger.error(f"运行示例时出错: {e}", exc_info=True)
    
    logger.info("示例运行完成")

if __name__ == "__main__":
    main()