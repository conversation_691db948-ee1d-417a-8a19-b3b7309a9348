
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本标准化器模块
负责文本的标准化处理
"""

import re
import logging
from typing import Dict, Any, List, Optional
import unicodedata
from zhconv import convert  # 用于中文繁简转换

class TextNormalizer:
    """文本标准化器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化文本标准化器
        
        Args:
            config: 配置字典，包含预处理相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 获取配置参数
        self.preprocessing_config = config.get('preprocessing', {})
        self.language = self.preprocessing_config.get('language', 'chinese')
        self.min_length = self.preprocessing_config.get('min_length', 10)
        self.max_length = self.preprocessing_config.get('max_length', 512)
        
        # 编译正则表达式
        self.number_pattern = re.compile(r'\d+')
        self.punctuation_pattern = re.compile(r'[^\w\s]')
        self.whitespace_pattern = re.compile(r'\s+')
        
        # 标点符号映射
        self.punctuation_map = {
            '，': ',',
            '。': '.',
            '！': '!',
            '？': '?',
            '；': ';',
            '：': ':',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '《': '<',
            '》': '>',
            '、': ',',
            '～': '~'
        }
    
    def normalize_chinese(self, text: str) -> str:
        """
        标准化中文文本
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        # 转换为简体中文
        text = convert(text, 'zh-cn')
        
        # 标准化标点符号
        for ch, en in self.punctuation_map.items():
            text = text.replace(ch, en)
        
        return text
    
    def normalize_case(self, text: str) -> str:
        """
        标准化大小写
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        if self.preprocessing_config.get('lowercase', True):
            return text.lower()
        return text
    
    def normalize_numbers(self, text: str) -> str:
        """
        标准化数字
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        number_handling = self.preprocessing_config.get('number_handling', 'keep')
        
        if number_handling == 'remove':
            # 完全移除数字
            return self.number_pattern.sub('', text)
        elif number_handling == 'replace':
            # 用特殊标记替换数字
            return self.number_pattern.sub('<NUM>', text)
        else:
            # 保持原样
            return text
    
    def normalize_punctuation(self, text: str) -> str:
        """
        标准化标点符号
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        punctuation_handling = self.preprocessing_config.get('punctuation_handling', 'normalize')
        
        if punctuation_handling == 'remove':
            # 完全移除标点符号
            return self.punctuation_pattern.sub('', text)
        elif punctuation_handling == 'normalize':
            # 标准化标点符号
            text = unicodedata.normalize('NFKC', text)
            for old, new in self.punctuation_map.items():
                text = text.replace(old, new)
            return text
        else:
            # 保持原样
            return text
    
    def normalize_whitespace(self, text: str) -> str:
        """
        标准化空白字符
        
        Args:
            text: 输入文本
            
        Returns:
            str: 标准化后的文本
        """
        # 将多个空白字符替换为单个空格
        return self.whitespace_pattern.sub(' ', text).strip()
    
    def truncate_text(self, text: str) -> str:
        """
        截断文本到指定长度
        
        Args:
            text: 输入文本
            
        Returns:
            str: 截断后的文本
        """
        if len(text) > self.max_length:
            # 在单词边界截断
            truncated = text[:self.max_length]
            last_space = truncated.rfind(' ')
            if last_space > 0:
                truncated = truncated[:last_space]
            return truncated
        return text
    
    def is_valid_length(self, text: str) -> bool:
        """
        检查文本长度是否有效
        
        Args:
            text: 输入文本
            
        Returns:
            bool: 文本长度是否在有效范围内
        """
        text_length = len(text)
        return self.min_length <= text_length <= self.max_length
    
    def normalize_text(self, text: str, **options) -> Optional[str]:
        """
        标准化文本
        
        Args:
            text: 输入文本
            **options: 标准化选项，可以覆盖配置文件中的设置
            
        Returns:
            Optional[str]: 标准化后的文本，如果文本无效则返回None
        """
        if not text:
            return None
            
        try:
            # 中文文本处理
            if self.language == 'chinese':
                text = self.normalize_chinese(text)
            
            # 大小写标准化
            text = self.normalize_case(text)
            
            # 数字标准化
            text = self.normalize_numbers(text)
            
            # 标点符号标准化
            text = self.normalize_punctuation(text)
            
            # 空白字符标准化
            text = self.normalize_whitespace(text)
            
            # 截断文本
            text = self.truncate_text(text)
            
            # 检查文本长度
            if not self.is_valid_length(text):
                return None
            
            return text
            
        except Exception as e:
            self.logger.error(f"标准化文本时出错: {e}")
            return None
    
    def normalize_batch(self, texts: List[str], **options) -> List[str]:
        """
        批量标准化文本
        
        Args:
            texts: 文本列表
            **options: 标准化选项
            
        Returns:
            List[str]: 标准化后的文本列表（过滤掉了None的结果）
        """
        normalized_texts = []
        for text in texts:
            normalized = self.normalize_text(text, **options)
            if normalized is not None:
                normalized_texts.append(normalized)
        return normalized_texts

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'preprocessing': {
            'language': 'chinese',
            'lowercase': True,
            'min_length': 10,
            'max_length': 512,
            'number_handling': 'keep',
            'punctuation_handling': 'normalize'
        }
    }
    
    normalizer = TextNormalizer(test_config)
    
    test_texts = [
        "这是一个测试文本！123",
        "這是繁體中文測試文本。",
        "This is English Text.",
        "混合Chinese和English的Text。",
        "数字123和标点符号！？。，",
        "   多个空格   和换行符   ",
        "a"  # 太短的文本
    ]
    
    print("标准化结果:")
    for text in test_texts:
        normalized = normalizer.normalize_text(text)
        print(f"原文: {text}")
        print(f"标准化: {normalized}")
        print()