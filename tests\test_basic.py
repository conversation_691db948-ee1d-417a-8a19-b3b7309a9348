
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基本功能测试
测试MD Vector Processor的核心功能
"""

import pytest
import sys
from pathlib import Path
import numpy as np
import tempfile
import shutil
import yaml
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.data_loader import FileScanner, MDLoader
from src.preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from src.vectorizer import TextEmbedding, VectorTransformer
from src.indexer import IndexBuilder, VectorSearcher
from src.storage import VectorStore, MetadataManager
from src.utils import setup_logger, ConfigUtils

@pytest.fixture
def test_config():
    """测试配置fixture"""
    config = {
        'file_processing': {
            'input_dir': 'test_data',
            'output_dir': 'test_output',
            'supported_extensions': ['.md'],
            'batch_size': 2
        },
        'preprocessing': {
            'language': 'chinese',
            'remove_html_tags': True,
            'normalize_whitespace': True
        },
        'vectorization': {
            'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
            'vector_dimension': 384,
            'device': 'cpu'
        },
        'indexing': {
            'index_type': 'faiss',
            'metric': 'cosine'
        },
        'storage': {
            'base_dir': 'test_storage',
            'compression': True
        },
        'logging': {
            'log_level': 'DEBUG',
            'console_log': True
        }
    }
    return config

@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)

@pytest.fixture
def test_markdown_file(temp_dir):
    """创建测试Markdown文件"""
    content = """# Test Document
    
    This is a **test** document with some basic Markdown features.
    
    * List item 1
    * List item 2
    
    ```python
    print("Hello, World!")
    ```
    
    [Link](http://example.com)
    """
    
    file_path = temp_dir / "test.md"
    file_path.write_text(content)
    return file_path

class TestFileScanner:
    """文件扫描器测试"""
    
    def test_file_scanning(self, test_config, temp_dir, test_markdown_file):
        """测试文件扫描功能"""
        # 更新配置中的输入目录
        test_config['file_processing']['input_dir'] = str(temp_dir)
        
        # 创建扫描器
        scanner = FileScanner(test_config)
        
        # 测试文件计数
        assert scanner.get_file_count() == 1
        
        # 测试文件扫描
        batches = list(scanner.scan_files())
        assert len(batches) == 1
        assert len(batches[0]) == 1
        assert batches[0][0].extension == '.md'

class TestMDLoader:
    """Markdown加载器测试"""
    
    def test_markdown_loading(self, test_config, test_markdown_file):
        """测试Markdown文件加载功能"""
        loader = MDLoader(test_config)
        
        # 创建文件元数据
        metadata = FileMetadata.from_path(test_markdown_file, test_markdown_file.parent)
        
        # 加载文件
        content = loader.load_file(metadata)
        
        assert content is not None
        assert len(content.sections) > 0
        assert len(content.code_blocks) == 1
        assert len(content.links) == 1

class TestTextProcessing:
    """文本处理测试"""
    
    def test_text_cleaning(self, test_config):
        """测试文本清理功能"""
        cleaner = TextCleaner(test_config)
        
        test_text = """<p>This is a test</p>
        
        with   multiple   spaces"""
        
        cleaned = cleaner.clean_text(test_text)
        
        assert '<p>' not in cleaned
        assert '   ' not in cleaned
    
    def test_text_normalization(self, test_config):
        """测试文本标准化功能"""
        normalizer = TextNormalizer(test_config)
        
        test_text = "This is A TEST"
        normalized = normalizer.normalize_text(test_text)
        
        assert normalized.islower()
    
    def test_tokenization(self, test_config):
        """测试分词功能"""
        tokenizer = TextTokenizer(test_config)
        
        test_text = "这是一个测试文本"
        tokens = tokenizer.tokenize_text(test_text)
        
        assert len(tokens) > 0
        assert isinstance(tokens, list)
        assert all(isinstance(token, str) for token in tokens)

class TestVectorization:
    """向量化测试"""
    
    def test_text_embedding(self, test_config):
        """测试文本嵌入功能"""
        embedder = TextEmbedding(test_config)
        
        test_text = "这是测试文本"
        vector = embedder.encode_text(test_text)
        
        assert isinstance(vector, np.ndarray)
        assert vector.shape == (embedder.vector_dimension,)
    
    def test_vector_transformation(self, test_config):
        """测试向量转换功能"""
        transformer = VectorTransformer(test_config)
        
        test_vectors = np.random.rand(5, 384)
        normalized = transformer.normalize_vectors(test_vectors)
        
        assert normalized.shape == test_vectors.shape
        assert np.allclose(np.linalg.norm(normalized, axis=1), 1.0)

class TestIndexing:
    """索引测试"""
    
    def test_index_building(self, test_config):
        """测试索引构建功能"""
        builder = IndexBuilder(test_config)
        
        # 创建测试向量
        vectors = np.random.rand(10, 384).astype('float32')
        
        # 构建索引
        builder.create_index(384)
        success = builder.add_vectors(vectors)
        
        assert success
        assert builder.total_vectors == 10
    
    def test_vector_searching(self, test_config):
        """测试向量搜索功能"""
        builder = IndexBuilder(test_config)
        searcher = VectorSearcher(test_config)
        
        # 创建测试向量
        vectors = np.random.rand(10, 384).astype('float32')
        
        # 构建索引
        builder.create_index(384)
        builder.add_vectors(vectors)
        
        # 设置搜索器
        searcher.set_index(builder.index)
        
        # 执行搜索
        query_vector = np.random.rand(384).astype('float32')
        results = searcher.search(query_vector, k=5)
        
        assert len(results) == 5
        assert all(0 <= result.score <= 1 for result in results)

class TestStorage:
    """存储测试"""
    
    def test_vector_storage(self, test_config, temp_dir):
        """测试向量存储功能"""
        # 更新存储目录
        test_config['storage']['base_dir'] = str(temp_dir)
        store = VectorStore(test_config)
        
        # 创建测试向量
        vectors = np.random.rand(5, 384).astype('float32')
        
        # 存储向量
        success = store.store_vectors(vectors)
        assert success
        
        # 加载向量
        loaded_vectors, ids = store.load_vectors()
        assert loaded_vectors is not None
        assert np.allclose(vectors, loaded_vectors)
    
    def test_metadata_storage(self, test_config, temp_dir):
        """测试元数据存储功能"""
        # 更新存储目录
        test_config['storage']['base_dir'] = str(temp_dir)
        manager = MetadataManager(test_config)
        
        # 创建测试元数据
        metadata = {
            1: {'text': 'Test 1', 'timestamp': datetime.now().isoformat()},
            2: {'text': 'Test 2', 'timestamp': datetime.now().isoformat()}
        }
        
        # 存储元数据
        success = manager.batch_store_metadata(metadata)
        assert success
        
        # 检索元数据
        loaded_metadata = manager.get_metadata(1)
        assert loaded_metadata is not None
        assert loaded_metadata['text'] == 'Test 1'

def test_end_to_end(test_config, temp_dir, test_markdown_file):
    """端到端测试"""
    # 更新配置
    test_config['file_processing']['input_dir'] = str(temp_dir)
    test_config['storage']['base_dir'] = str(temp_dir / 'storage')
    
    try:
        # 1. 扫描文件
        scanner = FileScanner(test_config)
        loader = MDLoader(test_config)
        
        # 2. 处理文本
        cleaner = TextCleaner(test_config)
        normalizer = TextNormalizer(test_config)
        tokenizer = TextTokenizer(test_config)
        
        # 3. 向量化
        embedder = TextEmbedding(test_config)
        
        # 4. 构建索引
        builder = IndexBuilder(test_config)
        searcher = VectorSearcher(test_config)
        
        # 5. 存储
        vector_store = VectorStore(test_config)
        metadata_manager = MetadataManager(test_config)
        
        # 执行处理流程
        for batch in scanner.scan_files():
            for metadata in batch:
                # 加载和处理文本
                content = loader.load_file(metadata)
                cleaned_text = cleaner.clean_text(content.text_content)
                normalized_text = normalizer.normalize_text(cleaned_text)
                tokens = tokenizer.tokenize_text(normalized_text)
                
                # 生成向量
                vector = embedder.encode_text(' '.join(tokens))
                
                # 存储数据
                vector_store.store_vectors(vector.reshape(1, -1))
                metadata_manager.store_metadata(0, {
                    'filename': metadata.filename,
                    'tokens': tokens
                })
        
        assert True  # 如果执行到这里没有异常，则测试通过
        
    except Exception as e:
        pytest.fail(f"端到端测试失败: {e}")

if __name__ == '__main__':
    pytest.main([__file__])