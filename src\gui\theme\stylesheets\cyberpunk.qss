/* 赛博朋克主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #0a0a0a;
}

QLabel {
    color: #00ffff;
}

QPushButton {
    background-color: #ff00ff;
    color: #000000;
    border: 2px solid #00ffff;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #00ffff;
    color: #000000;
    border: 2px solid #ff00ff;
}

QPushButton:pressed {
    background-color: #00cccc;
    color: #000000;
    border: 2px solid #cc00cc;
}

QPushButton:disabled {
    background-color: #333333;
    color: #666666;
    border: 2px solid #444444;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #1a1a1a;
    color: #00ffff;
    border: 1px solid #00ffff;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 2px solid #ff00ff;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #1a1a1a;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #00ffff;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #1a1a1a;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #00ffff;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #1a1a1a;
    color: #00ffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #333333;
}

QMenu {
    background-color: #1a1a1a;
    color: #00ffff;
    border: 1px solid #00ffff;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #333333;
}

QTabWidget::pane {
    border: 1px solid #00ffff;
    background-color: #1a1a1a;
}

QTabBar::tab {
    background-color: #0a0a0a;
    color: #00ffff;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #1a1a1a;
    color: #ff00ff;
}

QStatusBar {
    background-color: #1a1a1a;
    color: #00ffff;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #ff00ff;
}

#sidebarWidget {
    background-color: #1a1a1a;
    min-width: 200px;
    max-width: 200px;
    border-right: 2px solid #00ffff;
}

#contentWidget {
    background-color: #0a0a0a;
}

#dashboardWidget {
    background-color: #0a0a0a;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #1a1a1a;
    border: 2px solid #00ffff;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #ff00ff;
    margin-bottom: 8px;
}

.card-content {
    color: #00ffff;
}

/* 科技感元素 */
.tech-border {
    border: 2px solid #00ffff;
    border-radius: 4px;
}

.glow-effect {
    border: 2px solid #00ffff;
    box-shadow: 0 0 20px #00ffff;
}
