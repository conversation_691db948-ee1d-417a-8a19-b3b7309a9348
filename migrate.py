
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据迁移脚本
用于处理版本升级时的数据迁移
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import json
import sqlite3
import h5py
import numpy as np
from datetime import datetime
import yaml
from tqdm import tqdm

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('migration.log')
        ]
    )
    return logging.getLogger(__name__)

class DataMigration:
    """数据迁移类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化迁移器"""
        self.logger = setup_logging()
        self.config = self._load_config(config_path)
        self.migrations = []
        self._register_migrations()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置"""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def _register_migrations(self):
        """注册迁移操作"""
        # 在这里注册所有迁移操作
        self.migrations = [
            {
                'version': '1.0.0',
                'description': '初始数据库结构',
                'function': self._migrate_v1_0_0
            },
            {
                'version': '1.1.0',
                'description': '添加向量压缩支持',
                'function': self._migrate_v1_1_0
            },
            {
                'version': '1.2.0',
                'description': '更新元数据结构',
                'function': self._migrate_v1_2_0
            }
        ]
    
    def _get_current_version(self) -> str:
        """获取当前数据版本"""
        try:
            conn = sqlite3.connect('data/metadata.db')
            cursor = conn.cursor()
            cursor.execute("PRAGMA user_version")
            version = cursor.fetchone()[0]
            conn.close()
            
            # 转换数据库版本号为语义化版本
            major = version // 10000
            minor = (version % 10000) // 100
            patch = version % 100
            
            return f"{major}.{minor}.{patch}"
            
        except:
            return '0.0.0'
    
    def _set_version(self, version: str):
        """设置数据库版本"""
        # 转换语义化版本为数据库版本号
        major, minor, patch = map(int, version.split('.'))
        version_num = major * 10000 + minor * 100 + patch
        
        conn = sqlite3.connect('data/metadata.db')
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA user_version = {version_num}")
        conn.commit()
        conn.close()
    
    def _migrate_v1_0_0(self):
        """迁移到1.0.0版本"""
        conn = sqlite3.connect('data/metadata.db')
        cursor = conn.cursor()
        
        # 创建元数据表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS metadata (
            id INTEGER PRIMARY KEY,
            data TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            version INTEGER DEFAULT 1,
            checksum TEXT
        )
        """)
        
        # 创建索引表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS metadata_index (
            id INTEGER,
            key TEXT,
            value TEXT,
            FOREIGN KEY (id) REFERENCES metadata(id),
            UNIQUE (id, key)
        )
        """)
        
        conn.commit()
        conn.close()
    
    def _migrate_v1_1_0(self):
        """迁移到1.1.0版本"""
        # 更新向量存储格式
        vector_file = Path('data/vectors/vectors.h5')
        if vector_file.exists():
            # 读取旧格式数据
            with h5py.File(vector_file, 'r') as f:
                vectors = f['vectors'][:]
            
            # 压缩向量
            compressed_vectors = self._compress_vectors(vectors)
            
            # 保存新格式数据
            with h5py.File(vector_file, 'w') as f:
                f.create_dataset('vectors_compressed', data=compressed_vectors)
                f.attrs['compression'] = True
    
    def _migrate_v1_2_0(self):
        """迁移到1.2.0版本"""
        conn = sqlite3.connect('data/metadata.db')
        cursor = conn.cursor()
        
        # 添加新字段
        cursor.execute("""
        ALTER TABLE metadata ADD COLUMN tags TEXT;
        ALTER TABLE metadata ADD COLUMN embedding_model TEXT;
        """)
        
        # 更新现有记录
        cursor.execute("UPDATE metadata SET tags = '[]', embedding_model = 'default'")
        
        conn.commit()
        conn.close()
    
    def _compress_vectors(self, vectors: np.ndarray) -> np.ndarray:
        """压缩向量数据"""
        # 实现向量压缩逻辑
        return vectors  # 示例中直接返回原数据
    
    def _backup_database(self):
        """备份数据库"""
        src = Path('data/metadata.db')
        if src.exists():
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            dst = Path(f'data/metadata_{timestamp}.db.bak')
            src.rename(dst)
            self.logger.info(f"数据库已备份到: {dst}")
    
    def run_migrations(self, target_version: str = None):
        """运行数据迁移"""
        current_version = self._get_current_version()
        self.logger.info(f"当前版本: {current_version}")
        
        if target_version is None:
            target_version = self.migrations[-1]['version']
        
        self.logger.info(f"目标版本: {target_version}")
        
        # 确定需要运行的迁移
        migrations_to_run = []
        for migration in self.migrations:
            if migration['version'] > current_version and migration['version'] <= target_version:
                migrations_to_run.append(migration)
        
        if not migrations_to_run:
            self.logger.info("没有需要运行的迁移")
            return
        
        # 备份数据库
        self._backup_database()
        
        # 运行迁移
        for migration in migrations_to_run:
            try:
                self.logger.info(f"运行迁移 {migration['version']}: {migration['description']}")
                migration['function']()
                self._set_version(migration['version'])
                self.logger.info(f"迁移 {migration['version']} 完成")
                
            except Exception as e:
                self.logger.error(f"迁移 {migration['version']} 失败: {e}")
                raise
        
        self.logger.info("所有迁移完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor数据迁移工具')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--target-version', type=str,
                       help='目标版本')
    args = parser.parse_args()
    
    try:
        migration = DataMigration(args.config)
        migration.run_migrations(args.target_version)
        
    except Exception as e:
        logging.error(f"迁移失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()