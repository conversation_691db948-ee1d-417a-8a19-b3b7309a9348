
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API服务模块
提供RESTful API接口
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Security
from fastapi.security.api_key import APIKeyHeader, APIKey
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import numpy as np

from .utils import setup_logger, ConfigUtils, Timer
from .data_loader import MDLoader
from .preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from .vectorizer import TextEmbedding
from .indexer import IndexBuilder, VectorSearcher
from .storage import VectorStore, MetadataManager

# API模型
class ProcessRequest(BaseModel):
    """处理请求模型"""
    text: str = Field(..., description="要处理的文本")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询")
    top_k: int = Field(default=5, description="返回结果数量")
    min_score: float = Field(default=0.5, description="最小相似度分数")

class ProcessResponse(BaseModel):
    """处理响应模型"""
    id: str = Field(..., description="文档ID")
    vector: List[float] = Field(..., description="文档向量")
    metadata: Dict[str, Any] = Field(..., description="文档元数据")

class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[Dict[str, Any]] = Field(..., description="搜索结果")
    total: int = Field(..., description="总结果数")
    query_time: float = Field(..., description="查询时间（秒）")

# API应用
app = FastAPI(
    title="MD Vector Processor API",
    description="Markdown文档向量化处理API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API密钥认证
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)

async def get_api_key(api_key_header: str = Security(api_key_header)) -> str:
    """验证API密钥"""
    if api_key_header == app.state.config.get('api_key'):
        return api_key_header
    raise HTTPException(
        status_code=403,
        detail="Could not validate API key"
    )

# 全局组件
def init_components(config: Dict[str, Any]):
    """初始化组件"""
    app.state.cleaner = TextCleaner(config)
    app.state.normalizer = TextNormalizer(config)
    app.state.tokenizer = TextTokenizer(config)
    app.state.embedder = TextEmbedding(config)
    app.state.vector_store = VectorStore(config)
    app.state.metadata_manager = MetadataManager(config)
    app.state.index_builder = IndexBuilder(config)
    app.state.searcher = VectorSearcher(config)

# API路由
@app.post("/process", response_model=ProcessResponse)
async def process_document(
    request: ProcessRequest,
    api_key: APIKey = Depends(get_api_key)
) -> ProcessResponse:
    """处理文档"""
    try:
        with Timer() as timer:
            # 清理和标准化文本
            cleaned_text = app.state.cleaner.clean_text(request.text)
            normalized_text = app.state.normalizer.normalize_text(cleaned_text)
            tokens = app.state.tokenizer.tokenize_text(normalized_text)

            # 生成向量
            vector = app.state.embedder.encode_text(' '.join(tokens))

            # 存储数据
            doc_id = str(hash(request.text))
            app.state.vector_store.store_vectors(vector.reshape(1, -1))

            metadata = request.metadata or {}
            metadata.update({
                'tokens': tokens,
                'processing_time': timer.elapsed
            })
            app.state.metadata_manager.store_metadata(doc_id, metadata)

            return ProcessResponse(
                id=doc_id,
                vector=vector.tolist(),
                metadata=metadata
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"处理文档时出错: {str(e)}"
        )

@app.post("/search", response_model=SearchResponse)
async def search_documents(
    request: SearchRequest,
    api_key: APIKey = Depends(get_api_key)
) -> SearchResponse:
    """搜索文档"""
    try:
        with Timer() as timer:
            # 生成查询向量
            query_vector = app.state.embedder.encode_text(request.query)

            # 执行搜索
            results = app.state.searcher.search(
                query_vector,
                k=request.top_k,
                min_similarity=request.min_score
            )

            # 格式化结果
            formatted_results = []
            for result in results:
                metadata = app.state.metadata_manager.get_metadata(result.id)
                formatted_results.append({
                    'id': result.id,
                    'score': result.score,
                    'metadata': metadata
                })

            return SearchResponse(
                results=formatted_results,
                total=len(results),
                query_time=timer.elapsed
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"搜索文档时出错: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

def start_api(config_path: str = "config/config.yaml", host: str = "0.0.0.0",
             port: int = 8000, reload: bool = False):
    """启动API服务"""
    # 加载主配置
    config = ConfigUtils.load_config(config_path)
    if not config:
        raise ValueError(f"无法加载配置文件: {config_path}")

    # 加载models.yaml配置（如果存在）
    models_config_path = Path("config/models.yaml")
    if models_config_path.exists():
        models_config = ConfigUtils.load_config(models_config_path)
        if models_config:
            # 合并配置
            config.update(models_config)
            logging.info("已加载models.yaml配置")

    # 设置日志
    setup_logger(config, 'api')

    # 初始化组件
    init_components(config)

    # 存储配置
    app.state.config = config

    # 启动服务
    uvicorn.run(
        "md_vector_processor.api:app",
        host=host,
        port=port,
        reload=reload
    )

if __name__ == "__main__":
    start_api()