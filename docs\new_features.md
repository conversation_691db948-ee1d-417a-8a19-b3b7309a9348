# MD Vector Processor 新功能文档

本文档介绍了MD Vector Processor的新增功能和优化，包括向量化模型升级、索引性能提升和多语言支持增强。

## 目录

- [向量化模型升级](#向量化模型升级)
  - [多模型支持](#多模型支持)
  - [领域特化模型](#领域特化模型)
  - [自定义微调](#自定义微调)
- [索引性能提升](#索引性能提升)
  - [混合索引](#混合索引)
  - [量化技术](#量化技术)
  - [增量索引](#增量索引)
- [多语言支持增强](#多语言支持增强)
  - [低资源语言](#低资源语言)
  - [代码块处理](#代码块处理)
  - [多语言混合文档](#多语言混合文档)
- [配置选项](#配置选项)
- [性能优化](#性能优化)
- [GPU加速](#gpu加速)
- [测试新功能](#测试新功能)

## 向量化模型升级

### 多模型支持

MD Vector Processor现在支持多种先进的语言模型，包括：

- **BERT-MRC**：针对机器阅读理解优化的BERT模型
- **RoBERTa**：优化的BERT变体，具有更好的性能
- **XLNet**：基于Transformer-XL的自回归预训练模型
- **ERNIE**：百度开发的知识增强语言表示模型

使用方法：

```yaml
# 在config.yaml中配置
vectorization:
  model_name: "bert-mrc"  # 或 "roberta", "xlnet", "ernie"
```

或者使用预定义的模型名称：

```yaml
vectorization:
  model_name: "multilingual-minilm"  # 预定义的多语言模型
```

### 领域特化模型

新增了针对特定领域的预训练模型支持：

- **医疗领域**：针对医疗文本优化的模型
- **法律领域**：针对法律文档优化的模型
- **技术文档**：针对技术文档优化的模型
- **代码领域**：针对代码文本优化的模型

使用方法：

```yaml
vectorization:
  domain: "medical"  # 或 "legal", "technical", "code"
```

### 自定义微调

新增了模型微调接口，允许用户使用自己的数据集微调模型：

```python
from src.vectorizer.embeddings import TextEmbedding

# 初始化嵌入器
embedder = TextEmbedding(config)

# 准备训练数据
texts = ["训练文本1", "训练文本2", ...]
labels = [0, 1, ...]  # 可选，用于有监督微调

# 微调模型
embedder.fine_tune(texts, labels, save_path="models/fine_tuned", epochs=3)

# 导出模型
embedder.export_model("models/exported_model")
```

配置选项：

```yaml
vectorization:
  fine_tuning:
    enabled: true
    data_path: "data/training_data.txt"  # 训练数据路径
    save_path: "models/fine_tuned"
    epochs: 3
    learning_rate: 2e-5
    batch_size: 16
```

## 索引性能提升

### 混合索引

实现了HNSW+IVF混合索引，提高检索效率：

```yaml
indexing:
  index_type: "hybrid"
  hybrid_mode: "hnsw_ivf"  # 或 "ivf_hnsw"
  hybrid_n_lists: 50
  hybrid_n_probes: 5
```

### 量化技术

添加了多种向量压缩方法：

- **PQ (Product Quantization)**：乘积量化
- **OPQ (Optimized Product Quantization)**：优化的乘积量化
- **ScaNN**：Google的向量搜索库

```yaml
indexing:
  quantization: "pq"  # 或 "opq", "scann", "none"
  pq_m: 8              # PQ子空间数量
  pq_bits: 8           # 每个子空间的位数
  opq_m: 8             # OPQ子空间数量
  opq_bits: 8          # 每个子空间的位数
```

### 增量索引

优化了增量更新机制，避免全量重建：

```yaml
indexing:
  incremental_index: true
  rebuild_threshold: 1000  # 增量更新多少次后重建索引
```

## 多语言支持增强

### 低资源语言

扩展了对小语种的支持：

- 日语、韩语、俄语、西班牙语、法语、德语
- 泰语、越南语、印地语、土耳其语、印尼语、马来语

```yaml
preprocessing:
  languages:
    enabled: ["chinese", "english", "japanese", "korean", "russian", "spanish", "french", "german"]
    auto_detect: true
    fallback: "english"
  
  low_resource_languages:
    enabled: true
    languages: ["thai", "vietnamese", "hindi", "turkish", "indonesian", "malay"]
    use_specialized_tokenizers: true
```

### 代码块处理

添加了专门处理Markdown中代码块的功能：

```yaml
preprocessing:
  code_blocks:
    enabled: true
    languages: ["python", "java", "javascript", "c", "cpp", "csharp", "go", "rust", "php", "ruby"]
    special_model: "code"  # 使用代码特化模型处理代码块
```

### 多语言混合文档

实现了智能识别文档中的多语言部分并分别处理：

```yaml
preprocessing:
  language: "auto"  # 自动检测语言
```

## 配置选项

完整的新配置选项示例：

```yaml
# 向量化配置
vectorization:
  # 模型设置
  model_name: "multilingual-minilm"  # 预定义模型名称或HuggingFace模型ID
  domain: null  # 可选: 'medical', 'legal', 'technical', 'code'
  vector_dimension: 384
  batch_size: 32
  device: "cuda"  # 'cuda' or 'cpu'
  
  # 高级模型设置
  model_cache_dir: "models"
  custom_model_path: null  # 自定义模型路径
  pooling_method: "mean_pooling"  # 'mean_pooling', 'cls_pooling', 'max_pooling', 'last_pooling'
  
  # 向量处理
  normalize_vectors: true
  compression_method: "pq"  # 'pq', 'opq', 'scann', 'svd'
  reduced_dimension: 128
  
  # 微调设置
  fine_tuning:
    enabled: false
    data_path: null  # 训练数据路径
    save_path: "models/fine_tuned"
    epochs: 3
    learning_rate: 2e-5
    batch_size: 16

# 索引配置
indexing:
  # 索引类型
  index_type: "hybrid"  # 'flat', 'ivf', 'hnsw', 'hybrid'
  metric: "cosine"     # 'cosine', 'l2', 'ip'
  
  # 混合索引参数
  hybrid_mode: "hnsw_ivf"  # 'hnsw_ivf', 'ivf_hnsw'
  hybrid_n_lists: 50
  hybrid_n_probes: 5
  
  # 量化参数
  quantization: "pq"   # 'pq', 'opq', 'sq', 'none'
  pq_m: 8              # PQ子空间数量
  pq_bits: 8           # 每个子空间的位数
  
  # 增量索引设置
  incremental_index: true
  rebuild_threshold: 1000  # 增量更新多少次后重建索引
  
  # GPU加速
  use_gpu_index: false  # 是否使用GPU加速索引

# 预处理配置
preprocessing:
  # 语言处理
  language: "auto"  # 'chinese', 'english', 'multilingual', 'auto'
  
  # 多语言支持
  languages:
    enabled: ["chinese", "english", "japanese", "korean", "russian", "spanish", "french", "german"]
    auto_detect: true
    fallback: "english"
  
  # 代码处理
  code_blocks:
    enabled: true
    languages: ["python", "java", "javascript", "c", "cpp", "csharp", "go", "rust", "php", "ruby"]
    special_model: "code"  # 使用代码特化模型处理代码块
  
  # 小语种支持
  low_resource_languages:
    enabled: true
    languages: ["thai", "vietnamese", "hindi", "turkish", "indonesian", "malay"]
    use_specialized_tokenizers: true
```

## 性能优化

我们提供了性能优化脚本，用于优化混合索引和量化技术的性能：

```bash
python scripts/optimize_performance.py --action all
```

该脚本会：
1. 对不同索引类型和量化方法进行基准测试
2. 优化混合索引参数
3. 生成优化后的配置文件

## GPU加速

我们提供了GPU加速优化脚本，用于提高向量化和索引构建的速度：

```bash
python scripts/gpu_optimization.py --action all
```

该脚本会：
1. 检查GPU可用性
2. 对比GPU和CPU性能
3. 优化GPU配置

## 测试新功能

我们提供了测试脚本，用于测试新添加的功能：

```bash
python tests/test_new_features.py --test all
```

该脚本会测试：
1. 多语言支持
2. 代码块处理
3. 模型注册表和多模型支持
4. 混合索引和量化技术

## 依赖项

新功能需要安装以下依赖项：

```bash
pip install -r requirements.txt
```

主要新增依赖：
- langdetect：语言检测
- pygments：代码语法高亮
- mecab-python3：日语分词
- konlpy：韩语分词
- pythainlp：泰语分词
- pyarabic：阿拉伯语处理
- pymorphy2：俄语形态分析
- scann：Google的向量搜索库
