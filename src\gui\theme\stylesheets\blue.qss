/* 蓝色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #E3F2FD;
}

QLabel {
    color: #0D47A1;
}

QPushButton {
    background-color: #1976D2;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1565C0;
}

QPushButton:pressed {
    background-color: #0D47A1;
}

QPushButton:disabled {
    background-color: #BBDEFB;
    color: #90CAF9;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #0D47A1;
    border: 1px solid #90CAF9;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #1976D2;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #E3F2FD;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #90CAF9;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #E3F2FD;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #90CAF9;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #1976D2;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #1565C0;
}

QMenu {
    background-color: #FFFFFF;
    color: #0D47A1;
    border: 1px solid #90CAF9;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #E3F2FD;
}

QTabWidget::pane {
    border: 1px solid #90CAF9;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #BBDEFB;
    color: #0D47A1;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #1976D2;
}

QStatusBar {
    background-color: #1976D2;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #1976D2;
}

#sidebarWidget {
    background-color: #1976D2;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #E3F2FD;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #90CAF9;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #1976D2;
    margin-bottom: 8px;
}

.card-content {
    color: #0D47A1;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #1976D2;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #1976D2;
    box-shadow: 0 0 10px #1976D2;
}
