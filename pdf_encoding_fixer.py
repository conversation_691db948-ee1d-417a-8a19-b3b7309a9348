#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF编码问题修复脚本
"""

import PyPDF2
import fitz  # pymupdf
from pathlib import Path
import logging

def fix_pdf_encoding_issues(pdf_path: Path) -> str:
    """修复PDF编码问题"""
    try:
        # 方法1: 使用pymupdf处理编码问题
        doc = fitz.open(pdf_path)
        text_parts = []
        
        for page_num in range(min(5, doc.page_count)):  # 只处理前5页
            page = doc[page_num]
            text = page.get_text()
            
            # 清理问题字符
            cleaned_text = ""
            for char in text:
                try:
                    char.encode('utf-8')
                    cleaned_text += char
                except UnicodeEncodeError:
                    cleaned_text += '?'  # 替换问题字符
            
            text_parts.append(cleaned_text)
        
        doc.close()
        return '\n'.join(text_parts)
        
    except Exception as e:
        logging.warning(f"PDF编码修复失败 {pdf_path}: {e}")
        return ""

# 使用示例
# fixed_text = fix_pdf_encoding_issues(Path("problem_file.pdf"))
