#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
搜索诊断工具

用于诊断和修复搜索功能中的问题，特别是索引ID和元数据ID映射问题
"""

import os
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SearchDiagnostics:
    """搜索诊断工具"""
    
    def __init__(self, config: Dict):
        """
        初始化搜索诊断工具
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def diagnose_search_issues(self, index_path: str) -> Dict:
        """
        诊断搜索问题
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            Dict: 诊断结果
        """
        self.logger.info(f"开始诊断搜索问题: {index_path}")
        
        diagnosis = {
            'index_status': {},
            'metadata_status': {},
            'vector_storage_status': {},
            'mapping_issues': [],
            'recommendations': []
        }
        
        try:
            # 1. 检查索引状态
            diagnosis['index_status'] = self._check_index_status(index_path)
            
            # 2. 检查元数据状态
            diagnosis['metadata_status'] = self._check_metadata_status()
            
            # 3. 检查向量存储状态
            diagnosis['vector_storage_status'] = self._check_vector_storage_status()
            
            # 4. 检查映射一致性
            mapping_issues = self._check_mapping_consistency(
                diagnosis['index_status'],
                diagnosis['metadata_status'],
                diagnosis['vector_storage_status']
            )
            diagnosis['mapping_issues'] = mapping_issues
            
            # 5. 生成修复建议
            diagnosis['recommendations'] = self._generate_recommendations(diagnosis)
            
        except Exception as e:
            self.logger.error(f"诊断过程中出错: {e}")
            diagnosis['mapping_issues'].append(f"诊断过程中出错: {str(e)}")
        
        return diagnosis
    
    def _check_index_status(self, index_path: str) -> Dict:
        """检查索引状态"""
        status = {
            'exists': False,
            'loadable': False,
            'vector_count': 0,
            'dimension': 0,
            'index_type': 'unknown',
            'metric': 'unknown'
        }
        
        try:
            if not os.path.exists(index_path):
                return status
            
            status['exists'] = True
            
            # 尝试加载索引
            from ..indexer import IndexBuilder
            config = {'indexing': {'index_type': 'flat', 'metric': 'cosine'}}
            builder = IndexBuilder(config)
            
            if builder.load_index(index_path):
                status['loadable'] = True
                status['vector_count'] = builder.total_vectors
                
                if hasattr(builder, 'index') and builder.index:
                    status['dimension'] = builder.index.d
                    status['index_type'] = type(builder.index).__name__
                    
                    # 检查度量类型
                    if hasattr(builder, 'metric'):
                        status['metric'] = builder.metric
                
                self.logger.info(f"索引状态: {status}")
            
        except Exception as e:
            self.logger.error(f"检查索引状态时出错: {e}")
            status['error'] = str(e)
        
        return status
    
    def _check_metadata_status(self) -> Dict:
        """检查元数据状态"""
        status = {
            'accessible': False,
            'total_records': 0,
            'sample_ids': [],
            'sample_metadata': {}
        }
        
        try:
            from ..storage import MetadataManager
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            metadata_manager = MetadataManager(storage_config)
            
            # 获取所有元数据
            all_metadata = metadata_manager.get_all_metadata()
            
            if all_metadata:
                status['accessible'] = True
                status['total_records'] = len(all_metadata)
                status['sample_ids'] = list(all_metadata.keys())[:5]  # 前5个ID作为样本
                
                # 获取样本元数据
                for doc_id in status['sample_ids']:
                    metadata = metadata_manager.get_metadata(doc_id)
                    if metadata:
                        status['sample_metadata'][doc_id] = {
                            'keys': list(metadata.keys()),
                            'has_text': 'text' in metadata,
                            'text_length': len(metadata.get('text', '')) if 'text' in metadata else 0
                        }
                
                self.logger.info(f"元数据状态: 总记录数={status['total_records']}")
            
        except Exception as e:
            self.logger.error(f"检查元数据状态时出错: {e}")
            status['error'] = str(e)
        
        return status
    
    def _check_vector_storage_status(self) -> Dict:
        """检查向量存储状态"""
        status = {
            'accessible': False,
            'total_vectors': 0,
            'sample_ids': [],
            'vector_dimension': 0
        }
        
        try:
            from ..storage import VectorStore
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            
            # 获取统计信息
            stats = vector_store.get_stats()
            
            if stats:
                status['accessible'] = True
                status['total_vectors'] = stats.get('total_vectors', 0)
                
                # 尝试获取一些样本向量
                # 这里需要实现获取样本向量的方法
                self.logger.info(f"向量存储状态: 总向量数={status['total_vectors']}")
            
        except Exception as e:
            self.logger.error(f"检查向量存储状态时出错: {e}")
            status['error'] = str(e)
        
        return status
    
    def _check_mapping_consistency(self, index_status: Dict, 
                                 metadata_status: Dict, 
                                 vector_storage_status: Dict) -> List[str]:
        """检查映射一致性"""
        issues = []
        
        # 检查数量一致性
        index_count = index_status.get('vector_count', 0)
        metadata_count = metadata_status.get('total_records', 0)
        vector_count = vector_storage_status.get('total_vectors', 0)
        
        if index_count != metadata_count:
            issues.append(f"索引向量数量({index_count})与元数据记录数量({metadata_count})不匹配")
        
        if index_count != vector_count:
            issues.append(f"索引向量数量({index_count})与向量存储数量({vector_count})不匹配")
        
        if metadata_count != vector_count:
            issues.append(f"元数据记录数量({metadata_count})与向量存储数量({vector_count})不匹配")
        
        # 检查ID映射问题
        if metadata_status.get('accessible') and metadata_status.get('sample_ids'):
            sample_ids = metadata_status['sample_ids']
            
            # 检查ID是否为连续整数
            try:
                int_ids = [int(id_) for id_ in sample_ids]
                min_id = min(int_ids)
                max_id = max(int_ids)
                
                if min_id < 0:
                    issues.append(f"发现负数ID: {min_id}")
                
                if max_id >= index_count and index_count > 0:
                    issues.append(f"最大ID({max_id})超出索引范围(0-{index_count-1})")
                
                # 检查ID是否连续
                expected_range = set(range(min_id, max_id + 1))
                actual_ids = set(int_ids)
                missing_ids = expected_range - actual_ids
                
                if missing_ids and len(missing_ids) > len(actual_ids) * 0.1:  # 如果缺失超过10%
                    issues.append(f"ID不连续，缺失ID数量: {len(missing_ids)}")
                
            except ValueError:
                issues.append("元数据ID不是整数格式")
        
        return issues
    
    def _generate_recommendations(self, diagnosis: Dict) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于诊断结果生成建议
        if not diagnosis['index_status'].get('exists'):
            recommendations.append("索引文件不存在，需要创建新索引")
        elif not diagnosis['index_status'].get('loadable'):
            recommendations.append("索引文件损坏，需要重建索引")
        
        if not diagnosis['metadata_status'].get('accessible'):
            recommendations.append("元数据不可访问，检查存储配置")
        elif diagnosis['metadata_status'].get('total_records', 0) == 0:
            recommendations.append("没有元数据记录，需要重新向量化数据")
        
        if not diagnosis['vector_storage_status'].get('accessible'):
            recommendations.append("向量存储不可访问，检查存储配置")
        
        if diagnosis['mapping_issues']:
            recommendations.append("存在映射问题，建议重建索引以修复ID映射")
            recommendations.append("或者使用索引修复工具进行修复")
        
        # 检查元数据质量
        sample_metadata = diagnosis['metadata_status'].get('sample_metadata', {})
        text_missing_count = 0
        
        for doc_id, meta_info in sample_metadata.items():
            if not meta_info.get('has_text'):
                text_missing_count += 1
        
        if text_missing_count > 0:
            recommendations.append(f"发现 {text_missing_count} 个样本缺少文本内容，可能影响搜索结果显示")
        
        if not recommendations:
            recommendations.append("未发现明显问题，搜索功能应该正常工作")
        
        return recommendations
    
    def test_search_functionality(self, index_path: str, query: str = "测试查询") -> Dict:
        """
        测试搜索功能
        
        Args:
            index_path: 索引文件路径
            query: 测试查询
            
        Returns:
            Dict: 测试结果
        """
        result = {
            'success': False,
            'results_count': 0,
            'has_content': False,
            'sample_results': [],
            'error': None
        }
        
        try:
            # 加载索引
            from ..indexer import IndexBuilder
            config = {'indexing': {'index_type': 'flat', 'metric': 'cosine'}}
            builder = IndexBuilder(config)
            
            if not builder.load_index(index_path):
                result['error'] = "无法加载索引"
                return result
            
            # 创建查询向量
            from ..vectorizer import TextEmbedding
            vectorization_config = {
                'vectorization': {
                    'model_name': 'multilingual-minilm',
                    'vector_dimension': 384,
                    'device': 'cpu'
                }
            }
            embedder = TextEmbedding(vectorization_config)
            query_vector = embedder.encode_text(query)
            
            # 执行搜索
            distances, indices = builder.search(query_vector.reshape(1, -1), k=5)
            
            result['success'] = True
            result['results_count'] = len(indices[0])
            
            # 检查结果内容
            from ..storage import MetadataManager
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            metadata_manager = MetadataManager(storage_config)
            
            for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
                if i >= 3:  # 只检查前3个结果
                    break
                
                try:
                    metadata = metadata_manager.get_metadata(int(idx))
                    has_text = metadata and 'text' in metadata and metadata['text'].strip()
                    
                    result['sample_results'].append({
                        'index': int(idx),
                        'distance': float(distance),
                        'has_metadata': metadata is not None,
                        'has_text': has_text,
                        'text_preview': metadata.get('text', '')[:100] if has_text else None
                    })
                    
                    if has_text:
                        result['has_content'] = True
                        
                except Exception as e:
                    result['sample_results'].append({
                        'index': int(idx),
                        'distance': float(distance),
                        'error': str(e)
                    })
            
            # 清理资源
            embedder.cleanup()
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"测试搜索功能时出错: {e}")
        
        return result


def create_search_diagnostics(config: Dict) -> SearchDiagnostics:
    """
    创建搜索诊断工具实例
    
    Args:
        config: 配置字典
        
    Returns:
        SearchDiagnostics: 搜索诊断工具实例
    """
    return SearchDiagnostics(config)
