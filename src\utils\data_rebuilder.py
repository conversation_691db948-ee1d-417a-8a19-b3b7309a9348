#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据重建工具

用于重建索引和元数据，解决ID映射不一致问题
"""

import os
import shutil
import logging
import sqlite3
import numpy as np
from typing import Dict, List, Tuple, Optional, Callable
from pathlib import Path
import time
import hashlib

logger = logging.getLogger(__name__)


class DataRebuilder:
    """数据重建器"""

    def __init__(self, config: Dict, progress_callback: Optional[Callable] = None):
        """
        初始化数据重建器

        Args:
            config: 配置字典
            progress_callback: 进度回调函数
        """
        self.config = config
        self.progress_callback = progress_callback
        self.logger = logging.getLogger(__name__)

        # 数据目录
        self.base_dir = Path(config.get('storage', {}).get('base_dir', 'data/vectors'))
        self.indices_dir = Path('data/indices')

    def rebuild_all_data(self, backup_existing: bool = True) -> bool:
        """
        重建所有数据

        Args:
            backup_existing: 是否备份现有数据

        Returns:
            bool: 是否成功重建
        """
        try:
            self.logger.info("开始重建所有数据...")

            # 1. 备份现有数据
            if backup_existing:
                self._backup_existing_data()

            # 2. 清理现有数据
            self._cleanup_existing_data()

            # 3. 重新初始化存储系统
            self._initialize_storage_system()

            # 4. 提取现有文本数据
            text_data = self._extract_text_data_from_backup()

            if not text_data:
                self.logger.warning("没有找到可重建的文本数据")
                return False

            # 5. 重新向量化和索引
            success = self._rebuild_vectors_and_index(text_data)

            if success:
                self.logger.info("数据重建完成")
                return True
            else:
                self.logger.error("数据重建失败")
                return False

        except Exception as e:
            self.logger.error(f"重建数据时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _backup_existing_data(self):
        """备份现有数据"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_dir = Path(f"data_backup_{timestamp}")

            self.logger.info(f"备份现有数据到: {backup_dir}")

            # 备份向量数据
            if self.base_dir.exists():
                shutil.copytree(self.base_dir, backup_dir / "vectors")

            # 备份索引数据
            if self.indices_dir.exists():
                shutil.copytree(self.indices_dir, backup_dir / "indices")

            self.logger.info("数据备份完成")

        except Exception as e:
            self.logger.error(f"备份数据时出错: {e}")

    def _cleanup_existing_data(self):
        """清理现有数据"""
        try:
            self.logger.info("清理现有数据...")

            # 清理向量数据
            if self.base_dir.exists():
                shutil.rmtree(self.base_dir)

            # 清理索引数据
            if self.indices_dir.exists():
                shutil.rmtree(self.indices_dir)

            self.logger.info("数据清理完成")

        except Exception as e:
            self.logger.error(f"清理数据时出错: {e}")

    def _initialize_storage_system(self):
        """重新初始化存储系统"""
        try:
            self.logger.info("重新初始化存储系统...")

            # 创建目录
            self.base_dir.mkdir(parents=True, exist_ok=True)
            self.indices_dir.mkdir(parents=True, exist_ok=True)

            # 初始化元数据管理器
            from ..storage import MetadataManager
            metadata_manager = MetadataManager(self.config)

            # 初始化向量存储
            from ..storage import VectorStore
            vector_store = VectorStore(self.config)

            self.logger.info("存储系统初始化完成")

        except Exception as e:
            self.logger.error(f"初始化存储系统时出错: {e}")
            raise

    def _extract_text_data_from_backup(self) -> List[Dict]:
        """从备份中提取文本数据"""
        try:
            # 查找所有备份，优先使用有数据的备份
            backup_dirs = sorted(Path('.').glob('data_backup_*'))
            if not backup_dirs:
                self.logger.warning("没有找到备份数据")
                return []

            # 查找包含数据的备份
            valid_backup = None
            for backup_dir in backup_dirs:
                metadata_db = backup_dir / "vectors" / "metadata" / "metadata.db"
                if metadata_db.exists():
                    # 检查是否有数据
                    conn = sqlite3.connect(metadata_db)
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM metadata")
                    count = cursor.fetchone()[0]
                    conn.close()

                    if count > 0:
                        valid_backup = backup_dir
                        self.logger.info(f"找到有效备份: {backup_dir} (包含 {count} 条记录)")
                        break

            if not valid_backup:
                self.logger.warning("没有找到包含数据的备份")
                return []

            metadata_db = valid_backup / "vectors" / "metadata" / "metadata.db"

            if not metadata_db.exists():
                self.logger.warning("备份中没有找到元数据数据库")
                return []

            self.logger.info(f"从备份提取文本数据: {metadata_db}")

            text_data = []

            # 连接到备份的数据库
            conn = sqlite3.connect(metadata_db)
            cursor = conn.cursor()

            # 首先检查表结构
            cursor.execute("PRAGMA table_info(metadata)")
            columns = [row[1] for row in cursor.fetchall()]
            self.logger.info(f"数据库列: {columns}")

            # 根据实际列名查询数据
            if 'doc_id' in columns and 'metadata' in columns:
                cursor.execute("SELECT doc_id, metadata FROM metadata")
            elif 'id' in columns and 'data' in columns:
                cursor.execute("SELECT id, data FROM metadata")
            elif 'id' in columns and 'metadata' in columns:
                cursor.execute("SELECT id, metadata FROM metadata")
            else:
                # 尝试获取所有列
                cursor.execute("SELECT * FROM metadata")
                sample_row = cursor.fetchone()
                if sample_row:
                    self.logger.info(f"样本行: {sample_row}")
                cursor.execute("SELECT * FROM metadata")

            rows = cursor.fetchall()

            # 处理不同的数据库结构
            for row in rows:
                try:
                    import json

                    # 根据列数确定数据结构
                    if len(row) >= 2:
                        doc_id = row[0]
                        metadata_json = row[1]
                    else:
                        self.logger.warning(f"行数据格式不正确: {row}")
                        continue

                    # 解析元数据
                    if isinstance(metadata_json, str):
                        metadata = json.loads(metadata_json)
                    elif isinstance(metadata_json, dict):
                        metadata = metadata_json
                    else:
                        self.logger.warning(f"元数据格式不正确: {type(metadata_json)}")
                        continue

                    if 'text' in metadata and metadata['text'].strip():
                        text_data.append({
                            'original_id': doc_id,
                            'text': metadata['text'],
                            'file_type': metadata.get('file_type', 'text'),
                            'model': metadata.get('model', 'unknown'),
                            'chunk_index': metadata.get('chunk_index', 0),
                            'total_chunks': metadata.get('total_chunks', 1)
                        })

                except Exception as e:
                    self.logger.warning(f"解析元数据时出错 (行: {row}): {e}")
                    continue

            conn.close()

            self.logger.info(f"提取了 {len(text_data)} 条文本数据")
            return text_data

        except Exception as e:
            self.logger.error(f"提取文本数据时出错: {e}")
            return []

    def _rebuild_vectors_and_index(self, text_data: List[Dict]) -> bool:
        """重新构建向量和索引"""
        try:
            self.logger.info("开始重新构建向量和索引...")

            if self.progress_callback:
                self.progress_callback(0, len(text_data), "开始向量化...")

            # 创建向量化器
            from ..vectorizer import TextEmbedding
            vectorization_config = {
                'vectorization': {
                    'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                    'vector_dimension': 384,
                    'batch_size': 32,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }
            embedder = TextEmbedding(vectorization_config)

            # 创建存储管理器
            from ..storage import MetadataManager, VectorStore
            metadata_manager = MetadataManager(self.config)
            vector_store = VectorStore(self.config)

            # 批量处理文本
            batch_size = 50
            all_vectors = []
            all_doc_ids = []

            for i in range(0, len(text_data), batch_size):
                batch = text_data[i:i + batch_size]

                if self.progress_callback:
                    self.progress_callback(i, len(text_data), f"处理批次 {i//batch_size + 1}...")

                # 生成新的连续ID
                batch_vectors = []
                batch_doc_ids = []

                for j, item in enumerate(batch):
                    # 使用连续的ID
                    new_doc_id = i + j

                    # 向量化文本
                    vector = embedder.encode_text(item['text'])
                    batch_vectors.append(vector)
                    batch_doc_ids.append(new_doc_id)

                    # 存储元数据
                    metadata = {
                        'text': item['text'],
                        'file_type': item['file_type'],
                        'model': item['model'],
                        'chunk_index': item['chunk_index'],
                        'total_chunks': item['total_chunks'],
                        'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'vector_dimension': vector.shape[0],
                        'original_id': item['original_id']  # 保留原始ID用于追踪
                    }

                    success = metadata_manager.store_metadata(new_doc_id, metadata)
                    if not success:
                        self.logger.warning(f"存储元数据失败: ID {new_doc_id}")

                all_vectors.extend(batch_vectors)
                all_doc_ids.extend(batch_doc_ids)

            # 转换为numpy数组
            vectors_array = np.vstack(all_vectors)
            doc_ids_array = np.array(all_doc_ids)

            self.logger.info(f"完成向量化，共 {len(vectors_array)} 个向量")

            # 存储向量
            if self.progress_callback:
                self.progress_callback(len(text_data), len(text_data), "存储向量...")

            success = vector_store.store_vectors(vectors_array, doc_ids_array)
            if not success:
                self.logger.error("存储向量失败")
                return False

            # 创建索引
            if self.progress_callback:
                self.progress_callback(len(text_data), len(text_data), "创建索引...")

            from ..indexer import IndexBuilder
            index_config = {
                'indexing': {
                    'index_type': 'flat',
                    'metric': 'cosine',
                    'dimension': 384
                }
            }

            builder = IndexBuilder(index_config)

            # 创建索引
            builder.create_index(vectors_array.shape[1])

            # 添加向量到索引（对于Flat索引，不使用ID）
            try:
                # 直接使用索引的add方法，因为我们使用的是连续ID
                builder.index.add(vectors_array)
                builder.total_vectors = len(vectors_array)
                self.logger.info(f"成功添加 {len(vectors_array)} 个向量到索引")
            except Exception as e:
                self.logger.error(f"添加向量到索引失败: {e}")
                return False

            # 保存索引
            index_path = self.indices_dir / "rebuilt_index.idx"
            success = builder.save_index(index_path)
            if not success:
                self.logger.error("保存索引失败")
                return False

            # 清理资源
            embedder.cleanup()

            self.logger.info("向量和索引重建完成")

            if self.progress_callback:
                self.progress_callback(len(text_data), len(text_data), "重建完成")

            return True

        except Exception as e:
            self.logger.error(f"重建向量和索引时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def verify_rebuilt_data(self) -> Dict:
        """验证重建的数据"""
        try:
            self.logger.info("验证重建的数据...")

            # 检查索引
            index_files = list(self.indices_dir.glob('*.idx'))
            index_status = len(index_files) > 0

            # 检查元数据
            from ..storage import MetadataManager
            metadata_manager = MetadataManager(self.config)
            all_metadata = metadata_manager.get_all_metadata()
            metadata_count = len(all_metadata)

            # 检查向量存储
            from ..storage import VectorStore
            vector_store = VectorStore(self.config)
            stats = vector_store.get_stats()
            vector_count = stats.get('total_vectors', 0)

            # 检查ID一致性
            if all_metadata and vector_count > 0:
                metadata_ids = sorted(list(all_metadata.keys()))
                expected_ids = list(range(len(metadata_ids)))
                id_consistency = metadata_ids == expected_ids
            else:
                id_consistency = False

            result = {
                'index_exists': index_status,
                'metadata_count': metadata_count,
                'vector_count': vector_count,
                'id_consistency': id_consistency,
                'data_consistent': index_status and metadata_count > 0 and vector_count > 0 and id_consistency
            }

            self.logger.info(f"验证结果: {result}")
            return result

        except Exception as e:
            self.logger.error(f"验证数据时出错: {e}")
            return {
                'index_exists': False,
                'metadata_count': 0,
                'vector_count': 0,
                'id_consistency': False,
                'data_consistent': False,
                'error': str(e)
            }


def create_data_rebuilder(config: Dict, progress_callback: Optional[Callable] = None) -> DataRebuilder:
    """
    创建数据重建器实例

    Args:
        config: 配置字典
        progress_callback: 进度回调函数

    Returns:
        DataRebuilder: 数据重建器实例
    """
    return DataRebuilder(config, progress_callback)
