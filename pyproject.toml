
[build-system]
requires = ["setuptools>=45", "wheel", "pip>=21.0"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/tests/data/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true
skip_glob = ["tests/data/*"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_optional = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --cov=src --cov-report=term-missing"
testpaths = [
    "tests",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*", "*Test"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "tests/*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]
ignore_errors = true
fail_under = 80

[tool.pylint.messages_control]
disable = [
    "C0111",  # missing-docstring
    "C0103",  # invalid-name
    "C0330",  # bad-continuation
    "C0326",  # bad-whitespace
    "W0621",  # redefined-outer-name
    "W0614",  # unused-wildcard-import
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
    "R0914",  # too-many-locals
]

[tool.pylint.format]
max-line-length = 88

[tool.pylint.basic]
good-names = ["i", "j", "k", "ex", "Run", "_", "fp", "id"]

[tool.bandit]
exclude_dirs = ["tests", "examples"]
skips = ["B101", "B404", "B603"]

[tool.semantic_release]
version_variable = "src/version.py:__version__"
branch = "main"
upload_to_pypi = true
build_command = "python setup.py sdist bdist_wheel"
