#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试训练修复效果
验证汽车相关性检查、编码处理等修复是否有效
"""

import sys
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_automotive_relevance():
    """测试汽车相关性计算"""
    print("🧪 测试汽车相关性计算...")
    
    try:
        from training_automation import AutomotiveTrainingPipeline
        
        pipeline = AutomotiveTrainingPipeline()
        
        # 测试文件
        test_cases = [
            {
                'filename': 'SAE_USCAR-21_EN_2008-10_端子压接性能规范.pdf',
                'content': '汽车电器连接器端子压接性能测试标准规范',
                'expected_high': True
            },
            {
                'filename': '汽车电器连接器系统性能规范.pdf', 
                'content': '本标准规定了汽车电器连接器系统的性能要求和测试方法',
                'expected_high': True
            },
            {
                'filename': 'VW_75174_CH_2018-10_汽车连接器_测试规范.pdf',
                'content': '大众汽车连接器测试规范，包括电气性能和机械性能测试',
                'expected_high': True
            },
            {
                'filename': 'random_document.pdf',
                'content': '这是一个普通的文档，没有汽车相关内容',
                'expected_high': False
            }
        ]
        
        for case in test_cases:
            relevance = pipeline._calculate_automotive_relevance(case['content'])
            print(f"  {case['filename']}: {relevance:.3f}")
            
            if case['expected_high'] and relevance < 0.1:
                print(f"    ⚠️ 警告: 预期高相关性但得分较低")
            elif not case['expected_high'] and relevance > 0.3:
                print(f"    ⚠️ 警告: 预期低相关性但得分较高")
            else:
                print(f"    ✅ 符合预期")
        
        return True
        
    except Exception as e:
        print(f"❌ 汽车相关性测试失败: {e}")
        return False

def test_quality_validation():
    """测试质量验证"""
    print("\n🧪 测试文档质量验证...")
    
    try:
        from training_automation import AutomotiveTrainingPipeline
        
        pipeline = AutomotiveTrainingPipeline()
        
        # 测试文件路径
        test_files = [
            Path('training_data/raw_documents/enterprise_standards/OEM_standards/07_福特/SAE_USCAR-21_EN_2008-10_端子压接性能规范.pdf'),
            Path('training_data/raw_documents/enterprise_standards/OEM_standards/07_福特/SAE_USCAR-2_CH_2007-11_汽车电器连接器系统性能规范.pdf'),
            Path('training_data/raw_documents/enterprise_standards/OEM_standards/07_福特/SDS_CONN_V14_EN_2007-01_福特连接器系统设计标准.pdf'),
        ]
        
        for file_path in test_files:
            if file_path.exists():
                # 模拟内容
                content = f"汽车电器连接器测试标准 {file_path.name}"
                
                is_valid = pipeline._validate_document_quality(content, file_path)
                print(f"  {file_path.name}: {'✅ 通过' if is_valid else '❌ 未通过'}")
            else:
                print(f"  {file_path.name}: ⚠️ 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 质量验证测试失败: {e}")
        return False

def test_encoding_fix():
    """测试编码修复"""
    print("\n🧪 测试编码修复...")
    
    try:
        from training_automation import AutomotiveTrainingPipeline
        
        pipeline = AutomotiveTrainingPipeline()
        
        # 测试包含编码问题的文本
        problematic_texts = [
            "正常文本内容",
            "包含问题字符的文本\udcff\udcfe",
            "汽车电器\u0000连接器",
            "测试\uffff内容"
        ]
        
        for i, text in enumerate(problematic_texts):
            try:
                fixed_text = pipeline._fix_encoding_issues(text)
                print(f"  测试 {i+1}: ✅ 修复成功")
                print(f"    原文长度: {len(text)}, 修复后长度: {len(fixed_text)}")
            except Exception as e:
                print(f"  测试 {i+1}: ❌ 修复失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 编码修复测试失败: {e}")
        return False

def test_file_size_check():
    """测试文件大小检查"""
    print("\n🧪 测试文件大小检查...")
    
    try:
        # 检查实际文件大小
        data_dir = Path('training_data/raw_documents')
        if data_dir.exists():
            large_files = []
            total_files = 0
            
            for file_path in data_dir.rglob('*.pdf'):
                total_files += 1
                file_size = file_path.stat().st_size
                size_mb = file_size / (1024 * 1024)
                
                if size_mb > 20:  # 大于20MB
                    large_files.append((file_path, size_mb))
            
            print(f"  总文件数: {total_files}")
            print(f"  大文件数 (>20MB): {len(large_files)}")
            
            if large_files:
                print("  大文件列表:")
                for file_path, size_mb in large_files[:5]:  # 只显示前5个
                    print(f"    {file_path.name}: {size_mb:.1f}MB")
                if len(large_files) > 5:
                    print(f"    ... 还有 {len(large_files) - 5} 个大文件")
        else:
            print("  ⚠️ 数据目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件大小检查失败: {e}")
        return False

def test_batch_processing_simulation():
    """模拟批量处理测试"""
    print("\n🧪 模拟批量处理测试...")
    
    try:
        # 模拟文档列表
        mock_documents = {
            'enterprise_standards': [
                Path(f'doc_{i}.pdf') for i in range(50)
            ]
        }
        
        BATCH_SIZE = 10
        total_processed = 0
        
        for category, files in mock_documents.items():
            print(f"  处理类别: {category}")
            
            for i in range(0, len(files), BATCH_SIZE):
                batch = files[i:i+BATCH_SIZE]
                batch_num = i // BATCH_SIZE + 1
                total_batches = (len(files) - 1) // BATCH_SIZE + 1
                
                print(f"    批次 {batch_num}/{total_batches}: {len(batch)} 个文档")
                total_processed += len(batch)
        
        print(f"  总处理文档数: {total_processed}")
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 训练修复效果测试")
    print("=" * 60)
    
    tests = [
        ("汽车相关性计算", test_automotive_relevance),
        ("文档质量验证", test_quality_validation), 
        ("编码修复", test_encoding_fix),
        ("文件大小检查", test_file_size_check),
        ("批量处理模拟", test_batch_processing_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！训练修复效果良好，可以进行全量训练。")
        return True
    else:
        print("⚠️ 部分测试失败，建议检查相关问题后再进行训练。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
