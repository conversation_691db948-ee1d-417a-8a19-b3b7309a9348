#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查备份数据内容
"""

import sqlite3
import json
from pathlib import Path

def check_backup_data():
    """检查备份数据"""
    # 查找所有备份
    backup_dirs = sorted(Path('.').glob('data_backup_*'))
    if not backup_dirs:
        print("没有找到备份数据")
        return

    print(f"找到 {len(backup_dirs)} 个备份:")
    for backup_dir in backup_dirs:
        print(f"  {backup_dir}")

    # 检查每个备份
    for backup_dir in backup_dirs:
        print(f"\n检查备份: {backup_dir}")

        # 检查元数据数据库
        metadata_db = backup_dir / "vectors" / "metadata" / "metadata.db"
        if metadata_db.exists():
            print(f"  元数据数据库: 存在")
            check_metadata_db(metadata_db)
        else:
            print(f"  元数据数据库: 不存在")

        # 检查向量文件
        vectors_h5 = backup_dir / "vectors" / "vectors.h5"
        if vectors_h5.exists():
            print(f"  向量文件: 存在 ({vectors_h5.stat().st_size} 字节)")
            check_vectors_h5(vectors_h5)
        else:
            print(f"  向量文件: 不存在")

        # 检查索引文件
        indices_dir = backup_dir / "indices"
        if indices_dir.exists():
            index_files = list(indices_dir.glob('*.idx'))
            print(f"  索引文件: {len(index_files)} 个")
            for idx_file in index_files:
                print(f"    {idx_file.name} ({idx_file.stat().st_size} 字节)")
        else:
            print(f"  索引目录: 不存在")

def check_metadata_db(metadata_db):
    """检查元数据数据库"""
    try:
        conn = sqlite3.connect(metadata_db)
        cursor = conn.cursor()

        # 检查表结构
        cursor.execute("PRAGMA table_info(metadata)")
        columns = cursor.fetchall()

        # 检查数据数量
        cursor.execute("SELECT COUNT(*) FROM metadata")
        count = cursor.fetchone()[0]
        print(f"    记录数: {count}")

        if count > 0:
            # 查看一条记录
            cursor.execute("SELECT * FROM metadata LIMIT 1")
            row = cursor.fetchone()

            if len(row) >= 2:
                try:
                    data = json.loads(row[1])
                    print(f"    数据字段: {list(data.keys())}")
                    if 'text' in data:
                        text_len = len(data['text'])
                        print(f"    文本长度: {text_len} 字符")
                except Exception as e:
                    print(f"    解析数据失败: {e}")

        conn.close()
    except Exception as e:
        print(f"    检查数据库失败: {e}")

def check_vectors_h5(vectors_h5):
    """检查向量文件"""
    try:
        import h5py
        with h5py.File(vectors_h5, 'r') as f:
            print(f"    HDF5 键: {list(f.keys())}")
            if 'vectors' in f:
                vectors = f['vectors']
                print(f"    向量形状: {vectors.shape}")
                print(f"    向量类型: {vectors.dtype}")
            if 'doc_ids' in f:
                doc_ids = f['doc_ids']
                print(f"    文档ID数量: {len(doc_ids)}")
                print(f"    ID范围: {doc_ids[:].min()} - {doc_ids[:].max()}")
    except Exception as e:
        print(f"    检查向量文件失败: {e}")

if __name__ == "__main__":
    check_backup_data()
