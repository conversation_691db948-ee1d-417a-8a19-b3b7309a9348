
version: '3.8'

services:
  md_vector_processor:
    build:
      context: .
      dockerfile: Dockerfile
    image: md_vector_processor:latest
    container_name: md_vector_processor
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
      - ./config:/app/config
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G

  # 可选：添加数据库服务
  # db:
  #   image: postgres:13-alpine
  #   environment:
  #     - POSTGRES_USER=mdvector
  #     - POSTGRES_PASSWORD=password
  #     - POSTGRES_DB=mdvector
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"

  # 可选：添加缓存服务
  # redis:
  #   image: redis:6-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    driver: bridge