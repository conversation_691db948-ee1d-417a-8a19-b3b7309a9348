
# 核心依赖
numpy>=1.21.0
faiss-cpu>=1.7.0  # 如果使用GPU，替换为faiss-gpu
sentence-transformers>=2.2.0
transformers>=4.21.0
torch>=1.9.0
tqdm>=4.62.0
langdetect>=1.0.9  # 语言检测

# 高级模型
bert-for-tf2>=0.14.9  # BERT-MRC支持
tokenizers>=0.13.0    # 高效分词器
datasets>=2.10.0      # 数据集处理
accelerate>=0.18.0    # 模型加速
optimum>=1.8.0        # 模型优化
onnxruntime>=1.14.0   # ONNX运行时

# 文本处理
jieba>=0.42.1
nltk>=3.6.0
spacy>=3.2.0
zhconv>=1.4.0
mecab-python3>=1.0.5  # 日语分词
konlpy>=0.6.0         # 韩语分词
pythainlp>=3.1.0      # 泰语分词
pyarabic>=0.6.15      # 阿拉伯语处理
pymorphy2>=0.9.1      # 俄语形态分析
fugashi>=1.2.0        # 日语形态分析
sudachipy>=0.6.7      # 日语分词

# 代码处理
pygments>=2.14.0      # 代码语法高亮
tree-sitter>=0.20.1   # 代码解析

# 数据存储和处理
h5py>=3.6.0
sqlite3>=3.35.0
hnswlib>=0.6.0
lmdb>=1.4.0           # 高性能键值存储
annoy>=1.17.1         # 近似最近邻
scann>=1.2.9          # Google的向量搜索库

# 文件处理
python-magic>=0.4.24
chardet>=4.0.0
python-frontmatter>=1.0.0

# 并发和日志
concurrent-log-handler>=0.9.19
threading>=3.8.0
ray>=2.3.0            # 分布式计算

# 配置和工具
pyyaml>=5.4.1
python-dotenv>=0.19.0
hydra-core>=1.3.2     # 高级配置管理

# 可选依赖
umap-learn>=0.5.0     # 用于降维可视化
matplotlib>=3.4.0     # 用于可视化
scikit-learn>=0.24.0  # 用于数据处理和降维
plotly>=5.14.0        # 交互式可视化

# 开发工具
pytest>=6.2.5
black>=21.7b0
flake8>=3.9.0
mypy>=0.910
isort>=5.9.0