
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分词器模块
负责文本分词、停用词过滤和词形还原
支持多语言和代码块处理
"""

import logging
import re
from typing import Dict, Any, List, Set, Optional, Tuple, Union
from enum import Enum
import jieba
import nltk
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from nltk.stem.porter import PorterStemmer
from nltk.corpus import stopwords
import spacy
from pathlib import Path
import json
import langdetect
from langdetect import detect
from langdetect.lang_detect_exception import LangDetectException
import pygments
from pygments.lexers import get_lexer_by_name, guess_lexer
from pygments.token import Token

class LanguageType(Enum):
    """语言类型枚举"""
    CHINESE = "chinese"
    ENGLISH = "english"
    JAPANESE = "japanese"
    KOREAN = "korean"
    RUSSIAN = "russian"
    SPANISH = "spanish"
    FRENCH = "french"
    GERMAN = "german"
    ARABIC = "arabic"
    THAI = "thai"
    VIETNAMESE = "vietnamese"
    HINDI = "hindi"
    TURKISH = "turkish"
    INDONESIAN = "indonesian"
    MALAY = "malay"
    MULTILINGUAL = "multilingual"
    AUTO = "auto"
    UNKNOWN = "unknown"
    CODE = "code"

class CodeLanguage(Enum):
    """代码语言类型枚举"""
    PYTHON = "python"
    JAVA = "java"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    C = "c"
    CPP = "cpp"
    CSHARP = "csharp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    SQL = "sql"
    HTML = "html"
    CSS = "css"
    SHELL = "shell"
    UNKNOWN = "unknown"

class TextType(Enum):
    """文本类型枚举"""
    NATURAL_LANGUAGE = "natural_language"
    CODE = "code"
    MIXED = "mixed"

class TextTokenizer:
    """文本分词器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化分词器

        Args:
            config: 配置字典，包含预处理相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.preprocessing_config = config.get('preprocessing', {})

        # 基本配置
        language_str = self.preprocessing_config.get('language', 'chinese')
        self.language = self._parse_language_type(language_str)
        self.use_lemmatization = self.preprocessing_config.get('use_lemmatization', True)
        self.use_stemming = self.preprocessing_config.get('use_stemming', False)
        self.remove_stopwords = self.preprocessing_config.get('remove_stopwords', True)

        # 多语言支持配置
        self.languages_config = self.preprocessing_config.get('languages', {})
        self.enabled_languages = self._parse_enabled_languages()
        self.auto_detect_language = self.languages_config.get('auto_detect', True)
        self.fallback_language = self._parse_language_type(self.languages_config.get('fallback', 'english'))

        # 代码块处理配置
        self.code_blocks_config = self.preprocessing_config.get('code_blocks', {})
        self.process_code_blocks = self.code_blocks_config.get('enabled', True)
        self.supported_code_languages = self._parse_supported_code_languages()
        self.code_special_model = self.code_blocks_config.get('special_model', None)

        # 小语种支持配置
        self.low_resource_config = self.preprocessing_config.get('low_resource_languages', {})
        self.low_resource_enabled = self.low_resource_config.get('enabled', False)
        self.low_resource_languages = self._parse_low_resource_languages()
        self.use_specialized_tokenizers = self.low_resource_config.get('use_specialized_tokenizers', True)

        # 初始化分词器和处理工具
        self._initialize_tokenizers()
        self._initialize_stemmers()
        self._load_stopwords()

        # 用户自定义词典和停用词
        self._load_custom_dictionaries()

        # 初始化代码处理器
        if self.process_code_blocks:
            self._initialize_code_processors()

    def _parse_language_type(self, language_str: str) -> LanguageType:
        """解析语言类型字符串"""
        try:
            # 使用更安全的方式获取枚举值
            language_str = language_str.lower()
            for lang_type in LanguageType:
                if lang_type.value == language_str:
                    return lang_type
            raise ValueError(f"未知的语言类型: {language_str}")
        except ValueError:
            self.logger.warning(f"未知的语言类型: {language_str}，使用默认的multilingual")
            return LanguageType.MULTILINGUAL

    def _parse_enabled_languages(self) -> List[LanguageType]:
        """解析启用的语言列表"""
        enabled_langs = self.languages_config.get('enabled', [])
        if not enabled_langs:
            # 默认启用中文和英文
            return [LanguageType.CHINESE, LanguageType.ENGLISH]

        result = []
        for lang_str in enabled_langs:
            try:
                # 使用更安全的方式获取枚举值
                lang_str = lang_str.lower()
                found = False
                for lang_type in LanguageType:
                    if lang_type.value == lang_str:
                        result.append(lang_type)
                        found = True
                        break
                if not found:
                    self.logger.warning(f"未知的语言类型: {lang_str}，将被忽略")
            except Exception as e:
                self.logger.warning(f"处理语言类型时出错: {lang_str}, {e}，将被忽略")

        if not result:
            # 如果没有有效的语言，默认启用中文和英文
            return [LanguageType.CHINESE, LanguageType.ENGLISH]

        return result

    def _parse_supported_code_languages(self) -> List[CodeLanguage]:
        """解析支持的代码语言列表"""
        code_langs = self.code_blocks_config.get('languages', [])
        if not code_langs:
            # 默认支持常见的代码语言
            return [
                CodeLanguage.PYTHON, CodeLanguage.JAVA, CodeLanguage.JAVASCRIPT,
                CodeLanguage.CPP, CodeLanguage.CSHARP
            ]

        result = []
        for lang_str in code_langs:
            try:
                # 使用更安全的方式获取枚举值
                lang_str = lang_str.lower()
                found = False
                for code_lang in CodeLanguage:
                    if code_lang.value == lang_str:
                        result.append(code_lang)
                        found = True
                        break
                if not found:
                    self.logger.warning(f"未知的代码语言类型: {lang_str}，将被忽略")
            except Exception as e:
                self.logger.warning(f"处理代码语言类型时出错: {lang_str}, {e}，将被忽略")

        if not result:
            # 如果没有有效的代码语言，默认支持Python
            return [CodeLanguage.PYTHON]

        return result

    def _parse_low_resource_languages(self) -> List[LanguageType]:
        """解析小语种语言列表"""
        low_resource_langs = self.low_resource_config.get('languages', [])
        if not low_resource_langs:
            return []

        result = []
        for lang_str in low_resource_langs:
            try:
                # 使用更安全的方式获取枚举值
                lang_str = lang_str.lower()
                found = False
                for lang_type in LanguageType:
                    if lang_type.value == lang_str:
                        result.append(lang_type)
                        found = True
                        break
                if not found:
                    self.logger.warning(f"未知的小语种语言类型: {lang_str}，将被忽略")
            except Exception as e:
                self.logger.warning(f"处理小语种语言类型时出错: {lang_str}, {e}，将被忽略")

        return result

    def _initialize_tokenizers(self):
        """初始化分词器"""
        try:
            # 中文分词器
            if self.language == LanguageType.CHINESE or LanguageType.CHINESE in self.enabled_languages:
                # 加载用户自定义词典（如果存在）
                custom_dict_path = Path('data/dictionaries/custom_dict.txt')
                if custom_dict_path.exists():
                    jieba.load_userdict(str(custom_dict_path))
                self.logger.info("已初始化中文分词器")

            # 英文分词器
            if self.language == LanguageType.ENGLISH or LanguageType.ENGLISH in self.enabled_languages:
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    nltk.download('punkt')

                try:
                    nltk.data.find('corpora/wordnet')
                except LookupError:
                    nltk.download('wordnet')
                self.logger.info("已初始化英文分词器")

            # 多语言支持
            if self.language == LanguageType.MULTILINGUAL or len(self.enabled_languages) > 1:
                try:
                    self.nlp = spacy.load('xx_ent_wiki_sm')
                    self.logger.info("已加载SpaCy多语言模型")
                except OSError:
                    self.logger.warning("SpaCy多语言模型未安装，将使用基本分词")
                    self.nlp = None

            # 小语种支持
            if self.low_resource_enabled and self.low_resource_languages:
                self._initialize_low_resource_tokenizers()

            # 初始化语言检测器
            if self.language == LanguageType.AUTO or self.auto_detect_language:
                langdetect.DetectorFactory.seed = 0  # 确保结果一致性
                self.logger.info("已初始化语言检测器")

        except Exception as e:
            self.logger.error(f"初始化分词器时出错: {e}")
            raise

    def _initialize_low_resource_tokenizers(self):
        """初始化小语种分词器"""
        try:
            # 日语分词器
            if LanguageType.JAPANESE in self.low_resource_languages:
                try:
                    import MeCab
                    self.mecab = MeCab.Tagger("-Owakati")
                    self.logger.info("已初始化日语分词器 (MeCab)")
                except ImportError:
                    self.logger.warning("MeCab未安装，日语分词将使用基本分词")

            # 韩语分词器
            if LanguageType.KOREAN in self.low_resource_languages:
                try:
                    from konlpy.tag import Mecab as KoMecab
                    self.komecab = KoMecab()
                    self.logger.info("已初始化韩语分词器 (KoNLPy)")
                except ImportError:
                    self.logger.warning("KoNLPy未安装，韩语分词将使用基本分词")

            # 泰语分词器
            if LanguageType.THAI in self.low_resource_languages:
                try:
                    from pythainlp.tokenize import word_tokenize as thai_tokenize
                    self.thai_tokenize = thai_tokenize
                    self.logger.info("已初始化泰语分词器 (PyThaiNLP)")
                except ImportError:
                    self.logger.warning("PyThaiNLP未安装，泰语分词将使用基本分词")

            # 阿拉伯语分词器
            if LanguageType.ARABIC in self.low_resource_languages:
                try:
                    import pyarabic.araby as araby
                    self.araby = araby
                    self.logger.info("已初始化阿拉伯语分词器 (PyArabic)")
                except ImportError:
                    self.logger.warning("PyArabic未安装，阿拉伯语分词将使用基本分词")

            # 俄语分词器
            if LanguageType.RUSSIAN in self.low_resource_languages:
                try:
                    import pymorphy2
                    self.pymorphy2 = pymorphy2.MorphAnalyzer()
                    self.logger.info("已初始化俄语分词器 (PyMorphy2)")
                except ImportError:
                    self.logger.warning("PyMorphy2未安装，俄语分词将使用基本分词")

        except Exception as e:
            self.logger.error(f"初始化小语种分词器时出错: {e}")

    def _initialize_code_processors(self):
        """初始化代码处理器"""
        try:
            # 初始化代码语言检测器
            self.code_lexers = {}
            for code_lang in self.supported_code_languages:
                try:
                    lexer_name = self._get_lexer_name(code_lang)
                    self.code_lexers[code_lang] = get_lexer_by_name(lexer_name)
                    self.logger.debug(f"已加载{code_lang.value}代码词法分析器")
                except Exception as e:
                    self.logger.warning(f"加载{code_lang.value}代码词法分析器失败: {e}")

            # 编译代码块正则表达式
            self.code_block_pattern = re.compile(r'```(\w*)\n([\s\S]*?)\n```')
            self.inline_code_pattern = re.compile(r'`([^`]+)`')

            self.logger.info(f"已初始化代码处理器，支持{len(self.code_lexers)}种代码语言")

        except Exception as e:
            self.logger.error(f"初始化代码处理器时出错: {e}")

    def _get_lexer_name(self, code_lang: CodeLanguage) -> str:
        """获取代码语言对应的词法分析器名称"""
        mapping = {
            CodeLanguage.PYTHON: "python",
            CodeLanguage.JAVA: "java",
            CodeLanguage.JAVASCRIPT: "javascript",
            CodeLanguage.TYPESCRIPT: "typescript",
            CodeLanguage.C: "c",
            CodeLanguage.CPP: "cpp",
            CodeLanguage.CSHARP: "csharp",
            CodeLanguage.GO: "go",
            CodeLanguage.RUST: "rust",
            CodeLanguage.PHP: "php",
            CodeLanguage.RUBY: "ruby",
            CodeLanguage.SWIFT: "swift",
            CodeLanguage.KOTLIN: "kotlin",
            CodeLanguage.SQL: "sql",
            CodeLanguage.HTML: "html",
            CodeLanguage.CSS: "css",
            CodeLanguage.SHELL: "bash",
        }
        return mapping.get(code_lang, "text")

    def _initialize_stemmers(self):
        """初始化词干提取器和词形还原器"""
        try:
            # 修复：使用枚举值进行比较
            if self.language in [LanguageType.ENGLISH, LanguageType.MULTILINGUAL]:
                if self.use_stemming:
                    self.stemmer = PorterStemmer()
                if self.use_lemmatization:
                    self.lemmatizer = WordNetLemmatizer()
            else:
                # 为所有语言类型初始化lemmatizer，避免属性缺失错误
                self.lemmatizer = WordNetLemmatizer() if self.use_lemmatization else None
                self.stemmer = None
        except Exception as e:
            self.logger.error(f"初始化词干提取器时出错: {e}")
            raise

    def _load_stopwords(self):
        """加载停用词"""
        self.stopwords = set()

        try:
            if self.remove_stopwords:
                # 加载NLTK停用词
                if self.language in [LanguageType.ENGLISH, LanguageType.MULTILINGUAL]:
                    try:
                        nltk.data.find('corpora/stopwords')
                    except LookupError:
                        nltk.download('stopwords')
                    self.stopwords.update(stopwords.words('english'))

                # 加载中文停用词
                if self.language in [LanguageType.CHINESE, LanguageType.MULTILINGUAL]:
                    stopwords_path = Path('data/dictionaries/chinese_stopwords.txt')
                    if stopwords_path.exists():
                        with open(stopwords_path, 'r', encoding='utf-8') as f:
                            self.stopwords.update(line.strip() for line in f)

                # 加载用户自定义停用词
                custom_stopwords_path = Path('data/dictionaries/custom_stopwords.txt')
                if custom_stopwords_path.exists():
                    with open(custom_stopwords_path, 'r', encoding='utf-8') as f:
                        self.stopwords.update(line.strip() for line in f)

        except Exception as e:
            self.logger.error(f"加载停用词时出错: {e}")
            raise

    def _load_custom_dictionaries(self):
        """加载用户自定义词典"""
        try:
            # 加载自定义词典配置
            dict_config_path = Path('config/dictionary_config.json')
            if dict_config_path.exists():
                with open(dict_config_path, 'r', encoding='utf-8') as f:
                    dict_config = json.load(f)

                # 添加自定义词典
                if 'custom_words' in dict_config:
                    for word in dict_config['custom_words']:
                        jieba.add_word(word)

                # 添加自定义停用词
                if 'custom_stopwords' in dict_config:
                    self.stopwords.update(dict_config['custom_stopwords'])

        except Exception as e:
            self.logger.error(f"加载自定义词典时出错: {e}")

    def detect_language(self, text: str) -> LanguageType:
        """
        检测文本语言

        Args:
            text: 输入文本

        Returns:
            LanguageType: 检测到的语言类型
        """
        if not text or len(text.strip()) < 10:
            return self.fallback_language

        try:
            # 检测是否是代码
            if self.process_code_blocks and self._is_likely_code(text):
                return LanguageType.CODE

            # 使用langdetect检测语言
            lang_code = detect(text)

            # 映射语言代码到LanguageType
            lang_mapping = {
                'zh': LanguageType.CHINESE,
                'en': LanguageType.ENGLISH,
                'ja': LanguageType.JAPANESE,
                'ko': LanguageType.KOREAN,
                'ru': LanguageType.RUSSIAN,
                'es': LanguageType.SPANISH,
                'fr': LanguageType.FRENCH,
                'de': LanguageType.GERMAN,
                'ar': LanguageType.ARABIC,
                'th': LanguageType.THAI,
                'vi': LanguageType.VIETNAMESE,
                'hi': LanguageType.HINDI,
                'tr': LanguageType.TURKISH,
                'id': LanguageType.INDONESIAN,
                'ms': LanguageType.MALAY
            }

            detected_lang = lang_mapping.get(lang_code, self.fallback_language)

            # 检查是否支持该语言
            if detected_lang in self.enabled_languages:
                return detected_lang
            elif detected_lang in self.low_resource_languages and self.low_resource_enabled:
                return detected_lang
            else:
                return self.fallback_language

        except LangDetectException:
            return self.fallback_language

    def _is_likely_code(self, text: str) -> bool:
        """
        判断文本是否可能是代码

        Args:
            text: 输入文本

        Returns:
            bool: 是否可能是代码
        """
        # 检查是否是Markdown代码块
        if self.code_block_pattern.search(text):
            return True

        # 检查常见代码特征
        code_indicators = [
            # 编程语言关键字
            r'\b(function|def|class|import|from|var|const|let|if|else|for|while|return)\b',
            # 变量赋值
            r'[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*',
            # 函数调用
            r'[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)',
            # 括号和缩进
            r'[\{\}\[\]]{3,}',
            # 行尾分号
            r';\s*$',
            # HTML标签
            r'<[a-zA-Z][^>]*>.*?</[a-zA-Z][^>]*>',
            # 注释
            r'(//|#|/\*|\*/).*?$'
        ]

        code_pattern = re.compile('|'.join(code_indicators))
        code_line_count = sum(1 for line in text.split('\n') if code_pattern.search(line))
        total_lines = max(1, len(text.split('\n')))

        # 如果超过30%的行包含代码特征，认为是代码
        return code_line_count / total_lines > 0.3

    def tokenize_chinese(self, text: str) -> List[str]:
        """
        中文分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        return [token for token in jieba.cut(text) if token.strip()]

    def tokenize_english(self, text: str) -> List[str]:
        """
        英文分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        return word_tokenize(text)

    def tokenize_japanese(self, text: str) -> List[str]:
        """
        日语分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if hasattr(self, 'mecab'):
            return self.mecab.parse(text).strip().split()
        else:
            # 回退到基本分词
            return text.split()

    def tokenize_korean(self, text: str) -> List[str]:
        """
        韩语分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if hasattr(self, 'komecab'):
            return [token[0] for token in self.komecab.pos(text)]
        else:
            # 回退到基本分词
            return text.split()

    def tokenize_thai(self, text: str) -> List[str]:
        """
        泰语分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if hasattr(self, 'thai_tokenize'):
            return self.thai_tokenize(text)
        else:
            # 回退到基本分词
            return text.split()

    def tokenize_arabic(self, text: str) -> List[str]:
        """
        阿拉伯语分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if hasattr(self, 'araby'):
            return self.araby.tokenize(text)
        else:
            # 回退到基本分词
            return text.split()

    def tokenize_russian(self, text: str) -> List[str]:
        """
        俄语分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if hasattr(self, 'pymorphy2'):
            # 使用空格分词，然后进行词形还原
            tokens = text.split()
            return [self.pymorphy2.parse(token)[0].normal_form for token in tokens]
        else:
            # 回退到基本分词
            return text.split()

    def tokenize_multilingual(self, text: str) -> List[str]:
        """
        多语言分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        if self.nlp:
            # 使用SpaCy进行分词
            doc = self.nlp(text)
            return [token.text for token in doc]
        else:
            # 回退到基本分词
            tokens = []
            # 尝试中文分词
            chinese_tokens = self.tokenize_chinese(text)
            tokens.extend(chinese_tokens)
            # 尝试英文分词
            english_tokens = self.tokenize_english(text)
            tokens.extend(english_tokens)
            return tokens

    def tokenize_code(self, code: str, language: Optional[CodeLanguage] = None) -> List[str]:
        """
        代码分词

        Args:
            code: 代码文本
            language: 代码语言类型（可选）

        Returns:
            List[str]: 分词结果
        """
        try:
            # 如果没有指定语言，尝试猜测
            if language is None:
                try:
                    lexer = guess_lexer(code)
                    # 查找对应的CodeLanguage
                    for code_lang, lexer_obj in self.code_lexers.items():
                        if lexer.__class__.__name__ == lexer_obj.__class__.__name__:
                            language = code_lang
                            break
                except Exception:
                    # 猜测失败，使用纯文本处理
                    language = CodeLanguage.UNKNOWN

            # 如果有对应的词法分析器，使用它进行分词
            if language in self.code_lexers:
                lexer = self.code_lexers[language]
                tokens = []

                # 使用Pygments进行词法分析
                for token_type, value in lexer.get_tokens(code):
                    # 只保留有意义的token
                    if token_type in Token.Name or token_type in Token.Keyword or token_type in Token.String:
                        tokens.append(value)

                return tokens
            else:
                # 回退到简单的空格分词
                return [token for token in re.split(r'[\s\(\)\{\}\[\];,\.=<>]+', code) if token]

        except Exception as e:
            self.logger.warning(f"代码分词时出错: {e}")
            # 回退到简单的空格分词
            return [token for token in re.split(r'[\s\(\)\{\}\[\];,\.=<>]+', code) if token]

    def process_tokens(self, tokens: List[str]) -> List[str]:
        """
        处理分词结果（词干提取/词形还原、停用词过滤）

        Args:
            tokens: 分词列表

        Returns:
            List[str]: 处理后的分词列表
        """
        processed_tokens = []

        for token in tokens:
            # 跳过停用词
            if self.remove_stopwords and token.lower() in self.stopwords:
                continue

            # 词形还原
            if self.use_lemmatization:
                if isinstance(self.language, LanguageType):
                    if self.language in [LanguageType.ENGLISH, LanguageType.MULTILINGUAL]:
                        token = self.lemmatizer.lemmatize(token)
                    elif self.language == LanguageType.RUSSIAN and hasattr(self, 'pymorphy2'):
                        # 俄语词形还原
                        token = self.pymorphy2.parse(token)[0].normal_form
                elif self.language in ['english', 'multilingual']:
                    # 兼容旧版本
                    token = self.lemmatizer.lemmatize(token)

            # 词干提取
            if self.use_stemming:
                if isinstance(self.language, LanguageType):
                    if self.language in [LanguageType.ENGLISH, LanguageType.MULTILINGUAL]:
                        token = self.stemmer.stem(token)
                elif self.language in ['english', 'multilingual']:
                    # 兼容旧版本
                    token = self.stemmer.stem(token)

            processed_tokens.append(token)

        return processed_tokens

    def extract_code_blocks(self, text: str) -> List[Tuple[str, Optional[str]]]:
        """
        从文本中提取代码块

        Args:
            text: 输入文本

        Returns:
            List[Tuple[str, Optional[str]]]: 代码块列表，每个元素为(代码内容, 语言)
        """
        if not hasattr(self, 'code_block_pattern') or not hasattr(self, 'inline_code_pattern'):
            # 编译代码块正则表达式
            self.code_block_pattern = re.compile(r'```(\w*)\n([\s\S]*?)\n```')
            self.inline_code_pattern = re.compile(r'`([^`]+)`')

        code_blocks = []

        # 提取Markdown代码块
        for match in self.code_block_pattern.finditer(text):
            lang_str, code = match.groups()
            lang = None
            if lang_str:
                try:
                    # 使用更安全的方式获取枚举值
                    lang_str = lang_str.lower()
                    for code_lang in CodeLanguage:
                        if code_lang.value == lang_str:
                            lang = code_lang
                            break

                    if lang is None:
                        # 尝试映射常见语言别名
                        lang_mapping = {
                            'py': CodeLanguage.PYTHON,
                            'js': CodeLanguage.JAVASCRIPT,
                            'ts': CodeLanguage.TYPESCRIPT,
                            'cs': CodeLanguage.CSHARP,
                            'cpp': CodeLanguage.CPP,
                            'sh': CodeLanguage.SHELL,
                        }
                        lang = lang_mapping.get(lang_str.lower())
                except Exception as e:
                    self.logger.warning(f"处理代码语言类型时出错: {lang_str}, {e}")

            code_blocks.append((code, lang))

        # 提取内联代码
        for match in self.inline_code_pattern.finditer(text):
            code = match.group(1)
            if len(code) > 10:  # 只处理较长的内联代码
                code_blocks.append((code, None))

        return code_blocks

    def tokenize_text(self, text: str) -> List[str]:
        """
        对文本进行分词处理

        Args:
            text: 输入文本

        Returns:
            List[str]: 处理后的分词列表
        """
        if not text:
            return []

        try:
            # 检查是否需要处理代码块
            if hasattr(self, 'process_code_blocks') and self.process_code_blocks:
                code_blocks = self.extract_code_blocks(text)
                if code_blocks:
                    # 处理代码块
                    all_tokens = []
                    for code, lang in code_blocks:
                        code_lang = lang if isinstance(lang, CodeLanguage) else None
                        code_tokens = self.tokenize_code(code, code_lang)
                        all_tokens.extend(code_tokens)

                    # 处理剩余文本
                    # 移除代码块
                    text_without_code = self.code_block_pattern.sub('', text)
                    text_without_code = self.inline_code_pattern.sub('', text_without_code)

                    if text_without_code.strip():
                        # 处理非代码部分
                        text_tokens = self._tokenize_natural_language(text_without_code)
                        all_tokens.extend(text_tokens)

                    # 处理分词结果
                    processed_tokens = self.process_tokens(all_tokens)
                    return processed_tokens

            # 如果没有代码块或不处理代码块，直接处理文本
            return self._tokenize_natural_language(text)

        except Exception as e:
            self.logger.error(f"分词处理时出错: {e}")
            return []

    def _tokenize_natural_language(self, text: str) -> List[str]:
        """
        对自然语言文本进行分词

        Args:
            text: 输入文本

        Returns:
            List[str]: 分词结果
        """
        # 自动检测语言
        if hasattr(self, 'language') and hasattr(self, 'auto_detect_language'):
            if self.language == LanguageType.AUTO or self.auto_detect_language:
                detected_lang = self.detect_language(text)
            else:
                detected_lang = self.language
        else:
            # 兼容旧版本
            detected_lang = getattr(self, 'language', 'multilingual')

        # 根据语言选择分词方法
        if isinstance(detected_lang, LanguageType):
            # 新版本使用枚举
            if detected_lang == LanguageType.CHINESE:
                tokens = self.tokenize_chinese(text)
            elif detected_lang == LanguageType.ENGLISH:
                tokens = self.tokenize_english(text)
            elif detected_lang == LanguageType.JAPANESE:
                tokens = self.tokenize_japanese(text)
            elif detected_lang == LanguageType.KOREAN:
                tokens = self.tokenize_korean(text)
            elif detected_lang == LanguageType.THAI:
                tokens = self.tokenize_thai(text)
            elif detected_lang == LanguageType.ARABIC:
                tokens = self.tokenize_arabic(text)
            elif detected_lang == LanguageType.RUSSIAN:
                tokens = self.tokenize_russian(text)
            elif detected_lang == LanguageType.CODE:
                tokens = self.tokenize_code(text)
            else:  # 多语言或其他语言
                tokens = self.tokenize_multilingual(text)
        else:
            # 兼容旧版本的字符串类型
            if detected_lang == 'chinese':
                tokens = self.tokenize_chinese(text)
            elif detected_lang == 'english':
                tokens = self.tokenize_english(text)
            else:  # multilingual
                tokens = self.tokenize_multilingual(text)

        # 处理分词结果
        return self.process_tokens(tokens)

    def tokenize_batch(self, texts: List[str]) -> List[List[str]]:
        """
        批量处理文本分词

        Args:
            texts: 文本列表

        Returns:
            List[List[str]]: 分词结果列表
        """
        return [self.tokenize_text(text) for text in texts]

    def get_vocabulary(self, texts: List[str]) -> Set[str]:
        """
        获取文本集合的词汇表

        Args:
            texts: 文本列表

        Returns:
            Set[str]: 词汇表集合
        """
        vocabulary = set()
        for text in texts:
            tokens = self.tokenize_text(text)
            vocabulary.update(tokens)
        return vocabulary

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'preprocessing': {
            'language': 'auto',  # 自动检测语言
            'use_lemmatization': True,
            'use_stemming': False,
            'remove_stopwords': True,

            # 多语言支持
            'languages': {
                'enabled': ["chinese", "english", "japanese", "korean", "russian", "spanish", "french", "german"],
                'auto_detect': True,
                'fallback': "english"
            },

            # 代码块处理
            'code_blocks': {
                'enabled': True,
                'languages': ["python", "java", "javascript", "c", "cpp", "csharp"]
            },

            # 小语种支持
            'low_resource_languages': {
                'enabled': True,
                'languages': ["thai", "vietnamese", "hindi", "turkish", "indonesian", "malay"]
            }
        }
    }

    print("初始化分词器...")
    tokenizer = TextTokenizer(test_config)

    test_texts = [
        "这是一个中文测试文本",
        "This is an English test text",
        "混合Chinese和English的Text",
        "これは日本語のテストテキストです",
        "이것은 한국어 테스트 텍스트입니다",
        "Это тестовый текст на русском языке",
        "Este es un texto de prueba en español",
        "Ceci est un texte de test en français",
        "Dies ist ein deutscher Testtext",
        # 代码块测试
        "以下是Python代码示例：\n```python\ndef hello_world():\n    print('Hello, World!')\n\nhello_world()\n```",
        "JavaScript示例：\n```js\nfunction helloWorld() {\n    console.log('Hello, World!');\n}\n\nhelloWorld();\n```",
        # 内联代码测试
        "使用 `print('Hello')` 打印问候语"
    ]

    print("\n分词结果:")
    for text in test_texts:
        print("-" * 60)
        print(f"原文: {text}")

        # 检测语言
        if hasattr(tokenizer, 'detect_language'):
            detected_lang = tokenizer.detect_language(text)
            if isinstance(detected_lang, LanguageType):
                print(f"检测到的语言: {detected_lang.value}")
            else:
                print(f"检测到的语言: {detected_lang}")

        # 分词
        tokens = tokenizer.tokenize_text(text)
        print(f"分词结果 ({len(tokens)}个token): {tokens[:20]}")
        if len(tokens) > 20:
            print(f"... 还有 {len(tokens) - 20} 个token")
        print()

    # 测试代码块提取
    if hasattr(tokenizer, 'extract_code_blocks'):
        print("\n代码块提取测试:")
        for text in test_texts:
            code_blocks = tokenizer.extract_code_blocks(text)
            if code_blocks:
                print("-" * 60)
                print(f"原文: {text[:50]}...")
                print(f"提取到 {len(code_blocks)} 个代码块:")
                for i, (code, lang) in enumerate(code_blocks):
                    lang_str = lang.value if isinstance(lang, CodeLanguage) else "未知"
                    print(f"代码块 {i+1} (语言: {lang_str}):")
                    print(code[:100] + ("..." if len(code) > 100 else ""))
                print()

    # 测试词汇表生成
    vocabulary = tokenizer.get_vocabulary(test_texts)
    print("\n词汇表:")
    print(f"共 {len(vocabulary)} 个唯一token")
    print(f"部分词汇: {list(vocabulary)[:20]}")
    if len(vocabulary) > 20:
        print(f"... 还有 {len(vocabulary) - 20} 个token")