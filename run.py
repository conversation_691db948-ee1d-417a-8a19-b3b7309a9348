
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主运行脚本
提供项目的主入口点
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

from src.utils import setup_logger, ConfigUtils
from src.cli import CLI
from src.api import start_api

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='MD Vector Processor - 文档向量化处理工具'
    )
    
    parser.add_argument(
        '--mode',
        choices=['cli', 'api'],
        default='cli',
        help='运行模式：cli（命令行）或 api（API服务）'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        default='config/config.yaml',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--host',
        type=str,
        default='0.0.0.0',
        help='API服务主机地址（仅在api模式下有效）'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8000,
        help='API服务端口（仅在api模式下有效）'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--reload',
        action='store_true',
        help='启用自动重载（仅在api模式下有效）'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    try:
        # 加载配置
        config = ConfigUtils.load_config(args.config)
        if not config:
            print(f"错误：无法加载配置文件 {args.config}")
            return 1
        
        # 设置日志
        logger = setup_logger(config)
        
        # 根据模式运行
        if args.mode == 'cli':
            # 运行CLI模式
            cli = CLI()
            return cli.run()
        else:
            # 运行API模式
            logger.info(f"启动API服务 - http://{args.host}:{args.port}")
            start_api(
                config_path=args.config,
                host=args.host,
                port=args.port,
                reload=args.reload
            )
            return 0
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"错误：{e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())