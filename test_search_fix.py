#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试搜索功能修复效果
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_search_functionality():
    """测试搜索功能"""
    print("=== 测试搜索功能修复效果 ===")
    
    try:
        # 1. 检查索引文件
        index_dir = Path('data/indices')
        if not index_dir.exists():
            print("❌ 索引目录不存在")
            return False
        
        index_files = list(index_dir.glob('*.idx'))
        if not index_files:
            print("❌ 未找到索引文件")
            return False
        
        latest_index = max(index_files, key=lambda x: x.stat().st_mtime)
        print(f"✅ 找到索引文件: {latest_index}")
        
        # 2. 使用搜索诊断工具
        from src.utils.search_diagnostics import create_search_diagnostics
        
        config = {'storage': {'base_dir': 'data/vectors'}}
        diagnostics = create_search_diagnostics(config)
        
        print("\n--- 诊断搜索问题 ---")
        diagnosis = diagnostics.diagnose_search_issues(str(latest_index))
        
        print(f"索引状态: {diagnosis['index_status']}")
        print(f"元数据状态: {diagnosis['metadata_status']}")
        print(f"向量存储状态: {diagnosis['vector_storage_status']}")
        
        if diagnosis['mapping_issues']:
            print("⚠️ 发现映射问题:")
            for issue in diagnosis['mapping_issues']:
                print(f"  - {issue}")
        else:
            print("✅ 未发现映射问题")
        
        print("建议:")
        for rec in diagnosis['recommendations']:
            print(f"  - {rec}")
        
        # 3. 测试搜索功能
        print("\n--- 测试搜索功能 ---")
        test_result = diagnostics.test_search_functionality(str(latest_index), "测试查询")
        
        print(f"搜索成功: {test_result['success']}")
        print(f"结果数量: {test_result['results_count']}")
        print(f"包含内容: {test_result['has_content']}")
        
        if test_result['error']:
            print(f"❌ 错误: {test_result['error']}")
        
        if test_result['sample_results']:
            print("样本结果:")
            for i, result in enumerate(test_result['sample_results']):
                print(f"  结果 {i+1}:")
                print(f"    索引: {result.get('index')}")
                print(f"    距离: {result.get('distance')}")
                print(f"    有元数据: {result.get('has_metadata')}")
                print(f"    有文本: {result.get('has_text')}")
                if result.get('text_preview'):
                    print(f"    文本预览: {result['text_preview'][:50]}...")
                if result.get('error'):
                    print(f"    错误: {result['error']}")
        
        return test_result['success'] and test_result['has_content']
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_loading():
    """测试文档加载功能"""
    print("\n=== 测试文档加载功能 ===")
    
    try:
        from src.utils.document_loader import create_document_loader
        
        # 创建测试文件
        test_file = Path('test_document.txt')
        test_content = "这是一个测试文档。\n包含多行内容。\n用于测试文档加载功能。"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ 创建测试文件: {test_file}")
        
        # 测试文档加载器
        def progress_callback(current, total, message):
            print(f"进度: {current}/{total} - {message}")
        
        loader = create_document_loader(progress_callback)
        documents, doc_infos = loader.load_documents([str(test_file)], validate_integrity=True)
        
        if str(test_file) in documents:
            print("✅ 文档加载成功")
            doc_info = doc_infos[0]
            print(f"文件大小: {doc_info.size} 字节")
            print(f"内容长度: {doc_info.content_length} 字符")
            print(f"检测编码: {doc_info.encoding}")
            print(f"加载时间: {doc_info.load_time:.3f} 秒")
            print(f"状态: {doc_info.status.value}")
            
            # 验证内容
            loaded_content = documents[str(test_file)]
            if loaded_content.strip() == test_content.strip():
                print("✅ 内容验证成功")
                success = True
            else:
                print("❌ 内容验证失败")
                success = False
        else:
            print("❌ 文档加载失败")
            if doc_infos:
                print(f"错误: {doc_infos[0].error_message}")
            success = False
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
            print(f"🗑️ 清理测试文件: {test_file}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_local_model_manager():
    """测试本地模型管理器"""
    print("\n=== 测试本地模型管理器 ===")
    
    try:
        from src.utils.local_model_manager import get_local_model_manager
        
        manager = get_local_model_manager()
        
        print(f"✅ 本地模型管理器初始化成功")
        print(f"配置路径: {manager.config_path}")
        
        # 获取可用模型
        all_models = list(manager.models.values())
        print(f"总模型数: {len(all_models)}")
        
        embedding_models = manager.get_available_models(supports_embedding=True)
        print(f"支持向量化的模型: {len(embedding_models)}")
        
        chat_models = manager.get_available_models(supports_chat=True)
        print(f"支持对话的模型: {len(chat_models)}")
        
        if embedding_models:
            print("向量化模型列表:")
            for model in embedding_models[:3]:  # 只显示前3个
                print(f"  - {model.name} ({model.type}) - 可用: {model.is_available}")
        
        if chat_models:
            print("对话模型列表:")
            for model in chat_models[:3]:  # 只显示前3个
                print(f"  - {model.name} ({model.type}) - 可用: {model.is_available}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复效果...\n")
    
    results = []
    
    # 测试文档加载
    results.append(("文档加载", test_document_loading()))
    
    # 测试本地模型管理器
    results.append(("本地模型管理器", test_local_model_manager()))
    
    # 测试搜索功能
    results.append(("搜索功能", test_search_functionality()))
    
    # 总结结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！修复效果良好。")
    else:
        print("⚠️ 部分测试失败，需要进一步修复。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
