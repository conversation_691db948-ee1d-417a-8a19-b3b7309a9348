#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量化小部件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
    QFileDialog, QProgressBar, QSpinBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from ..i18n import Translator

class VectorizeWidget(QWidget):
    """向量化小部件"""

    def __init__(self, translator: Translator):
        """
        初始化向量化小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("vectorizeWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(self.translator.get_text("vectorize_data", "向量化数据"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 创建标签页控件
        tab_widget = QTabWidget()

        # 创建"文本向量化"标签页
        text_tab = QWidget()
        text_layout = QVBoxLayout(text_tab)

        # 创建表单布局
        form_layout = QFormLayout()

        # 添加表单字段
        self.model_combo = QComboBox()

        # 加载模型选项
        self._load_model_options(self.model_combo)

        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "text",
            "csv",
            "json",
            "excel",
            "markdown",
            "code"
        ])

        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 1000)
        self.batch_size_spin.setValue(32)

        # 添加字段到表单
        form_layout.addRow(self.translator.get_text("model", "模型:"), self.model_combo)
        form_layout.addRow(self.translator.get_text("language", "语言:"), self.language_combo)
        form_layout.addRow(self.translator.get_text("batch_size", "批处理大小:"), self.batch_size_spin)

        text_layout.addLayout(form_layout)

        # 添加文本输入区域
        text_input_group = QGroupBox(self.translator.get_text("input_text", "输入文本"))
        text_input_layout = QVBoxLayout(text_input_group)

        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText(self.translator.get_text("enter_text_here", "在此输入文本..."))

        text_input_layout.addWidget(self.text_input)

        text_layout.addWidget(text_input_group)

        # 添加按钮
        buttons_layout = QHBoxLayout()

        self.load_text_button = QPushButton(self.translator.get_text("load_file", "加载文件"))
        self.load_text_button.setObjectName("secondaryButton")
        self.load_text_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.vectorize_text_button = QPushButton(self.translator.get_text("vectorize", "向量化"))
        self.vectorize_text_button.setObjectName("primaryButton")
        self.vectorize_text_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.load_text_button)
        buttons_layout.addWidget(self.vectorize_text_button)

        text_layout.addLayout(buttons_layout)

        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")

        text_layout.addWidget(self.progress_bar)

        # 创建"文件向量化"标签页
        file_tab = QWidget()
        file_layout = QVBoxLayout(file_tab)

        # 创建表单布局
        file_form_layout = QFormLayout()

        # 添加表单字段
        self.file_model_combo = QComboBox()

        # 加载模型选项
        self._load_model_options(self.file_model_combo)

        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems([
            "auto",
            "english",
            "chinese",
            "japanese",
            "korean",
            "russian",
            "spanish",
            "french",
            "german",
            "multilingual"
        ])

        self.file_batch_size_spin = QSpinBox()
        self.file_batch_size_spin.setRange(1, 1000)
        self.file_batch_size_spin.setValue(32)

        # 添加字段到表单
        file_form_layout.addRow(self.translator.get_text("model", "模型:"), self.file_model_combo)
        file_form_layout.addRow(self.translator.get_text("file_type", "文件类型:"), self.file_type_combo)
        file_form_layout.addRow(self.translator.get_text("batch_size", "批处理大小:"), self.file_batch_size_spin)

        file_layout.addLayout(file_form_layout)

        # 添加文件选择区域
        file_input_group = QGroupBox(self.translator.get_text("select_files", "选择文件"))
        file_input_layout = QVBoxLayout(file_input_group)

        file_buttons_layout = QHBoxLayout()

        self.select_files_button = QPushButton(self.translator.get_text("select_files", "选择文件"))
        self.select_files_button.setObjectName("secondaryButton")
        self.select_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.select_folder_button = QPushButton(self.translator.get_text("select_folder", "选择文件夹"))
        self.select_folder_button.setObjectName("secondaryButton")
        self.select_folder_button.setCursor(Qt.CursorShape.PointingHandCursor)

        file_buttons_layout.addWidget(self.select_files_button)
        file_buttons_layout.addWidget(self.select_folder_button)

        file_input_layout.addLayout(file_buttons_layout)

        self.file_list = QTextEdit()
        self.file_list.setReadOnly(True)
        self.file_list.setPlaceholderText(self.translator.get_text("selected_files_shown_here", "选择的文件将显示在这里..."))

        file_input_layout.addWidget(self.file_list)

        file_layout.addWidget(file_input_group)

        # 添加按钮
        file_process_layout = QHBoxLayout()

        self.clear_files_button = QPushButton(self.translator.get_text("clear", "清除"))
        self.clear_files_button.setObjectName("secondaryButton")
        self.clear_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.vectorize_files_button = QPushButton(self.translator.get_text("vectorize", "向量化"))
        self.vectorize_files_button.setObjectName("primaryButton")
        self.vectorize_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        file_process_layout.addWidget(self.clear_files_button)
        file_process_layout.addWidget(self.vectorize_files_button)

        file_layout.addLayout(file_process_layout)

        # 添加进度条
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setRange(0, 100)
        self.file_progress_bar.setValue(0)
        self.file_progress_bar.setTextVisible(True)
        self.file_progress_bar.setFormat("%p%")

        file_layout.addWidget(self.file_progress_bar)

        # 添加标签页到标签页控件
        tab_widget.addTab(text_tab, self.translator.get_text("text_vectorization", "文本向量化"))
        tab_widget.addTab(file_tab, self.translator.get_text("file_vectorization", "文件向量化"))

        main_layout.addWidget(tab_widget)

        # 连接信号
        self._connect_signals()

    def _load_model_options(self, combo_box: QComboBox):
        """
        加载模型选项到下拉框

        Args:
            combo_box: 目标下拉框
        """
        try:
            # 添加默认模型
            default_models = [
                "multilingual-minilm",
                "multilingual-mpnet",
                "bert-base-multilingual",
                "xlm-roberta",
                "medical-bert",
                "legal-roberta",
                "technical-xlnet",
                "code-bert"
            ]
            combo_box.addItems(default_models)

            # 加载本地模型
            from ...utils.local_model_manager import get_local_model_manager
            model_manager = get_local_model_manager()

            # 获取支持向量化的本地模型
            local_models = model_manager.get_available_models(supports_embedding=True)

            if local_models:
                # 添加分隔符
                combo_box.insertSeparator(combo_box.count())
                combo_box.addItem("--- 本地模型 ---")

                # 添加本地模型
                for model in local_models:
                    display_name = f"{model.type}:{model.model_id}"
                    combo_box.addItem(display_name)
                    # 存储模型对象作为用户数据
                    combo_box.setItemData(combo_box.count() - 1, model)

                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"加载了 {len(local_models)} 个本地向量化模型")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载本地模型时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _connect_signals(self):
        """连接信号和槽"""
        self.load_text_button.clicked.connect(self._on_load_text)
        self.vectorize_text_button.clicked.connect(self._on_vectorize_text)
        self.select_files_button.clicked.connect(self._on_select_files)
        self.select_folder_button.clicked.connect(self._on_select_folder)
        self.clear_files_button.clicked.connect(self._on_clear_files)
        self.vectorize_files_button.clicked.connect(self._on_vectorize_files)

    def _on_load_text(self):
        """加载文本文件按钮点击处理"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.translator.get_text("load_text_file", "加载文本文件"),
            "",
            "All Files (*);;Text Files (*.txt);;Excel Files (*.xlsx *.xls);;Markdown Files (*.md *.markdown);;CSV Files (*.csv);;JSON Files (*.json)"
        )

        if file_path:
            # 导入日志模块
            import logging
            logger = logging.getLogger(__name__)

            # 使用文档加载器进行完整性检查
            try:
                from ...utils.document_loader import create_document_loader

                # 创建进度回调
                def progress_callback(current, total, message):
                    logger.info(f"加载进度: {current}/{total} - {message}")

                loader = create_document_loader(progress_callback)
                documents, doc_infos = loader.load_documents([file_path], validate_integrity=True)

                if file_path in documents:
                    text = documents[file_path]
                    self.text_input.setText(text)

                    # 显示加载信息
                    doc_info = doc_infos[0]
                    logger.info(f"=== 文档加载完成 ===")
                    logger.info(f"文件路径: {file_path}")
                    logger.info(f"文件大小: {doc_info.size} 字节")
                    logger.info(f"内容长度: {doc_info.content_length} 字符")
                    logger.info(f"检测编码: {doc_info.encoding}")
                    logger.info(f"加载时间: {doc_info.load_time:.3f} 秒")
                    logger.info(f"内容哈希: {doc_info.content_hash}")
                    logger.info(f"加载状态: {doc_info.status.value}")

                    # 根据文件类型设置语言选择
                    from pathlib import Path
                    path = Path(file_path)
                    file_ext = path.suffix.lower()

                    if file_ext in ['.xlsx', '.xls', '.xlsm']:
                        self.language_combo.setCurrentText("excel")
                    elif file_ext in ['.md', '.markdown']:
                        self.language_combo.setCurrentText("markdown")
                        # 对于Markdown文件，自动启用分块
                        if doc_info.content_length > 2000:
                            self.chunk_size_spin.setValue(1000)  # 设置合适的分块大小
                            self.overlap_spin.setValue(200)      # 设置重叠大小
                    elif file_ext == '.csv':
                        self.language_combo.setCurrentText("csv")
                    elif file_ext == '.json':
                        self.language_combo.setCurrentText("json")
                    else:
                        self.language_combo.setCurrentText("text")

                    return

                else:
                    # 加载失败，使用原有的加载方法作为备用
                    doc_info = doc_infos[0] if doc_infos else None
                    error_msg = doc_info.error_message if doc_info else "未知错误"
                    logger.warning(f"文档加载器加载失败: {error_msg}，尝试使用备用方法")

            except Exception as e:
                logger.warning(f"文档加载器出错: {e}，使用备用加载方法")

            # 备用加载方法（原有逻辑）
            # 检查文件扩展名
            from pathlib import Path
            path = Path(file_path)
            file_ext = path.suffix.lower()
            text = None

            # 处理Excel文件
            if file_ext in ['.xlsx', '.xls', '.xlsm']:
                try:
                    # 设置语言类型为excel
                    self.language_combo.setCurrentText("excel")

                    # 导入pandas
                    import pandas as pd
                    # 读取Excel文件
                    df = pd.read_excel(file_path)
                    # 转换为文本格式
                    text = df.to_string(index=False)
                    logger.info(f"成功使用pandas读取Excel文件: {file_path}")
                except Exception as e:
                    logger.error(f"使用pandas读取Excel文件失败: {file_path}, {e}")
                    # 如果pandas读取失败，尝试其他方法

            # 处理Markdown文件
            elif file_ext in ['.md', '.markdown']:
                try:
                    # 设置语言类型为markdown
                    self.language_combo.setCurrentText("markdown")

                    # 尝试不同的编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                    for encoding in encodings:
                        try:
                            with open(file_path, 'r', encoding=encoding) as f:
                                raw_content = f.read()

                            # 如果有frontmatter库，尝试解析frontmatter
                            try:
                                import frontmatter
                                post = frontmatter.loads(raw_content)
                                # 获取内容部分
                                text = post.content
                            except ImportError:
                                # 如果没有frontmatter库，直接使用原始内容
                                text = raw_content

                            logger.info(f"成功使用 {encoding} 编码读取Markdown文件: {file_path}")
                            break
                        except UnicodeDecodeError:
                            logger.warning(f"使用 {encoding} 编码读取Markdown文件失败: {file_path}")
                except Exception as e:
                    logger.error(f"读取Markdown文件失败: {file_path}, {e}")

            # 处理CSV文件
            elif file_ext == '.csv':
                try:
                    # 设置语言类型为csv
                    self.language_combo.setCurrentText("csv")

                    # 导入pandas
                    import pandas as pd
                    # 尝试不同的分隔符
                    for sep in [',', ';', '\t']:
                        try:
                            df = pd.read_csv(file_path, sep=sep)
                            # 转换为文本格式
                            text = df.to_string(index=False)
                            logger.info(f"成功使用pandas读取CSV文件(分隔符: {sep}): {file_path}")
                            break
                        except Exception:
                            continue
                except Exception as e:
                    logger.error(f"使用pandas读取CSV文件失败: {file_path}, {e}")

            # 处理JSON文件
            elif file_ext == '.json':
                try:
                    # 设置语言类型为json
                    self.language_combo.setCurrentText("json")

                    # 导入json
                    import json
                    # 尝试不同的编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                    for encoding in encodings:
                        try:
                            with open(file_path, 'r', encoding=encoding) as f:
                                # 加载JSON
                                data = json.load(f)
                                # 格式化输出
                                text = json.dumps(data, indent=2, ensure_ascii=False)
                            logger.info(f"成功使用 {encoding} 编码读取JSON文件: {file_path}")
                            break
                        except UnicodeDecodeError:
                            logger.warning(f"使用 {encoding} 编码读取JSON文件失败: {file_path}")
                except Exception as e:
                    logger.error(f"读取JSON文件失败: {file_path}, {e}")

            # 处理其他文本文件
            if text is None:
                # 设置语言类型为text
                self.language_combo.setCurrentText("text")

                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            text = f.read()
                        logger.info(f"成功使用 {encoding} 编码读取文件: {file_path}")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用 {encoding} 编码读取文件失败: {file_path}")

                # 如果所有编码都失败，尝试二进制模式读取
                if text is None:
                    try:
                        with open(file_path, 'rb') as f:
                            binary_data = f.read()
                            # 尝试使用 latin-1 编码（可以处理任何字节序列）
                            text = binary_data.decode('latin-1')
                        logger.info(f"使用二进制模式读取文件: {file_path}")
                    except Exception as e:
                        logger.error(f"读取文件失败: {file_path}, {e}")
                        from PyQt6.QtWidgets import QMessageBox
                        QMessageBox.critical(
                            self,
                            "加载文件失败",
                            f"无法读取文件: {file_path}\n错误: {str(e)}"
                        )
                        return

            # 设置文本
            if text is not None:
                self.text_input.setText(text)
            else:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "加载文件失败",
                    f"无法读取文件: {file_path}\n尝试了多种编码但都失败了。"
                )

    def _on_vectorize_text(self):
        """向量化文本按钮点击处理"""
        # 获取文本内容
        text = self.text_input.toPlainText().strip()
        if not text:
            return

        # 获取选项
        model_name = self.model_combo.currentText()
        file_type = self.language_combo.currentText()
        batch_size = self.batch_size_spin.value()

        # 检查是否是本地模型
        local_model = self.model_combo.itemData(self.model_combo.currentIndex())
        if local_model:
            # 使用本地模型
            model_name = f"local:{local_model.name}"

        # 如果是Excel格式，尝试解析为表格
        if file_type == "excel" and text:
            try:
                # 导入pandas
                import pandas as pd
                import io

                # 尝试将文本解析为CSV格式（Excel文本通常是制表符或逗号分隔）
                # 尝试不同的分隔符
                for sep in ['\t', ',', ';']:
                    try:
                        df = pd.read_csv(io.StringIO(text), sep=sep)
                        # 转换为更易读的格式
                        text = df.to_string(index=False)
                        break
                    except Exception:
                        continue

                # 更新文本框
                self.text_input.setText(text)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"解析Excel文本时出错: {e}")
                # 继续使用原始文本

        # 设置进度条
        self.progress_bar.setValue(0)

        try:
            # 导入必要的模块
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager, VectorStore
            import numpy as np
            import logging
            import os
            import time
            import hashlib

            # 创建日志记录器
            logger = logging.getLogger(__name__)

            # 确保数据目录存在
            os.makedirs('data/vectors/metadata', exist_ok=True)

            # 创建向量存储和元数据管理器
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            metadata_manager = MetadataManager(storage_config)

            # 更新进度条
            self.progress_bar.setValue(10)

            # 创建文本嵌入器配置
            vectorization_config = {
                'vectorization': {
                    'model_name': model_name,
                    'vector_dimension': 384,
                    'batch_size': batch_size,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }

            # 检查是否使用Ollama模型
            if model_name.startswith("ollama:"):
                # 获取Ollama模型名称
                ollama_model_name = model_name.split(":", 1)[1]

                # 获取主窗口配置
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    # 检查是否有Ollama配置
                    local_models = main_window.config.get('local_models', {})
                    ollama_config = local_models.get('ollama', {})

                    if ollama_config.get('enabled', False):
                        # 添加Ollama配置
                        if 'local_models' not in vectorization_config:
                            vectorization_config['local_models'] = {}

                        vectorization_config['local_models']['ollama'] = {
                            'enabled': True,
                            'api_url': ollama_config.get('api_url', 'http://localhost:11434/api'),
                            'default_model': ollama_model_name,
                            'models': []
                        }

                        # 添加模型参数
                        for model in ollama_config.get('models', []):
                            if model.get('name') == ollama_model_name:
                                vectorization_config['local_models']['ollama']['models'] = [model]
                                break

            embedder = TextEmbedding(vectorization_config)
            logger.info(f"成功创建文本嵌入器，使用模型: {model_name}")

            # 更新进度条
            self.progress_bar.setValue(30)

            # 导入文本切片工具
            from src.utils.helpers import TextUtils

            # 将文本分割成较小的块
            chunk_size = 300  # 使用更合适的块大小，平衡语义完整性和精确匹配
            overlap = 50      # 设置较大的重叠大小，确保上下文连贯性

            # 对于非常大的文本，先进行预处理
            if len(text) > 1000000:  # 如果文本超过1MB
                logger.info(f"检测到大文本，长度: {len(text)}，进行预处理...")
                # 先按段落分割，避免一次性处理整个文本
                paragraphs = text.split('\n\n')
                chunks = []
                for i, para in enumerate(paragraphs):
                    if len(para) > chunk_size:
                        # 对大段落进行分割
                        para_chunks = TextUtils.split_text(para, chunk_size, overlap)
                        chunks.extend(para_chunks)
                    else:
                        chunks.append(para)
                    # 限制最大块数
                    if len(chunks) >= 1000:
                        logger.warning(f"达到最大块数限制(1000)，截断文本...")
                        break
            else:
                # 对于正常大小的文本，直接分割
                chunks = TextUtils.split_text(text, chunk_size, overlap)

            logger.info(f"文本已分割为 {len(chunks)} 个块")

            # 更新进度条
            self.progress_bar.setValue(40)

            # 处理每个文本块
            all_vectors = []
            all_doc_ids = []

            for i, chunk in enumerate(chunks):
                # 生成文档ID (使用文本块的哈希值)
                chunk_id = int(hashlib.md5((text[:50] + chunk[:50] + str(i)).encode()).hexdigest(), 16) % (10 ** 10)
                all_doc_ids.append(chunk_id)

                # 向量化文本块
                vector = embedder.encode_text(chunk)
                all_vectors.append(vector)

                # 更新进度条
                progress = 40 + int(20 * (i + 1) / len(chunks))
                self.progress_bar.setValue(progress)

            # 将所有向量合并为一个数组
            vectors_array = np.vstack(all_vectors)
            doc_ids_array = np.array(all_doc_ids)

            logger.info(f"成功向量化 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")

            # 更新进度条
            self.progress_bar.setValue(60)

            # 存储向量
            success = vector_store.store_vectors(vectors_array, doc_ids_array)
            if success:
                logger.info(f"成功存储 {len(chunks)} 个向量，ID: {all_doc_ids}")
            else:
                logger.error("存储向量失败")

            # 将向量添加到索引中
            try:
                from src.indexer import IndexBuilder
                from pathlib import Path

                # 获取当前加载的索引
                index_path = Path('data/indices')
                index_files = list(index_path.glob('*.idx'))

                if index_files:
                    # 使用最新的索引文件
                    latest_index = max(index_files, key=lambda x: x.stat().st_mtime)
                    logger.info(f"找到索引文件: {latest_index}")

                    # 创建索引构建器 - 使用简单的Flat索引避免训练问题
                    config = {
                        'indexing': {
                            'index_type': 'flat',
                            'metric': 'cosine',
                            'quantization': 'none'  # 禁用量化
                        }
                    }
                    builder = IndexBuilder(config)

                    # 加载索引
                    if builder.load_index(latest_index):
                        logger.info(f"成功加载索引: {latest_index}")

                        # 添加向量到索引
                        try:
                            # 检查索引类型
                            index_type = type(builder.index).__name__
                            logger.info(f"索引类型: {index_type}")

                            # 根据索引类型选择添加方法
                            if index_type == 'IndexFlatIP' or index_type == 'IndexFlatL2':
                                # 对于Flat索引，使用add方法
                                builder.index.add(vectors_array)
                                logger.info(f"成功将向量添加到索引（使用add方法）")
                                success = True
                                # 更新索引状态
                                builder.total_vectors += len(vectors_array)
                            else:
                                # 对于其他索引类型，尝试使用add_vectors方法
                                success = builder.add_vectors(vectors_array, doc_ids_array)
                                logger.info(f"成功将向量添加到索引，ID: {all_doc_ids}")
                                # 更新索引状态
                                builder.total_vectors += len(vectors_array)

                            # 保存索引
                            if builder.save_index(latest_index):
                                logger.info(f"成功保存索引: {latest_index}")
                            else:
                                logger.error(f"保存索引失败: {latest_index}")
                        except Exception as e:
                            logger.error(f"将向量添加到索引时出错: {e}")
                            import traceback
                            logger.error(traceback.format_exc())
                    else:
                        logger.error(f"加载索引失败: {latest_index}")
                else:
                    logger.warning("未找到索引文件，无法将向量添加到索引")
            except Exception as e:
                logger.error(f"将向量添加到索引时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

            # 更新进度条
            self.progress_bar.setValue(80)

            # 存储元数据
            # 为每个文本块存储元数据
            for i, (chunk_id, chunk) in enumerate(zip(all_doc_ids, chunks)):
                metadata = {
                    'text': chunk,
                    'original_text_start': i * (chunk_size - overlap) if i > 0 else 0,
                    'original_text_end': min((i + 1) * chunk_size - i * overlap, len(text)),
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'file_type': file_type,
                    'model': model_name,
                    'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'vector_dimension': all_vectors[i].shape[0]
                }

                success = metadata_manager.store_metadata(chunk_id, metadata)
                if success:
                    logger.info(f"成功存储元数据，ID: {chunk_id}, 块 {i+1}/{len(chunks)}")
                else:
                    logger.error(f"存储元数据失败，块 {i+1}/{len(chunks)}")

            # 更新进度条
            self.progress_bar.setValue(100)

            # 清理资源
            embedder.cleanup()

            # 通知状态管理器
            try:
                from ..state_manager import get_state_manager
                state_manager = get_state_manager()
                if latest_index:
                    state_manager.notify_vectors_added(str(latest_index), len(chunks))
            except Exception as e:
                logger.warning(f"通知状态管理器时出错: {e}")

            # 显示成功消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "向量化成功",
                f"成功向量化文本并保存到数据库\n共 {len(chunks)} 个文本块\n文档ID: {all_doc_ids[0]}...等"
            )

        except Exception as e:
            # 记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"向量化文本时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "向量化失败",
                f"向量化文本时出错: {str(e)}"
            )

            # 重置进度条
            self.progress_bar.setValue(0)

    def _on_select_files(self):
        """选择文件按钮点击处理"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            self.translator.get_text("select_files", "选择文件"),
            "",
            "All Files (*)"
        )

        if file_paths:
            current_text = self.file_list.toPlainText()
            if current_text:
                self.file_list.setText(current_text + "\n" + "\n".join(file_paths))
            else:
                self.file_list.setText("\n".join(file_paths))

    def _on_select_folder(self):
        """选择文件夹按钮点击处理"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            self.translator.get_text("select_folder", "选择文件夹"),
            ""
        )

        if folder_path:
            current_text = self.file_list.toPlainText()
            if current_text:
                self.file_list.setText(current_text + "\n" + folder_path)
            else:
                self.file_list.setText(folder_path)

    def _on_clear_files(self):
        """清除文件按钮点击处理"""
        self.file_list.clear()

    def _on_vectorize_files(self):
        """向量化文件按钮点击处理"""
        # 获取文件列表
        file_paths = self.file_list.toPlainText().strip().split('\n')
        if not file_paths or not file_paths[0]:
            return

        # 获取选项
        model_name = self.file_model_combo.currentText()
        language = self.file_type_combo.currentText()
        batch_size = self.file_batch_size_spin.value()

        # 检查是否是本地模型
        local_model = self.file_model_combo.itemData(self.file_model_combo.currentIndex())
        if local_model:
            # 使用本地模型
            model_name = f"local:{local_model.name}"

        # 设置进度条
        self.file_progress_bar.setValue(0)

        try:
            # 导入必要的模块
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager, VectorStore
            import numpy as np
            import logging
            import os
            import time
            import hashlib
            from pathlib import Path

            # 创建日志记录器
            logger = logging.getLogger(__name__)

            # 确保数据目录存在
            os.makedirs('data/vectors/metadata', exist_ok=True)

            # 创建向量存储和元数据管理器
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            metadata_manager = MetadataManager(storage_config)

            # 更新进度条
            self.file_progress_bar.setValue(5)

            # 创建文本嵌入器配置
            vectorization_config = {
                'vectorization': {
                    'model_name': model_name,
                    'vector_dimension': 384,
                    'batch_size': batch_size,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }

            # 检查是否使用Ollama模型
            if model_name.startswith("ollama:"):
                # 获取Ollama模型名称
                ollama_model_name = model_name.split(":", 1)[1]

                # 获取主窗口配置
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    # 检查是否有Ollama配置
                    local_models = main_window.config.get('local_models', {})
                    ollama_config = local_models.get('ollama', {})

                    if ollama_config.get('enabled', False):
                        # 添加Ollama配置
                        if 'local_models' not in vectorization_config:
                            vectorization_config['local_models'] = {}

                        vectorization_config['local_models']['ollama'] = {
                            'enabled': True,
                            'api_url': ollama_config.get('api_url', 'http://localhost:11434/api'),
                            'default_model': ollama_model_name,
                            'models': []
                        }

                        # 添加模型参数
                        for model in ollama_config.get('models', []):
                            if model.get('name') == ollama_model_name:
                                vectorization_config['local_models']['ollama']['models'] = [model]
                                break

            embedder = TextEmbedding(vectorization_config)
            logger.info(f"成功创建文本嵌入器，使用模型: {model_name}")

            # 更新进度条
            self.file_progress_bar.setValue(10)

            # 处理每个文件
            total_files = len(file_paths)
            processed_files = 0

            for file_path in file_paths:
                file_path = file_path.strip()
                if not file_path:
                    continue

                path = Path(file_path)
                if not path.exists():
                    logger.warning(f"文件不存在: {file_path}")
                    continue

                # 如果是目录，跳过
                if path.is_dir():
                    logger.info(f"跳过目录: {file_path}")
                    continue

                try:
                    # 检查文件扩展名
                    file_ext = path.suffix.lower()
                    text = None

                    # 处理Excel文件
                    if file_ext in ['.xlsx', '.xls', '.xlsm']:
                        try:
                            import pandas as pd
                            # 读取Excel文件
                            df = pd.read_excel(file_path)
                            # 转换为文本格式
                            text = df.to_string(index=False)
                            logger.info(f"成功使用pandas读取Excel文件: {file_path}")
                        except Exception as e:
                            logger.error(f"使用pandas读取Excel文件失败: {file_path}, {e}")
                            # 如果pandas读取失败，尝试其他方法

                    # 处理Markdown文件
                    elif file_ext in ['.md', '.markdown']:
                        try:
                            # 尝试不同的编码
                            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                            for encoding in encodings:
                                try:
                                    with open(file_path, 'r', encoding=encoding) as f:
                                        raw_content = f.read()

                                    # 如果有frontmatter库，尝试解析frontmatter
                                    try:
                                        import frontmatter
                                        post = frontmatter.loads(raw_content)
                                        # 获取内容部分
                                        text = post.content
                                    except ImportError:
                                        # 如果没有frontmatter库，直接使用原始内容
                                        text = raw_content

                                    logger.info(f"成功使用 {encoding} 编码读取Markdown文件: {file_path}")
                                    break
                                except UnicodeDecodeError:
                                    logger.warning(f"使用 {encoding} 编码读取Markdown文件失败: {file_path}")
                        except Exception as e:
                            logger.error(f"读取Markdown文件失败: {file_path}, {e}")

                    # 处理其他文本文件
                    if text is None:
                        # 尝试不同的编码
                        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']

                        for encoding in encodings:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    text = f.read()
                                logger.info(f"成功使用 {encoding} 编码读取文件: {file_path}")
                                break
                            except UnicodeDecodeError:
                                logger.warning(f"使用 {encoding} 编码读取文件失败: {file_path}")

                        # 如果所有编码都失败，尝试二进制模式读取
                        if text is None:
                            try:
                                with open(file_path, 'rb') as f:
                                    binary_data = f.read()
                                    # 尝试使用 latin-1 编码（可以处理任何字节序列）
                                    text = binary_data.decode('latin-1')
                                logger.info(f"使用二进制模式读取文件: {file_path}")
                            except Exception as e:
                                logger.error(f"读取文件失败: {file_path}, {e}")
                                continue

                    # 导入文本切片工具
                    from src.utils.helpers import TextUtils

                    # 将文本分割成较小的块
                    chunk_size = 300  # 使用更合适的块大小，平衡语义完整性和精确匹配
                    overlap = 50      # 设置较大的重叠大小，确保上下文连贯性

                    # 对于非常大的文本，先进行预处理
                    if len(text) > 1000000:  # 如果文本超过1MB
                        logger.info(f"检测到大文本文件，长度: {len(text)}，进行预处理...")
                        # 先按段落分割，避免一次性处理整个文本
                        paragraphs = text.split('\n\n')
                        chunks = []
                        for i, para in enumerate(paragraphs):
                            if len(para) > chunk_size:
                                # 对大段落进行分割
                                para_chunks = TextUtils.split_text(para, chunk_size, overlap)
                                chunks.extend(para_chunks)
                            else:
                                chunks.append(para)
                            # 限制最大块数
                            if len(chunks) >= 1000:
                                logger.warning(f"达到最大块数限制(1000)，截断文件...")
                                break
                    else:
                        # 对于正常大小的文本，直接分割
                        chunks = TextUtils.split_text(text, chunk_size, overlap)

                    logger.info(f"文件 {file_path} 已分割为 {len(chunks)} 个块")

                    # 处理每个文本块
                    file_vectors = []
                    file_doc_ids = []

                    for i, chunk in enumerate(chunks):
                        # 生成文档ID (使用文本块的哈希值)
                        chunk_id = int(hashlib.md5((file_path + chunk[:50] + str(i)).encode()).hexdigest(), 16) % (10 ** 10)
                        file_doc_ids.append(chunk_id)

                        # 向量化文本块
                        vector = embedder.encode_text(chunk)
                        file_vectors.append(vector)

                    # 将所有向量合并为一个数组
                    vectors_array = np.vstack(file_vectors)
                    doc_ids_array = np.array(file_doc_ids)

                    logger.info(f"成功向量化文件: {file_path}，共 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")

                    # 存储向量
                    success = vector_store.store_vectors(vectors_array, doc_ids_array)
                    if success:
                        logger.info(f"成功存储 {len(chunks)} 个向量，ID: {file_doc_ids}")
                    else:
                        logger.error(f"存储向量失败: {file_path}")

                    # 将向量添加到索引中
                    try:
                        from src.indexer import IndexBuilder
                        from pathlib import Path

                        # 获取当前加载的索引
                        index_path = Path('data/indices')
                        index_files = list(index_path.glob('*.idx'))

                        if index_files:
                            # 使用最新的索引文件
                            latest_index = max(index_files, key=lambda x: x.stat().st_mtime)
                            logger.info(f"找到索引文件: {latest_index}")

                            # 创建索引构建器 - 使用简单的Flat索引避免训练问题
                            config = {
                                'indexing': {
                                    'index_type': 'flat',
                                    'metric': 'cosine',
                                    'quantization': 'none'  # 禁用量化
                                }
                            }
                            builder = IndexBuilder(config)

                            # 加载索引
                            if builder.load_index(latest_index):
                                logger.info(f"成功加载索引: {latest_index}")

                                # 添加向量到索引
                                try:
                                    # 检查索引类型
                                    index_type = type(builder.index).__name__
                                    logger.info(f"索引类型: {index_type}")

                                    # 根据索引类型选择添加方法
                                    if index_type == 'IndexFlatIP' or index_type == 'IndexFlatL2':
                                        # 对于Flat索引，使用add方法
                                        builder.index.add(vectors_array)
                                        logger.info(f"成功将向量添加到索引（使用add方法）")
                                        success = True
                                    else:
                                        # 对于其他索引类型，使用add_vectors方法并传入元数据管理器
                                        success = builder.add_vectors(vectors_array, doc_ids_array, metadata_manager)
                                        logger.info(f"成功将向量添加到索引，ID: {file_doc_ids}")

                                    # 更新索引状态
                                    builder.total_vectors += 1

                                    # 保存索引
                                    if builder.save_index(latest_index):
                                        logger.info(f"成功保存索引: {latest_index}")
                                    else:
                                        logger.error(f"保存索引失败: {latest_index}")
                                except Exception as e:
                                    logger.error(f"将向量添加到索引时出错: {e}")
                                    import traceback
                                    logger.error(traceback.format_exc())
                            else:
                                logger.error(f"加载索引失败: {latest_index}")
                        else:
                            logger.warning("未找到索引文件，无法将向量添加到索引")
                    except Exception as e:
                        logger.error(f"将向量添加到索引时出错: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

                    # 存储元数据
                    # 为每个文本块存储元数据
                    for i, (chunk_id, chunk) in enumerate(zip(file_doc_ids, chunks)):
                        metadata = {
                            'text': chunk,
                            'original_text_start': i * (chunk_size - overlap) if i > 0 else 0,
                            'original_text_end': min((i + 1) * chunk_size - i * overlap, len(text)),
                            'chunk_index': i,
                            'total_chunks': len(chunks),
                            'filename': path.name,
                            'filepath': str(path),
                            'file_type': path.suffix,
                            'language': language,
                            'model': model_name,
                            'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                            'file_size': path.stat().st_size,
                            'vector_dimension': file_vectors[i].shape[0]
                        }

                        success = metadata_manager.store_metadata(chunk_id, metadata)
                        if success:
                            logger.info(f"成功存储元数据，ID: {chunk_id}, 块 {i+1}/{len(chunks)}")
                        else:
                            logger.error(f"存储元数据失败: {file_path}, 块 {i+1}/{len(chunks)}")

                except Exception as e:
                    logger.error(f"处理文件时出错: {file_path}, {e}")

                # 更新进度
                processed_files += 1
                progress = 10 + int(90 * processed_files / total_files)
                self.file_progress_bar.setValue(progress)

            # 更新进度条
            self.file_progress_bar.setValue(100)

            # 清理资源
            embedder.cleanup()

            # 通知状态管理器
            try:
                from ..state_manager import get_state_manager
                state_manager = get_state_manager()
                # 估算总向量数（每个文件的平均向量数）
                total_vectors = processed_files * 10  # 估算值
                if 'latest_index' in locals():
                    state_manager.notify_vectors_added(str(latest_index), total_vectors)
            except Exception as e:
                logger.warning(f"通知状态管理器时出错: {e}")

            # 显示成功消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "向量化成功",
                f"成功向量化 {processed_files} 个文件"
            )

        except Exception as e:
            # 记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"向量化文件时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 显示错误消息
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(
                self,
                "向量化失败",
                f"向量化文件时出错: {str(e)}"
            )

            # 重置进度条
            self.file_progress_bar.setValue(0)

    def on_language_changed(self):
        """语言变更回调"""
        # 更新标题
        self.findChild(QLabel, "titleLabel").setText(
            self.translator.get_text("vectorize_data", "向量化数据")
        )

        # 更新表单标签
        form_layout = self.findChild(QFormLayout)
        if form_layout:
            form_layout.itemAt(0, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("model", "模型:"))
            form_layout.itemAt(1, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("language", "语言:"))
            form_layout.itemAt(2, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("batch_size", "批处理大小:"))

        # 更新文本输入组
        text_input_group = self.findChild(QGroupBox)
        if text_input_group:
            text_input_group.setTitle(self.translator.get_text("input_text", "输入文本"))

        # 更新按钮
        self.load_text_button.setText(self.translator.get_text("load_file", "加载文件"))
        self.vectorize_text_button.setText(self.translator.get_text("vectorize", "向量化"))

        # 更新文件表单标签
        file_form_layout = self.findChildren(QFormLayout)[1] if len(self.findChildren(QFormLayout)) > 1 else None
        if file_form_layout:
            file_form_layout.itemAt(0, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("model", "模型:"))
            file_form_layout.itemAt(1, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("file_type", "文件类型:"))
            file_form_layout.itemAt(2, QFormLayout.ItemRole.LabelRole).widget().setText(
                self.translator.get_text("batch_size", "批处理大小:"))

        # 更新文件选择组
        file_input_group = self.findChildren(QGroupBox)[1] if len(self.findChildren(QGroupBox)) > 1 else None
        if file_input_group:
            file_input_group.setTitle(self.translator.get_text("select_files", "选择文件"))

        # 更新文件按钮
        self.select_files_button.setText(self.translator.get_text("select_files", "选择文件"))
        self.select_folder_button.setText(self.translator.get_text("select_folder", "选择文件夹"))
        self.clear_files_button.setText(self.translator.get_text("clear", "清除"))
        self.vectorize_files_button.setText(self.translator.get_text("vectorize", "向量化"))

        # 更新标签页标题
        tab_widget = self.findChild(QTabWidget)
        if tab_widget:
            tab_widget.setTabText(0, self.translator.get_text("text_vectorization", "文本向量化"))
            tab_widget.setTabText(1, self.translator.get_text("file_vectorization", "文件向量化"))
