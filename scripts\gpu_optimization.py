#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GPU加速优化脚本
用于优化GPU加速，提高向量化和索引构建的速度
"""

import sys
import os
import logging
import time
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import yaml
import argparse
import json
from tqdm import tqdm
import torch
import faiss

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.vectorizer.embeddings import TextEmbedding
from src.indexer.builder import IndexBuilder

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config(config_path='config/config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config, config_path='config/config.yaml'):
    """保存配置文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

def check_gpu_availability():
    """检查GPU可用性"""
    logger.info("检查GPU可用性...")
    
    # 检查PyTorch GPU
    torch_gpu_available = torch.cuda.is_available()
    torch_gpu_count = torch.cuda.device_count() if torch_gpu_available else 0
    torch_gpu_names = [torch.cuda.get_device_name(i) for i in range(torch_gpu_count)] if torch_gpu_available else []
    
    logger.info(f"PyTorch GPU可用: {torch_gpu_available}")
    logger.info(f"PyTorch GPU数量: {torch_gpu_count}")
    for i, name in enumerate(torch_gpu_names):
        logger.info(f"  GPU {i}: {name}")
    
    # 检查FAISS GPU
    try:
        faiss_gpu_count = faiss.get_num_gpus()
        faiss_gpu_available = faiss_gpu_count > 0
        logger.info(f"FAISS GPU可用: {faiss_gpu_available}")
        logger.info(f"FAISS GPU数量: {faiss_gpu_count}")
    except Exception as e:
        logger.warning(f"检查FAISS GPU时出错: {e}")
        faiss_gpu_available = False
        faiss_gpu_count = 0
    
    return {
        'torch_gpu_available': torch_gpu_available,
        'torch_gpu_count': torch_gpu_count,
        'torch_gpu_names': torch_gpu_names,
        'faiss_gpu_available': faiss_gpu_available,
        'faiss_gpu_count': faiss_gpu_count
    }

def benchmark_gpu_vs_cpu(config, dimension=384, n_vectors=10000, n_queries=100):
    """
    对比GPU和CPU性能
    
    Args:
        config: 配置字典
        dimension: 向量维度
        n_vectors: 测试向量数量
        n_queries: 查询次数
    
    Returns:
        dict: 基准测试结果
    """
    logger.info(f"开始GPU vs CPU基准测试 (维度: {dimension}, 向量数: {n_vectors}, 查询数: {n_queries})")
    
    # 检查GPU可用性
    gpu_info = check_gpu_availability()
    if not gpu_info['torch_gpu_available'] and not gpu_info['faiss_gpu_available']:
        logger.warning("没有可用的GPU，无法进行GPU vs CPU对比测试")
        return {}
    
    # 生成测试向量
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
    query_vectors = np.random.rand(n_queries, dimension).astype('float32')
    
    # 测试文本
    test_texts = [
        "这是一个中文测试文本，用于测试中文向量化功能。" * 10,
        "This is an English test text for testing English vectorization." * 10,
        "混合Chinese和English的Text，用于测试多语言处理能力。" * 10
    ] * 100  # 复制以增加数据量
    
    results = {}
    
    # 测试向量化性能
    logger.info("\n测试向量化性能:")
    
    # CPU向量化
    config['vectorization']['device'] = 'cpu'
    logger.info("使用CPU进行向量化...")
    
    embedder_cpu = TextEmbedding(config)
    
    start_time = time.time()
    cpu_vectors = embedder_cpu.encode_batch(test_texts, use_cache=False)
    cpu_vectorize_time = time.time() - start_time
    
    logger.info(f"CPU向量化耗时: {cpu_vectorize_time:.4f}秒")
    
    # GPU向量化
    if gpu_info['torch_gpu_available']:
        config['vectorization']['device'] = 'cuda'
        logger.info("使用GPU进行向量化...")
        
        embedder_gpu = TextEmbedding(config)
        
        start_time = time.time()
        gpu_vectors = embedder_gpu.encode_batch(test_texts, use_cache=False)
        gpu_vectorize_time = time.time() - start_time
        
        logger.info(f"GPU向量化耗时: {gpu_vectorize_time:.4f}秒")
        logger.info(f"加速比: {cpu_vectorize_time / gpu_vectorize_time:.2f}x")
        
        # 验证结果一致性
        if np.allclose(cpu_vectors, gpu_vectors, rtol=1e-3, atol=1e-3):
            logger.info("CPU和GPU向量化结果一致")
        else:
            logger.warning("CPU和GPU向量化结果不一致，但这可能是由于浮点精度差异导致的")
    else:
        gpu_vectorize_time = float('inf')
        logger.warning("没有可用的PyTorch GPU，跳过GPU向量化测试")
    
    # 记录向量化结果
    results['vectorization'] = {
        'cpu_time': cpu_vectorize_time,
        'gpu_time': gpu_vectorize_time,
        'speedup': cpu_vectorize_time / gpu_vectorize_time if gpu_vectorize_time > 0 else 0
    }
    
    # 测试索引性能
    logger.info("\n测试索引性能:")
    
    # 测试不同的索引类型
    index_types = ['flat', 'ivf', 'hybrid']
    
    for idx_type in index_types:
        # 更新配置
        config['indexing']['index_type'] = idx_type
        config['indexing']['quantization'] = 'none'
        
        # CPU索引
        config['indexing']['use_gpu_index'] = False
        logger.info(f"使用CPU创建{idx_type}索引...")
        
        builder_cpu = IndexBuilder(config)
        
        start_time = time.time()
        builder_cpu.create_index(dimension)
        cpu_create_time = time.time() - start_time
        
        start_time = time.time()
        builder_cpu.add_vectors(test_vectors)
        cpu_add_time = time.time() - start_time
        
        cpu_search_times = []
        for query_vector in tqdm(query_vectors[:10], desc=f"CPU {idx_type}搜索"):
            start_time = time.time()
            builder_cpu.search(query_vector, k=10)
            cpu_search_times.append(time.time() - start_time)
        
        cpu_avg_search_time = np.mean(cpu_search_times)
        
        logger.info(f"CPU {idx_type}索引创建耗时: {cpu_create_time:.4f}秒")
        logger.info(f"CPU {idx_type}索引添加向量耗时: {cpu_add_time:.4f}秒")
        logger.info(f"CPU {idx_type}索引平均搜索耗时: {cpu_avg_search_time:.6f}秒")
        
        # GPU索引
        if gpu_info['faiss_gpu_available']:
            config['indexing']['use_gpu_index'] = True
            logger.info(f"使用GPU创建{idx_type}索引...")
            
            builder_gpu = IndexBuilder(config)
            
            start_time = time.time()
            builder_gpu.create_index(dimension)
            gpu_create_time = time.time() - start_time
            
            start_time = time.time()
            builder_gpu.add_vectors(test_vectors)
            gpu_add_time = time.time() - start_time
            
            gpu_search_times = []
            for query_vector in tqdm(query_vectors[:10], desc=f"GPU {idx_type}搜索"):
                start_time = time.time()
                builder_gpu.search(query_vector, k=10)
                gpu_search_times.append(time.time() - start_time)
            
            gpu_avg_search_time = np.mean(gpu_search_times)
            
            logger.info(f"GPU {idx_type}索引创建耗时: {gpu_create_time:.4f}秒")
            logger.info(f"GPU {idx_type}索引添加向量耗时: {gpu_add_time:.4f}秒")
            logger.info(f"GPU {idx_type}索引平均搜索耗时: {gpu_avg_search_time:.6f}秒")
            
            logger.info(f"创建加速比: {cpu_create_time / gpu_create_time:.2f}x")
            logger.info(f"添加加速比: {cpu_add_time / gpu_add_time:.2f}x")
            logger.info(f"搜索加速比: {cpu_avg_search_time / gpu_avg_search_time:.2f}x")
        else:
            gpu_create_time = float('inf')
            gpu_add_time = float('inf')
            gpu_avg_search_time = float('inf')
            logger.warning("没有可用的FAISS GPU，跳过GPU索引测试")
        
        # 记录索引结果
        results[f'index_{idx_type}'] = {
            'cpu_create_time': cpu_create_time,
            'cpu_add_time': cpu_add_time,
            'cpu_search_time': cpu_avg_search_time,
            'gpu_create_time': gpu_create_time,
            'gpu_add_time': gpu_add_time,
            'gpu_search_time': gpu_avg_search_time,
            'create_speedup': cpu_create_time / gpu_create_time if gpu_create_time > 0 else 0,
            'add_speedup': cpu_add_time / gpu_add_time if gpu_add_time > 0 else 0,
            'search_speedup': cpu_avg_search_time / gpu_avg_search_time if gpu_avg_search_time > 0 else 0
        }
    
    # 可视化结果
    visualize_gpu_benchmark_results(results, "gpu_benchmark_results.png")
    
    return results

def visualize_gpu_benchmark_results(results, output_file):
    """
    可视化GPU基准测试结果
    
    Args:
        results: 基准测试结果字典
        output_file: 输出文件路径
    """
    # 创建图表
    fig, axs = plt.subplots(2, 2, figsize=(15, 12))
    
    # 向量化性能
    if 'vectorization' in results:
        vec_result = results['vectorization']
        axs[0, 0].bar(['CPU', 'GPU'], [vec_result['cpu_time'], vec_result['gpu_time']])
        axs[0, 0].set_title('向量化时间')
        axs[0, 0].set_ylabel('时间 (秒)')
        axs[0, 0].text(1, vec_result['gpu_time'], f"{vec_result['speedup']:.2f}x", 
                      ha='center', va='bottom')
    
    # 索引创建时间
    index_types = [k for k in results.keys() if k.startswith('index_')]
    if index_types:
        cpu_create_times = [results[idx]['cpu_create_time'] for idx in index_types]
        gpu_create_times = [results[idx]['gpu_create_time'] for idx in index_types]
        index_labels = [idx.replace('index_', '') for idx in index_types]
        
        x = np.arange(len(index_labels))
        width = 0.35
        
        axs[0, 1].bar(x - width/2, cpu_create_times, width, label='CPU')
        axs[0, 1].bar(x + width/2, gpu_create_times, width, label='GPU')
        axs[0, 1].set_title('索引创建时间')
        axs[0, 1].set_ylabel('时间 (秒)')
        axs[0, 1].set_xticks(x)
        axs[0, 1].set_xticklabels(index_labels)
        axs[0, 1].legend()
        
        # 添加向量时间
        cpu_add_times = [results[idx]['cpu_add_time'] for idx in index_types]
        gpu_add_times = [results[idx]['gpu_add_time'] for idx in index_types]
        
        axs[1, 0].bar(x - width/2, cpu_add_times, width, label='CPU')
        axs[1, 0].bar(x + width/2, gpu_add_times, width, label='GPU')
        axs[1, 0].set_title('添加向量时间')
        axs[1, 0].set_ylabel('时间 (秒)')
        axs[1, 0].set_xticks(x)
        axs[1, 0].set_xticklabels(index_labels)
        axs[1, 0].legend()
        
        # 搜索时间
        cpu_search_times = [results[idx]['cpu_search_time'] * 1000 for idx in index_types]  # 转换为毫秒
        gpu_search_times = [results[idx]['gpu_search_time'] * 1000 for idx in index_types]  # 转换为毫秒
        
        axs[1, 1].bar(x - width/2, cpu_search_times, width, label='CPU')
        axs[1, 1].bar(x + width/2, gpu_search_times, width, label='GPU')
        axs[1, 1].set_title('平均搜索时间')
        axs[1, 1].set_ylabel('时间 (毫秒)')
        axs[1, 1].set_xticks(x)
        axs[1, 1].set_xticklabels(index_labels)
        axs[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig(output_file)
    logger.info(f"结果图表已保存到: {output_file}")

def optimize_gpu_config(config, benchmark_results):
    """
    优化GPU配置
    
    Args:
        config: 配置字典
        benchmark_results: 基准测试结果
    
    Returns:
        dict: 优化后的配置
    """
    logger.info("优化GPU配置...")
    
    # 检查GPU可用性
    gpu_info = check_gpu_availability()
    
    # 优化向量化配置
    if gpu_info['torch_gpu_available']:
        config['vectorization']['device'] = 'cuda'
        logger.info("已启用GPU向量化")
    else:
        config['vectorization']['device'] = 'cpu'
        logger.info("未检测到PyTorch GPU，使用CPU向量化")
    
    # 优化索引配置
    if gpu_info['faiss_gpu_available']:
        # 检查哪种索引类型在GPU上性能最好
        if benchmark_results:
            index_types = [k for k in benchmark_results.keys() if k.startswith('index_')]
            best_index_type = None
            best_speedup = 0
            
            for idx in index_types:
                speedup = benchmark_results[idx]['search_speedup']
                if speedup > best_speedup:
                    best_speedup = speedup
                    best_index_type = idx.replace('index_', '')
            
            if best_index_type:
                config['indexing']['index_type'] = best_index_type
                config['indexing']['use_gpu_index'] = True
                logger.info(f"已选择{best_index_type}索引类型，并启用GPU索引")
            else:
                config['indexing']['use_gpu_index'] = True
                logger.info("已启用GPU索引")
        else:
            config['indexing']['use_gpu_index'] = True
            logger.info("已启用GPU索引")
    else:
        config['indexing']['use_gpu_index'] = False
        logger.info("未检测到FAISS GPU，使用CPU索引")
    
    return config

def main():
    parser = argparse.ArgumentParser(description='GPU加速优化脚本')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--action', type=str, choices=['check', 'benchmark', 'optimize', 'all'], 
                        default='all', help='要执行的操作')
    parser.add_argument('--dimension', type=int, default=384, help='向量维度')
    parser.add_argument('--n_vectors', type=int, default=10000, help='测试向量数量')
    parser.add_argument('--n_queries', type=int, default=100, help='查询次数')
    parser.add_argument('--output', type=str, default='gpu_optimized_config.yaml', help='优化后的配置输出路径')
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 执行操作
    if args.action in ['check', 'all']:
        check_gpu_availability()
    
    benchmark_results = {}
    if args.action in ['benchmark', 'all']:
        benchmark_results = benchmark_gpu_vs_cpu(config, args.dimension, args.n_vectors, args.n_queries)
        
        # 保存基准测试结果
        with open('gpu_benchmark_results.json', 'w') as f:
            json.dump(benchmark_results, f, indent=2)
    
    if args.action in ['optimize', 'all']:
        # 如果没有基准测试结果，尝试从文件加载
        if not benchmark_results and os.path.exists('gpu_benchmark_results.json'):
            try:
                with open('gpu_benchmark_results.json', 'r') as f:
                    benchmark_results = json.load(f)
            except Exception as e:
                logger.warning(f"加载基准测试结果时出错: {e}")
        
        # 优化配置
        optimized_config = optimize_gpu_config(config, benchmark_results)
        
        # 保存优化后的配置
        save_config(optimized_config, args.output)
        logger.info(f"优化后的GPU配置已保存到: {args.output}")
    
    logger.info("GPU优化完成!")

if __name__ == "__main__":
    main()
