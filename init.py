
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目初始化脚本
用于首次设置和配置项目
"""

import os
import sys
import shutil
import subprocess
import argparse
from pathlib import Path
import logging
from typing import List, Dict, Any
import json
import yaml
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('init.log')
        ]
    )
    return logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        raise RuntimeError("需要Python 3.8或更高版本")

def check_dependencies():
    """检查系统依赖"""
    required_commands = ['git', 'pip', 'python']
    for cmd in required_commands:
        try:
            subprocess.run([cmd, '--version'], capture_output=True)
        except FileNotFoundError:
            raise RuntimeError(f"未找到必需的命令: {cmd}")

def create_directory_structure():
    """创建目录结构"""
    directories = [
        'data/raw',
        'data/processed',
        'data/vectors',
        'logs',
        'cache',
        'config',
        'notebooks',
        'tests/data'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

def create_virtual_environment():
    """创建虚拟环境"""
    if not Path('venv').exists():
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)

def install_dependencies(minimal: bool = False):
    """安装项目依赖"""
    pip_cmd = os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pip')
    
    # 升级pip
    subprocess.run([pip_cmd, 'install', '--upgrade', 'pip'], check=True)
    
    # 安装依赖
    req_file = 'requirements-init.txt' if minimal else 'requirements.txt'
    subprocess.run([pip_cmd, 'install', '-r', req_file], check=True)
    
    # 如果不是最小安装，安装开发依赖
    if not minimal:
        subprocess.run([pip_cmd, 'install', '-r', 'requirements-dev.txt'], check=True)

def setup_git():
    """设置Git"""
    if not Path('.git').exists():
        subprocess.run(['git', 'init'], check=True)
    
    # 创建.gitignore如果不存在
    if not Path('.gitignore').exists():
        shutil.copy('templates/.gitignore', '.gitignore')

def initialize_config():
    """初始化配置文件"""
    if not Path('config/config.yaml').exists():
        shutil.copy('config/config.yaml.example', 'config/config.yaml')
    
    if not Path('.env').exists():
        shutil.copy('.env.example', '.env')

def setup_pre_commit():
    """设置pre-commit钩子"""
    subprocess.run(['pre-commit', 'install'], check=True)
    subprocess.run(['pre-commit', 'run', '--all-files'], check=True)

def run_tests():
    """运行测试"""
    pytest_cmd = os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pytest')
    subprocess.run([pytest_cmd, 'tests'], check=True)

def create_initial_vectors():
    """创建初始向量数据库"""
    subprocess.run([sys.executable, 'quick_test.py'], check=True)

def generate_documentation():
    """生成文档"""
    subprocess.run([
        'sphinx-build',
        '-b', 'html',
        'docs/source',
        'docs/build/html'
    ], check=True)

def save_initialization_info():
    """保存初始化信息"""
    info = {
        'timestamp': datetime.now().isoformat(),
        'python_version': sys.version,
        'platform': sys.platform,
        'initialized_by': os.getenv('USER', 'unknown')
    }
    
    with open('initialization_info.json', 'w') as f:
        json.dump(info, f, indent=2)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor项目初始化工具')
    parser.add_argument('--minimal', action='store_true',
                       help='仅安装最小依赖集')
    parser.add_argument('--skip-tests', action='store_true',
                       help='跳过测试')
    parser.add_argument('--skip-docs', action='store_true',
                       help='跳过文档生成')
    args = parser.parse_args()
    
    logger = setup_logging()
    logger.info("开始项目初始化...")
    
    try:
        # 检查环境
        logger.info("检查环境...")
        check_python_version()
        check_dependencies()
        
        # 创建目录结构
        logger.info("创建目录结构...")
        create_directory_structure()
        
        # 创建虚拟环境
        logger.info("创建虚拟环境...")
        create_virtual_environment()
        
        # 安装依赖
        logger.info("安装依赖...")
        install_dependencies(args.minimal)
        
        # 设置Git
        logger.info("设置Git...")
        setup_git()
        
        # 初始化配置
        logger.info("初始化配置...")
        initialize_config()
        
        # 设置pre-commit
        if not args.minimal:
            logger.info("设置pre-commit...")
            setup_pre_commit()
        
        # 运行测试
        if not args.skip_tests:
            logger.info("运行测试...")
            run_tests()
        
        # 创建初始向量
        logger.info("创建初始向量...")
        create_initial_vectors()
        
        # 生成文档
        if not args.skip_docs and not args.minimal:
            logger.info("生成文档...")
            generate_documentation()
        
        # 保存初始化信息
        save_initialization_info()
        
        logger.info("项目初始化完成！")
        
        # 打印使用说明
        print("\n=== 使用说明 ===")
        print("1. 激活虚拟环境:")
        if sys.platform == 'win32':
            print("   .\\venv\\Scripts\\activate")
        else:
            print("   source venv/bin/activate")
        print("\n2. 运行示例:")
        print("   python examples/basic_usage.py")
        print("\n3. 启动API服务:")
        print("   python run.py --mode api")
        print("\n4. 运行测试:")
        print("   pytest tests/")
        print("\n5. 查看文档:")
        print("   打开 docs/build/html/index.html")
        
    except Exception as e:
        logger.error(f"初始化失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()