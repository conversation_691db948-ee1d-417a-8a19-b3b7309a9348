#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析元数据质量
"""

import json
from pathlib import Path
from collections import defaultdict, Counter
import statistics

def analyze_metadata_quality():
    """分析元数据质量"""
    
    print("📊 元数据质量详细分析")
    print("=" * 80)
    
    metadata_dir = Path("training_data/raw_documents/enterprise_standards/metadata")
    
    if not metadata_dir.exists():
        print("❌ 元数据目录不存在")
        return
    
    metadata_files = list(metadata_dir.glob("*_metadata.json"))
    
    print(f"📁 找到 {len(metadata_files)} 个元数据文件")
    
    # 统计数据
    stats = {
        'total_files': len(metadata_files),
        'null_standard_number': 0,
        'null_page_count': 0,
        'null_word_count': 0,
        'empty_keywords': 0,
        'empty_abstract': 0,
        'zero_automotive_relevance': 0,
        'general_technical_domains': 0,
        'low_technical_depth': 0
    }
    
    # 详细问题记录
    problems = {
        'no_standard_number': [],
        'no_keywords': [],
        'no_abstract': [],
        'zero_relevance': [],
        'general_domain_only': []
    }
    
    # 样本展示
    sample_files = []
    
    for i, file_path in enumerate(metadata_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            filename = file_path.name
            
            # 收集前10个样本
            if i < 10:
                sample_files.append((filename, metadata))
            
            # 统计各种问题
            if metadata.get('standard_number') is None:
                stats['null_standard_number'] += 1
                problems['no_standard_number'].append(filename)
            
            if metadata.get('page_count') is None:
                stats['null_page_count'] += 1
            
            if metadata.get('word_count') is None:
                stats['null_word_count'] += 1
            
            keywords = metadata.get('keywords', [])
            if not keywords or keywords == []:
                stats['empty_keywords'] += 1
                problems['no_keywords'].append(filename)
            
            abstract = metadata.get('abstract', '')
            if not abstract or abstract == '':
                stats['empty_abstract'] += 1
                problems['no_abstract'].append(filename)
            
            automotive_relevance = metadata.get('automotive_relevance', 0)
            if automotive_relevance == 0.0 or automotive_relevance == 0:
                stats['zero_automotive_relevance'] += 1
                problems['zero_relevance'].append(filename)
            
            technical_domains = metadata.get('technical_domains', [])
            if technical_domains == ['general']:
                stats['general_technical_domains'] += 1
                problems['general_domain_only'].append(filename)
            
            technical_depth = metadata.get('technical_depth', '')
            if technical_depth == 'low':
                stats['low_technical_depth'] += 1
                
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path.name}: {e}")
    
    # 显示统计结果
    print(f"\n📈 问题统计:")
    print("-" * 60)
    total = stats['total_files']
    
    print(f"标准号缺失:     {stats['null_standard_number']:4d} / {total} ({stats['null_standard_number']/total*100:.1f}%)")
    print(f"页数缺失:       {stats['null_page_count']:4d} / {total} ({stats['null_page_count']/total*100:.1f}%)")
    print(f"字数缺失:       {stats['null_word_count']:4d} / {total} ({stats['null_word_count']/total*100:.1f}%)")
    print(f"关键词为空:     {stats['empty_keywords']:4d} / {total} ({stats['empty_keywords']/total*100:.1f}%)")
    print(f"摘要为空:       {stats['empty_abstract']:4d} / {total} ({stats['empty_abstract']/total*100:.1f}%)")
    print(f"汽车相关性为0:  {stats['zero_automotive_relevance']:4d} / {total} ({stats['zero_automotive_relevance']/total*100:.1f}%)")
    print(f"技术领域通用:   {stats['general_technical_domains']:4d} / {total} ({stats['general_technical_domains']/total*100:.1f}%)")
    print(f"技术深度低:     {stats['low_technical_depth']:4d} / {total} ({stats['low_technical_depth']/total*100:.1f}%)")
    
    # 显示样本文件
    print(f"\n📋 样本文件详情 (前10个):")
    print("-" * 80)
    
    for i, (filename, metadata) in enumerate(sample_files, 1):
        print(f"\n{i}. {filename}")
        print(f"   标题: {metadata.get('title', 'N/A')}")
        print(f"   标准号: {metadata.get('standard_number', 'NULL')}")
        print(f"   发布年份: {metadata.get('publication_year', 'NULL')}")
        print(f"   页数: {metadata.get('page_count', 'NULL')}")
        print(f"   字数: {metadata.get('word_count', 'NULL')}")
        print(f"   关键词: {metadata.get('keywords', [])}")
        print(f"   摘要: '{metadata.get('abstract', '')}' (长度: {len(metadata.get('abstract', ''))})")
        print(f"   汽车相关性: {metadata.get('automotive_relevance', 0)}")
        print(f"   技术领域: {metadata.get('technical_domains', [])}")
        print(f"   技术深度: {metadata.get('technical_depth', 'N/A')}")
        print(f"   文件大小: {metadata.get('file_size', 0)} bytes")
    
    # 显示问题文件示例
    print(f"\n🔍 问题文件示例:")
    print("-" * 60)
    
    print(f"\n❌ 标准号缺失的文件 (前5个):")
    for filename in problems['no_standard_number'][:5]:
        print(f"   - {filename}")
    
    print(f"\n❌ 关键词为空的文件 (前5个):")
    for filename in problems['no_keywords'][:5]:
        print(f"   - {filename}")
    
    print(f"\n❌ 摘要为空的文件 (前5个):")
    for filename in problems['no_abstract'][:5]:
        print(f"   - {filename}")
    
    print(f"\n❌ 汽车相关性为0的文件 (前5个):")
    for filename in problems['zero_relevance'][:5]:
        print(f"   - {filename}")
    
    print(f"\n❌ 技术领域只有'general'的文件 (前5个):")
    for filename in problems['general_domain_only'][:5]:
        print(f"   - {filename}")
    
    # 分析文件名模式
    print(f"\n🔤 文件名模式分析:")
    print("-" * 60)
    
    # 提取标准号模式
    standard_patterns = defaultdict(int)
    year_patterns = defaultdict(int)
    
    for file_path in metadata_files:
        filename = file_path.stem.replace('_metadata', '')
        
        # 分析标准号模式
        if filename.startswith('VW_'):
            standard_patterns['VW'] += 1
        elif filename.startswith('GMW_'):
            standard_patterns['GMW'] += 1
        elif filename.startswith('GM_'):
            standard_patterns['GM'] += 1
        elif filename.startswith('ES_'):
            standard_patterns['ES'] += 1
        elif filename.startswith('MBN_'):
            standard_patterns['MBN'] += 1
        elif filename.startswith('A '):
            standard_patterns['Mercedes-A'] += 1
        elif filename.startswith('DBL_'):
            standard_patterns['DBL'] += 1
        elif filename.startswith('SAE_'):
            standard_patterns['SAE'] += 1
        else:
            standard_patterns['Other'] += 1
    
    print("标准号前缀分布:")
    for pattern, count in sorted(standard_patterns.items(), key=lambda x: x[1], reverse=True):
        print(f"   {pattern}: {count} 个文件")
    
    # 建议
    print(f"\n💡 改进建议:")
    print("-" * 60)
    print("1. 标准号提取:")
    print("   - 可以从文件名中更准确地提取标准号")
    print("   - 需要针对不同厂商的命名规则优化正则表达式")
    
    print("\n2. PDF内容解析:")
    print("   - 需要集成PDF解析库 (PyPDF2, pdfplumber, pymupdf)")
    print("   - 可以提取页数、字数、摘要等信息")
    
    print("\n3. 关键词提取:")
    print("   - 可以从文件名和内容中提取更多关键词")
    print("   - 使用汽车电器专业词典进行匹配")
    
    print("\n4. 汽车相关性计算:")
    print("   - 当前算法过于保守，需要调整权重")
    print("   - 增加更多汽车电器相关关键词")
    
    print("\n5. 技术领域识别:")
    print("   - 需要更细致的技术领域分类")
    print("   - 基于文件名和内容进行智能分类")

if __name__ == "__main__":
    analyze_metadata_quality()
