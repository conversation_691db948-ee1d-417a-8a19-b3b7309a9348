{"start_time": "2025-05-29T13:51:24.162432", "tests": {"document_loading": {"name": "文档加载测试", "status": "completed", "details": {"docs\\new_features.md": {"status": "success", "content_length": 7891, "encoding": "latin-1", "load_time": 0.006989002227783203, "file_size": 7891}, "docs\\training_models_guide.md": {"status": "success", "content_length": 6012, "encoding": "utf-8", "load_time": 0.0020008087158203125, "file_size": 8670}, "docs\\快速搭建企业级知识库的详细架构与操作流程.md": {"status": "success", "content_length": 5352, "encoding": "latin-1", "load_time": 0.0031490325927734375, "file_size": 5499}}, "errors": [], "success_rate": 1.0}, "vectorization": {"name": "向量化测试", "status": "completed", "details": {"vector_storage": "success", "vectorization": {"chunks_count": 1, "vector_dimension": 384, "total_vectors": 1}}, "errors": []}, "index_creation": {"name": "索引创建测试", "status": "completed", "details": {"flat": {"status": "success", "vector_count": 100, "search_results": 5}, "ivf": {"status": "failed", "error": "Error in virtual void __cdecl faiss::IndexIVFFlat::add_core(idx_t, const float *, const idx_t *, const idx_t *, void *) at D:\\a\\faiss-wheels\\faiss-wheels\\faiss\\faiss\\IndexIVFFlat.cpp:52: Error: 'is_trained' failed"}}, "errors": []}, "search_functionality": {"name": "搜索功能测试", "status": "completed", "details": {"basic_search": {"status": "failed", "results_count": 0, "has_metadata": true}, "batch_search": {"status": "success", "query_count": 3, "avg_results": 0.0}}, "errors": []}}, "summary": {"total_tests": 4, "passed_tests": 4, "failed_tests": 0, "success_rate": 1.0, "test_files_count": 3}, "recommendations": [], "end_time": "2025-05-29T13:52:12.044518"}