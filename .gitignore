
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Project specific
data/
logs/
cache/
vectors/
*.h5
*.npy
*.npz
*.pkl
*.db
*.sqlite3

# Temporary files
*.tmp
*.bak
*.swp
*.log
.DS_Store

# Test
.pytest_cache/
.coverage
htmlcov/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
docs/api/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Distribution
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Compiled files
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.dylib

# Local development settings
local_settings.py
instance/

# Database
*.db
*.sqlite
*.sqlite3

# Environment variables
.env
.env.*

# Model files
*.pt
*.pth
*.onnx
*.model

# Vector files
*.vec
*.bin
*.idx

# Backup files
*.bak
*~
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Large files
*.zip
*.tar.gz
*.rar
*.7z

# Custom ignore
custom_ignore/