#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试新添加的功能
包括多语言支持、代码块处理、混合索引和量化技术
"""

import sys
import os
import logging
import time
import numpy as np
from pathlib import Path
import yaml
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.vectorizer.embeddings import TextEmbedding, ModelRegistry
from src.indexer.builder import IndexBuilder
from src.preprocessor.tokenizer import TextTokenizer, LanguageType, CodeLanguage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config(config_path='config/config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def test_multilingual_support(config):
    """测试多语言支持"""
    logger.info("=== 测试多语言支持 ===")

    # 初始化分词器
    tokenizer = TextTokenizer(config)

    # 测试文本
    test_texts = {
        "中文": "这是一个中文测试文本，用于测试中文分词功能。",
        "英文": "This is an English test text for testing English tokenization.",
        "日文": "これは日本語のテストテキストです。日本語のトークン化をテストするためのものです。",
        "韩文": "이것은 한국어 테스트 텍스트입니다. 한국어 토큰화를 테스트하기 위한 것입니다.",
        "俄文": "Это тестовый текст на русском языке для проверки токенизации русского языка.",
        "西班牙文": "Este es un texto de prueba en español para probar la tokenización en español.",
        "法文": "Ceci est un texte de test en français pour tester la tokenisation en français.",
        "德文": "Dies ist ein deutscher Testtext zur Überprüfung der deutschen Tokenisierung.",
        "阿拉伯文": "هذا نص اختبار باللغة العربية لاختبار الترميز العربي.",
        "泰文": "นี่คือข้อความทดสอบภาษาไทยสำหรับการทดสอบการแบ่งคำภาษาไทย",
        "混合语言": "这是一个混合了English和中文的测试Text，同时还包含了一些日本語。",
    }

    for lang_name, text in test_texts.items():
        logger.info(f"测试{lang_name}分词:")

        # 检测语言
        if hasattr(tokenizer, 'detect_language'):
            detected_lang = tokenizer.detect_language(text)
            if isinstance(detected_lang, LanguageType):
                logger.info(f"  检测到的语言: {detected_lang.value}")
            else:
                logger.info(f"  检测到的语言: {detected_lang}")

        # 分词
        tokens = tokenizer.tokenize_text(text)
        logger.info(f"  分词结果 ({len(tokens)}个token): {tokens[:10]}")
        if len(tokens) > 10:
            logger.info(f"  ... 还有 {len(tokens) - 10} 个token")
        logger.info("")

    return True

def test_code_block_processing(config):
    """测试代码块处理"""
    logger.info("=== 测试代码块处理 ===")

    # 初始化分词器
    tokenizer = TextTokenizer(config)

    # 测试文本
    test_texts = {
        "Python代码块": """以下是Python代码示例：
```python
def hello_world():
    print('Hello, World!')

    # 这是一个注释
    for i in range(10):
        print(f"Count: {i}")

hello_world()
```
这是代码块之后的文本。
""",
        "JavaScript代码块": """JavaScript示例：
```js
function helloWorld() {
    console.log('Hello, World!');

    // 这是一个注释
    for (let i = 0; i < 10; i++) {
        console.log(`Count: ${i}`);
    }
}

helloWorld();
```
这是代码块之后的文本。
""",
        "内联代码": "使用 `print('Hello')` 打印问候语，使用 `console.log('Hello')` 在JavaScript中打印。",
        "无语言标记代码块": """以下是无语言标记的代码块：
```
function test() {
    return true;
}
```
""",
        "混合内容": """这是一个包含多种语言代码块的文档。

Python示例:
```python
import numpy as np
data = np.array([1, 2, 3])
print(data.mean())
```

SQL示例:
```sql
SELECT * FROM users WHERE age > 18;
```

这是中间的一些文本。

C++示例:
```cpp
#include <iostream>
int main() {
    std::cout << "Hello World!" << std::endl;
    return 0;
}
```
"""
    }

    for content_type, text in test_texts.items():
        logger.info(f"测试{content_type}:")

        # 提取代码块
        if hasattr(tokenizer, 'extract_code_blocks'):
            code_blocks = tokenizer.extract_code_blocks(text)
            logger.info(f"  提取到 {len(code_blocks)} 个代码块")

            for i, (code, lang) in enumerate(code_blocks):
                lang_str = lang.value if isinstance(lang, CodeLanguage) else "未知"
                logger.info(f"  代码块 {i+1} (语言: {lang_str}):")
                code_preview = code.split('\n')[0][:50] + ("..." if len(code.split('\n')[0]) > 50 else "")
                logger.info(f"    {code_preview}")

        # 分词
        tokens = tokenizer.tokenize_text(text)
        logger.info(f"  分词结果 ({len(tokens)}个token): {tokens[:10]}")
        if len(tokens) > 10:
            logger.info(f"  ... 还有 {len(tokens) - 10} 个token")
        logger.info("")

    return True

def test_model_registry(config):
    """测试模型注册表和多模型支持"""
    logger.info("=== 测试模型注册表和多模型支持 ===")

    # 查看可用模型
    logger.info("可用的领域模型:")
    for domain, model_config in ModelRegistry.DOMAIN_MODELS.items():
        logger.info(f"  {domain}: {model_config.name} (基于 {model_config.base_model})")

    logger.info("\n通用多语言模型:")
    for name, model_path in ModelRegistry.GENERAL_MODELS.items():
        logger.info(f"  {name}: {model_path}")

    logger.info("\n高级模型:")
    for name, model_path in ModelRegistry.ADVANCED_MODELS.items():
        logger.info(f"  {name}: {model_path}")

    # 测试不同模型
    test_models = ["multilingual-minilm", "medical", "code"]
    test_texts = [
        "这是一个中文测试文本，用于测试中文向量化功能。",
        "This is an English test text for testing English vectorization.",
        "患者出现发热、咳嗽、呼吸困难等症状，考虑为肺炎。",
        "def hello_world():\n    print('Hello, World!')"
    ]

    for model_name in test_models:
        # 更新配置
        config['vectorization']['model_name'] = model_name
        if model_name in ModelRegistry.DOMAIN_MODELS:
            config['vectorization']['domain'] = model_name
        else:
            config['vectorization']['domain'] = None

        try:
            logger.info(f"\n测试模型: {model_name}")
            embedder = TextEmbedding(config)

            for text in test_texts:
                vector = embedder.encode_text(text)
                logger.info(f"  文本: {text[:30]}...")
                logger.info(f"  向量维度: {vector.shape}")
                logger.info(f"  向量前5个元素: {vector[:5]}")

            embedder.cleanup()
        except Exception as e:
            logger.error(f"测试模型 {model_name} 时出错: {e}")

    return True

def test_hybrid_index(config):
    """测试混合索引和量化技术"""
    logger.info("=== 测试混合索引和量化技术 ===")

    # 测试不同的索引类型和量化方法
    index_types = ['flat', 'ivf', 'hnsw', 'hybrid']
    # 移除opq，因为某些FAISS版本可能不支持
    quantization_types = ['none', 'pq']

    # 生成测试向量
    dimension = 384
    n_vectors = 1000
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')

    results = {}

    for idx_type in index_types:
        for quant_type in quantization_types:
            # 跳过不兼容的组合
            if (idx_type in ['pq', 'opq'] and quant_type != 'none'):
                continue

            # 更新配置
            config['indexing']['index_type'] = idx_type
            config['indexing']['quantization'] = quant_type

            try:
                logger.info(f"\n测试 {idx_type} 索引 + {quant_type} 量化:")

                # 创建索引构建器
                builder = IndexBuilder(config)

                # 创建索引
                start_time = time.time()
                builder.create_index(dimension)
                create_time = time.time() - start_time
                logger.info(f"  创建索引耗时: {create_time:.4f}秒")

                # 添加向量
                start_time = time.time()
                builder.add_vectors(test_vectors)
                add_time = time.time() - start_time
                logger.info(f"  添加向量耗时: {add_time:.4f}秒")

                # 优化索引
                start_time = time.time()
                builder.optimize_index()
                optimize_time = time.time() - start_time
                logger.info(f"  优化索引耗时: {optimize_time:.4f}秒")

                # 搜索测试
                query_vector = np.random.rand(dimension).astype('float32')
                start_time = time.time()
                distances, indices = builder.search(query_vector, k=10)
                search_time = time.time() - start_time
                logger.info(f"  搜索耗时: {search_time:.4f}秒")

                # 获取索引统计信息
                stats = builder.get_index_stats()
                mem_usage = stats.get('estimated_memory_usage_mb', 0)
                logger.info(f"  内存使用: {mem_usage:.2f} MB")

                # 记录结果
                results[f"{idx_type}_{quant_type}"] = {
                    'create_time': create_time,
                    'add_time': add_time,
                    'optimize_time': optimize_time,
                    'search_time': search_time,
                    'memory_usage': mem_usage
                }

            except Exception as e:
                logger.error(f"  测试 {idx_type} + {quant_type} 时出错: {e}")

    # 比较结果
    logger.info("\n性能比较:")
    logger.info(f"{'索引类型':20} {'创建时间(秒)':15} {'添加时间(秒)':15} {'优化时间(秒)':15} {'搜索时间(秒)':15} {'内存使用(MB)':15}")
    for idx_name, metrics in results.items():
        logger.info(f"{idx_name:20} {metrics['create_time']:15.4f} {metrics['add_time']:15.4f} {metrics['optimize_time']:15.4f} {metrics['search_time']:15.4f} {metrics['memory_usage']:15.2f}")

    return True

def main():
    parser = argparse.ArgumentParser(description='测试新添加的功能')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--test', type=str, choices=['all', 'multilingual', 'code', 'model', 'index'],
                        default='all', help='要运行的测试')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 运行测试
    if args.test in ['all', 'multilingual']:
        test_multilingual_support(config)

    if args.test in ['all', 'code']:
        test_code_block_processing(config)

    if args.test in ['all', 'model']:
        test_model_registry(config)

    if args.test in ['all', 'index']:
        test_hybrid_index(config)

    logger.info("所有测试完成!")

if __name__ == "__main__":
    main()
