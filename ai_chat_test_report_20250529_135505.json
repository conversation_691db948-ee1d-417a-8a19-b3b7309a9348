{"start_time": "2025-05-29T13:54:16.159076", "chat_tests": {"status": "completed", "results": {"如何搭建企业级知识库？": {"status": "success", "response": "根据提供的信息，企业级知识库搭建的步骤如下：\n1. 首先需要进行需求分析和架构设计\n2. 然后进行选择合适的技术栈\n3. 最后完成部署和优化系统", "quality_score": 0.8333333333333333, "response_length": 72}, "向量化模型需要训练吗？": {"status": "success", "response": "是的，向量化模型确实需要根据具体类型进行训练。具体来说：量化索引需要训练，而HNSW索引主要是构建过程", "quality_score": 1.0, "response_length": 51}}, "avg_quality_score": 0.9166666666666666}, "retrieval_tests": {"status": "completed", "results": {"如何搭建企业级知识库？": {"status": "success", "query_vector_shape": [384], "vector_norm": 1.0}, "向量化模型需要训练吗？": {"status": "success", "query_vector_shape": [384], "vector_norm": 1.0}, "FAISS索引的优势是什么？": {"status": "success", "query_vector_shape": [384], "vector_norm": 1.0}, "文档处理支持哪些格式？": {"status": "success", "query_vector_shape": [384], "vector_norm": 0.9999998807907104}}, "success_rate": 1.0}, "integration_tests": {"status": "completed", "workflow_results": {"文档加载": {"status": "success", "execution_time": 0.07875287617569744, "output_size": 5000}, "文本分块": {"status": "success", "execution_time": 0.17202790134406437, "output_size": 10}, "向量化": {"status": "success", "execution_time": 0.26518058833514707, "output_size": 384}, "索引存储": {"status": "success", "execution_time": 0.1770234235918215, "output_size": 1}, "查询处理": {"status": "success", "execution_time": 0.09917837297238773, "output_size": 5}, "AI响应生成": {"status": "success", "execution_time": 0.27524627601704504, "output_size": 200}}, "success_rate": 1.0, "total_steps": 6}, "end_time": "2025-05-29T13:55:05.448349"}