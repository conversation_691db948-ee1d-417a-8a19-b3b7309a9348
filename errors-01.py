2025-05-30 14:16:30,922 - src.utils.document_loader - INFO - 总内容长度: 370,263 字符
2025-05-30 14:16:30,922 - src.utils.document_loader - INFO - 平均加载时间: 15.765 秒
2025-05-30 14:16:31,051 - training_automation - INFO - 内容已截断: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3172_CH_2012-11_电子电气部件环境与耐久通
用规范.pdf
2025-05-30 14:16:31,052 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3172_CH_2012-11_电子电气部件环
境与耐久通用规范.pdf
处理enterprise_standards:  83%|█████████████████████████████████████████████████████████████████████████████████████████████████████▎                    | 83/100 [04:09<03:12, 11.32s/it]2025-05-30 14:16:31,083 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:35,684 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3172_EN_2012-11_电子电气部件环境
与耐久通用规范.pdf
2025-05-30 14:16:35,686 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:35,687 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:35,688 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:35,689 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:35,690 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:35,691 - src.utils.document_loader - INFO - 总文件大小: 2.71 MB
2025-05-30 14:16:35,691 - src.utils.document_loader - INFO - 总内容长度: 282,788 字符
2025-05-30 14:16:35,692 - src.utils.document_loader - INFO - 平均加载时间: 2.205 秒
2025-05-30 14:16:35,808 - training_automation - INFO - 内容已截断: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3172_EN_2012-11_电子电气部件环境与耐久通
用规范.pdf
2025-05-30 14:16:35,809 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3172_EN_2012-11_电子电气部件环
境与耐久通用规范.pdf
处理enterprise_standards:  84%|██████████████████████████████████████████████████████████████████████████████████████████████████████▍                   | 84/100 [04:14<02:35,  9.70s/it]2025-05-30 14:16:35,827 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:40,503 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3173_EN_2013-02_数据传输总线的导
线选型和线束要求.pdf
2025-05-30 14:16:40,505 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:40,505 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:40,506 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:40,506 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:40,507 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:40,507 - src.utils.document_loader - INFO - 总文件大小: 0.69 MB
2025-05-30 14:16:40,507 - src.utils.document_loader - INFO - 总内容长度: 93,759 字符
2025-05-30 14:16:40,508 - src.utils.document_loader - INFO - 平均加载时间: 2.276 秒
2025-05-30 14:16:40,541 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3173_EN_2013-02_数据传输总线的
导线选型和线束要求.pdf
处理enterprise_standards:  85%|███████████████████████████████████████████████████████████████████████████████████████████████████████▋                  | 85/100 [04:19<02:06,  8.40s/it]2025-05-30 14:16:40,554 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:41,947 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3176_EN_2008-05_系统功能缩写及对
应线色要求.pdf
2025-05-30 14:16:41,949 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:41,950 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:41,950 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:41,951 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:41,952 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:41,953 - src.utils.document_loader - INFO - 总文件大小: 0.14 MB
2025-05-30 14:16:41,953 - src.utils.document_loader - INFO - 总内容长度: 43,245 字符
2025-05-30 14:16:41,954 - src.utils.document_loader - INFO - 平均加载时间: 0.834 秒
2025-05-30 14:16:41,972 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3176_EN_2008-05_系统功能缩写及
对应线色要求.pdf
处理enterprise_standards:  86%|████████████████████████████████████████████████████████████████████████████████████████████████████████▉                 | 86/100 [04:20<01:31,  6.51s/it]2025-05-30 14:16:41,981 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:44,952 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3191_CH_2012-06_连接器测试和检验
标准.pdf
2025-05-30 14:16:44,953 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:44,954 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:44,954 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:44,955 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:44,955 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:44,955 - src.utils.document_loader - INFO - 总文件大小: 1.92 MB
2025-05-30 14:16:44,956 - src.utils.document_loader - INFO - 总内容长度: 61,244 字符
2025-05-30 14:16:44,956 - src.utils.document_loader - INFO - 平均加载时间: 1.579 秒
2025-05-30 14:16:44,982 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3191_CH_2012-06_连接器测试和检
验标准.pdf
处理enterprise_standards:  87%|██████████████████████████████████████████████████████████████████████████████████████████████████████████▏               | 87/100 [04:23<01:11,  5.53s/it]2025-05-30 14:16:45,004 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:54,985 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3191_EN_2012-06_连接器测试和检验
标准.pdf
2025-05-30 14:16:54,987 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:54,988 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:54,988 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:54,989 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:54,989 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:54,990 - src.utils.document_loader - INFO - 总文件大小: 1.32 MB
2025-05-30 14:16:54,990 - src.utils.document_loader - INFO - 总内容长度: 190,299 字符
2025-05-30 14:16:54,990 - src.utils.document_loader - INFO - 平均加载时间: 5.042 秒
2025-05-30 14:16:55,071 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3191_EN_2012-06_连接器测试和检
验标准.pdf
处理enterprise_standards:  88%|███████████████████████████████████████████████████████████████████████████████████████████████████████████▎              | 88/100 [04:33<01:21,  6.83s/it]2025-05-30 14:16:55,091 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:56,350 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3200_EN_2005-04_锌锡镀层的技术要
求.pdf
2025-05-30 14:16:56,351 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:56,352 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:56,352 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:56,353 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:56,353 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:56,354 - src.utils.document_loader - INFO - 总文件大小: 0.18 MB
2025-05-30 14:16:56,354 - src.utils.document_loader - INFO - 总内容长度: 23,387 字符
2025-05-30 14:16:56,354 - src.utils.document_loader - INFO - 平均加载时间: 0.894 秒
2025-05-30 14:16:56,365 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3200_EN_2005-04_锌锡镀层的技术 
要求.pdf
处理enterprise_standards:  89%|████████████████████████████████████████████████████████████████████████████████████████████████████████████▌             | 89/100 [04:34<00:57,  5.23s/it]2025-05-30 14:16:56,379 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:57,027 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3205_CH_2011-01_确定内饰材料的抗
气味传播性.pdf
2025-05-30 14:16:57,028 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:57,029 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:57,030 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:57,030 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:57,030 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:57,031 - src.utils.document_loader - INFO - 总文件大小: 0.32 MB
2025-05-30 14:16:57,031 - src.utils.document_loader - INFO - 总内容长度: 7,049 字符
2025-05-30 14:16:57,033 - src.utils.document_loader - INFO - 平均加载时间: 0.325 秒
2025-05-30 14:16:57,049 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3205_CH_2011-01_确定内饰材料的
抗气味传播性.pdf
处理enterprise_standards:  90%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████▊            | 90/100 [04:35<00:38,  3.89s/it2 
025-05-30 14:16:57,053 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:57,752 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3205_EN_2011-01_确定内饰材料的抗
气味传播性.pdf
2025-05-30 14:16:57,753 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:57,754 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:57,755 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:57,756 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:57,757 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:57,758 - src.utils.document_loader - INFO - 总文件大小: 0.07 MB
2025-05-30 14:16:57,759 - src.utils.document_loader - INFO - 总内容长度: 16,194 字符
2025-05-30 14:16:57,761 - src.utils.document_loader - INFO - 平均加载时间: 0.502 秒
2025-05-30 14:16:57,773 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3205_EN_2011-01_确定内饰材料的
抗气味传播性.pdf
处理enterprise_standards:  91%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████           | 91/100 [04:36<00:26,  2.96s/it]2025-05-30 14:16:57,776 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:58,417 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3232_EN_2007-02_测定内部装饰材料
可燃性的试验方法.pdf
2025-05-30 14:16:58,419 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:58,419 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:58,419 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:58,420 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:58,420 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:58,421 - src.utils.document_loader - INFO - 总文件大小: 0.14 MB
2025-05-30 14:16:58,421 - src.utils.document_loader - INFO - 总内容长度: 31,659 字符
2025-05-30 14:16:58,422 - src.utils.document_loader - INFO - 平均加载时间: 0.326 秒
2025-05-30 14:16:58,434 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3232_EN_2007-02_测定内部装饰材
料可燃性的试验方法.pdf
处理enterprise_standards:  92%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████▏         | 92/100 [04:37<00:18,  2.28s/it]2025-05-30 14:16:58,438 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:16:58,518 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3251_CH_2005-05_扎带要求.pdf
2025-05-30 14:16:58,519 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:16:58,519 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:16:58,520 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:16:58,520 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:16:58,521 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:16:58,521 - src.utils.document_loader - INFO - 总文件大小: 0.16 MB
2025-05-30 14:16:58,522 - src.utils.document_loader - INFO - 总内容长度: 1,497 字符
2025-05-30 14:16:58,522 - src.utils.document_loader - INFO - 平均加载时间: 0.042 秒
2025-05-30 14:16:58,523 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3251_CH_2005-05_扎带要求.pdf   
2025-05-30 14:16:58,524 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:01,945 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3251_EN_2005-05_扎带要求.pdf
2025-05-30 14:17:01,947 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:01,948 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:01,948 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:01,949 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:01,951 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:01,951 - src.utils.document_loader - INFO - 总文件大小: 0.28 MB
2025-05-30 14:17:01,953 - src.utils.document_loader - INFO - 总内容长度: 7,208 字符
2025-05-30 14:17:01,953 - src.utils.document_loader - INFO - 平均加载时间: 1.896 秒
2025-05-30 14:17:01,970 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_3251_EN_2005-05_扎带要求.pdf
处理enterprise_standards:  94%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████▋       | 94/100 [04:40<00:12,  2.04s/it]2025-05-30 14:17:01,974 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:02,729 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8739_EN_2007-06_汽车高压电器设计
安全要求.pdf
2025-05-30 14:17:02,730 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:02,731 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:02,732 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:02,732 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:02,733 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:02,734 - src.utils.document_loader - INFO - 总文件大小: 0.29 MB
2025-05-30 14:17:02,735 - src.utils.document_loader - INFO - 总内容长度: 43,587 字符
2025-05-30 14:17:02,735 - src.utils.document_loader - INFO - 平均加载时间: 0.292 秒
2025-05-30 14:17:02,754 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8739_EN_2007-06_汽车高压电器设
计安全要求.pdf
处理enterprise_standards:  95%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉      | 95/100 [04:41<00:08,  1.73s/it2 
025-05-30 14:17:02,761 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:19,680 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8762_EN_2006-03_GMLAN 系列数据.pdf
2025-05-30 14:17:19,682 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:19,683 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:19,684 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:19,685 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:19,685 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:19,686 - src.utils.document_loader - INFO - 总文件大小: 1.53 MB
2025-05-30 14:17:19,687 - src.utils.document_loader - INFO - 总内容长度: 782,837 字符
2025-05-30 14:17:19,687 - src.utils.document_loader - INFO - 平均加载时间: 8.368 秒
2025-05-30 14:17:20,044 - training_automation - INFO - 内容已截断: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8762_EN_2006-03_GMLAN 系列数据.pdf
2025-05-30 14:17:20,046 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8762_EN_2006-03_GMLAN 系列数据.pdf
处理enterprise_standards:  96%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████     | 96/100 [04:58<00:23,  5.80s/it]2025-05-30 14:17:20,091 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:20,814 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8772_EN_2006-03_串行数据结构子系
统.pdf
2025-05-30 14:17:20,815 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:20,816 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:20,816 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:20,816 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:20,816 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:20,816 - src.utils.document_loader - INFO - 总文件大小: 0.10 MB
2025-05-30 14:17:20,817 - src.utils.document_loader - INFO - 总内容长度: 50,432 字符
2025-05-30 14:17:20,817 - src.utils.document_loader - INFO - 平均加载时间: 0.360 秒
2025-05-30 14:17:20,836 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GMW_8772_EN_2006-03_串行数据结构子
系统.pdf
处理enterprise_standards:  97%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▎   | 97/100 [04:59<00:13,  4.43s/it]2025-05-30 14:17:20,843 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:47,531 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN-CH_全球供应商质量手册.pdf
2025-05-30 14:17:47,532 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:47,533 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:47,534 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:47,535 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:47,535 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:47,536 - src.utils.document_loader - INFO - 总文件大小: 2.27 MB
2025-05-30 14:17:47,537 - src.utils.document_loader - INFO - 总内容长度: 294,441 字符
2025-05-30 14:17:47,538 - src.utils.document_loader - INFO - 平均加载时间: 13.592 秒
2025-05-30 14:17:47,658 - training_automation - INFO - 内容已截断: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN-CH_全球供应商质量手册.pdf
2025-05-30 14:17:47,659 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN-CH_全球供应商质量手册.pdf
处理enterprise_standards:  98%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▌  | 98/100 [05:26<00:21, 10.69s/it]2025-05-30 14:17:47,683 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:57,299 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN_全球供应商质量手册.pdf
2025-05-30 14:17:57,301 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:57,301 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:57,302 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:57,302 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:57,302 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:57,303 - src.utils.document_loader - INFO - 总文件大小: 0.91 MB
2025-05-30 14:17:57,303 - src.utils.document_loader - INFO - 总内容长度: 218,872 字符
2025-05-30 14:17:57,304 - src.utils.document_loader - INFO - 平均加载时间: 4.911 秒
2025-05-30 14:17:57,381 - training_automation - INFO - 内容已截断: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN_全球供应商质量手册.pdf
2025-05-30 14:17:57,383 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_1927_EN_全球供应商质量手册.pdf
处理enterprise_standards:  99%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▊ | 99/100 [05:35<00:10, 10.41s/it]2025-05-30 14:17:57,406 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:17:59,885 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_20200_EN_2001-12_被动安全系统线束
相关技术要求.pdf
2025-05-30 14:17:59,886 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:17:59,887 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:17:59,888 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:17:59,888 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:17:59,888 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:17:59,889 - src.utils.document_loader - INFO - 总文件大小: 0.39 MB
2025-05-30 14:17:59,889 - src.utils.document_loader - INFO - 总内容长度: 49,231 字符
2025-05-30 14:17:59,890 - src.utils.document_loader - INFO - 平均加载时间: 1.249 秒
2025-05-30 14:17:59,909 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_20200_EN_2001-12_被动安全系统线
束相关技术要求.pdf
处理enterprise_standards: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [05:38<00:00,  3.38s/it] 
2025-05-30 14:17:59,917 - training_automation - INFO - 文档处理完成，生成 5288 个文本块
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c8' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 246, in run_training_with_monitoring
    logger.info(f"📈 类别 {category} 进度: {progress:.1f}% ({processed}/{len(files)})")
Message: '📈 类别 enterprise_standards 进度: 96.4% (800/830)'
Arguments: ()
2025-05-30 14:17:59,918 - __main__ - INFO - 📈 类别 enterprise_standards 进度: 96.4% (800/830)
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e6' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 236, in run_training_with_monitoring
    logger.info(f"📦 处理批次 {batch_num}/{total_batches} ({len(batch_files)} 个文档)")
Message: '📦 处理批次 9/9 (30 个文档)'
Arguments: ()
2025-05-30 14:17:59,923 - __main__ - INFO - 📦 处理批次 9/9 (30 个文档)
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c4' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 240, in run_training_with_monitoring
    batch_chunks = self.training_pipeline.process_documents(batch_documents)
  File "F:\software\md_vector_processor\training_automation.py", line 199, in process_documents
    logger.info("📄 处理文档...")
Message: '📄 处理文档...'
Arguments: ()
2025-05-30 14:17:59,929 - training_automation - INFO - 📄 处理文档...
2025-05-30 14:17:59,935 - training_automation - INFO - 处理 enterprise_standards 类别的 30 个文档
处理enterprise_standards:   0%|                                                                                                                                    | 0/30 [00:00<?, ?it/s]2025-05-30 14:17:59,936 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:01,015 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_CSR_IATF_16949_CH_2017-11_客户特殊
要求.pdf
2025-05-30 14:18:01,017 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:01,017 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:01,018 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:01,018 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:01,019 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:01,019 - src.utils.document_loader - INFO - 总文件大小: 0.43 MB
2025-05-30 14:18:01,020 - src.utils.document_loader - INFO - 总内容长度: 27,765 字符
2025-05-30 14:18:01,020 - src.utils.document_loader - INFO - 平均加载时间: 0.557 秒
2025-05-30 14:18:01,033 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_CSR_IATF_16949_CH_2017-11_客户特
殊要求.pdf
处理enterprise_standards:   3%|████▏                                                                                                                       | 1/30 [00:01<00:31,  1.10s/it]2025-05-30 14:18:01,037 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:02,827 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_CSR_IATF_16949_EN_2017-11_客户特殊
要求.pdf
2025-05-30 14:18:02,829 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:02,829 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:02,830 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:02,831 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:02,831 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:02,832 - src.utils.document_loader - INFO - 总文件大小: 0.66 MB
2025-05-30 14:18:02,832 - src.utils.document_loader - INFO - 总内容长度: 38,797 字符
2025-05-30 14:18:02,833 - src.utils.document_loader - INFO - 平均加载时间: 0.780 秒
2025-05-30 14:18:02,849 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_CSR_IATF_16949_EN_2017-11_客户特
殊要求.pdf
处理enterprise_standards:   7%|████████▎                                                                                                                   | 2/30 [00:02<00:42,  1.52s/it]2025-05-30 14:18:02,856 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:03,138 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_MOST50_EN_2012-02_MOST线束技术要求
.pdf
2025-05-30 14:18:03,139 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:03,140 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:03,140 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:03,141 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:03,141 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:03,141 - src.utils.document_loader - INFO - 总文件大小: 0.19 MB
2025-05-30 14:18:03,141 - src.utils.document_loader - INFO - 总内容长度: 6,970 字符
2025-05-30 14:18:03,142 - src.utils.document_loader - INFO - 平均加载时间: 0.150 秒
2025-05-30 14:18:03,157 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GM_MOST50_EN_2012-02_MOST线束技术要
求.pdf
处理enterprise_standards:  10%|████████████▍                                                                                                               | 3/30 [00:03<00:26,  1.03it/s]2025-05-30 14:18:03,165 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:11,343 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GSSTS_861_EN_2010-08_电源和信号分配及
子系统技术规范.pdf
2025-05-30 14:18:11,344 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:11,344 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:11,345 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:11,345 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:11,346 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:11,346 - src.utils.document_loader - INFO - 总文件大小: 1.01 MB
2025-05-30 14:18:11,346 - src.utils.document_loader - INFO - 总内容长度: 123,198 字符
2025-05-30 14:18:11,347 - src.utils.document_loader - INFO - 平均加载时间: 4.071 秒
2025-05-30 14:18:11,388 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\GSSTS_861_EN_2010-08_电源和信号分配
及子系统技术规范.pdf
处理enterprise_standards:  13%|████████████████▌                                                                                                           | 4/30 [00:11<01:39,  3.84s/it]2025-05-30 14:18:11,402 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:15,923 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_10012_CH_2015-01_泛亚汽车电线束技
术要求.pdf
2025-05-30 14:18:15,924 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:15,925 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:15,925 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:15,926 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:15,927 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:15,927 - src.utils.document_loader - INFO - 总文件大小: 2.56 MB
2025-05-30 14:18:15,928 - src.utils.document_loader - INFO - 总内容长度: 60,167 字符
2025-05-30 14:18:15,929 - src.utils.document_loader - INFO - 平均加载时间: 2.237 秒
2025-05-30 14:18:15,953 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_10012_CH_2015-01_泛亚汽车电线束
技术要求.pdf
处理enterprise_standards:  17%|████████████████████▋                                                                                                       | 5/30 [00:16<01:42,  4.10s/it]2025-05-30 14:18:15,961 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:17,455 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11080_CH-EN_2012-05_车用材料及零
部件散发性能测试标准及要求.pdf
2025-05-30 14:18:17,456 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:17,457 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:17,458 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:17,459 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:17,460 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:17,460 - src.utils.document_loader - INFO - 总文件大小: 0.24 MB
2025-05-30 14:18:17,462 - src.utils.document_loader - INFO - 总内容长度: 12,564 字符
2025-05-30 14:18:17,462 - src.utils.document_loader - INFO - 平均加载时间: 0.734 秒
2025-05-30 14:18:17,469 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11080_CH-EN_2012-05_车用材料及 
零部件散发性能测试标准及要求.pdf
处理enterprise_standards:  20%|████████████████████████▊                                                                                                   | 6/30 [00:17<01:17,  3.22s/it]2025-05-30 14:18:17,473 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:19,753 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11080_CH-EN_2014-01_车用材料及零
部件散发性能测试标准及要求.pdf
2025-05-30 14:18:19,754 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:19,755 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:19,755 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:19,756 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:19,756 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:19,756 - src.utils.document_loader - INFO - 总文件大小: 0.55 MB
2025-05-30 14:18:19,757 - src.utils.document_loader - INFO - 总内容长度: 18,056 字符
2025-05-30 14:18:19,757 - src.utils.document_loader - INFO - 平均加载时间: 1.154 秒
2025-05-30 14:18:19,766 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11080_CH-EN_2014-01_车用材料及 
零部件散发性能测试标准及要求.pdf
处理enterprise_standards:  23%|████████████████████████████▉                                                                                               | 7/30 [00:19<01:07,  2.92s/it]2025-05-30 14:18:19,769 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:21,633 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11106_CH_2017-11_电源及线束零件的
发布要求.pdf
2025-05-30 14:18:21,634 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:21,635 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:21,636 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:21,637 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:21,637 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:21,638 - src.utils.document_loader - INFO - 总文件大小: 1.15 MB
2025-05-30 14:18:21,638 - src.utils.document_loader - INFO - 总内容长度: 13,708 字符
2025-05-30 14:18:21,639 - src.utils.document_loader - INFO - 平均加载时间: 0.903 秒
2025-05-30 14:18:21,664 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\PES_11106_CH_2017-11_电源及线束零件
的发布要求.pdf
处理enterprise_standards:  27%|█████████████████████████████████                                                                                           | 8/30 [00:21<00:57,  2.59s/it]2025-05-30 14:18:21,667 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:21,724 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:21,724 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:21,725 - src.utils.document_loader - INFO - 成功加载: 0
2025-05-30 14:18:21,725 - src.utils.document_loader - INFO - 加载失败: 1
2025-05-30 14:18:21,726 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:21,726 - src.utils.document_loader - INFO - 总文件大小: 0.00 MB
2025-05-30 14:18:21,726 - src.utils.document_loader - INFO - 总内容长度: 0 字符
2025-05-30 14:18:21,727 - src.utils.document_loader - INFO - 平均加载时间: 0.000 秒
2025-05-30 14:18:21,727 - src.utils.document_loader - WARNING - 加载失败的文件:
2025-05-30 14:18:21,727 - src.utils.document_loader - WARNING -   training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\SGM_SQ_SOR_CH_2010-06_上海通用汽车对供应商质量
要求的规定-V2.5.pdf: 完整性验证失败: 文档内容为空
2025-05-30 14:18:21,728 - training_automation - ERROR - 处理文档 training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\SGM_SQ_SOR_CH_2010-06_上海通用汽车对供应商质量 
要求的规定-V2.5.pdf 时出错: 文档加载失败
2025-05-30 14:18:21,729 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:29,972 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\SGM_SQ_SOR_EN-CH_2014-12_上海通用汽车
对供应商质量要求的规定-V3.0.pdf
2025-05-30 14:18:29,973 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:29,974 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:29,975 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:29,976 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:29,977 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:29,977 - src.utils.document_loader - INFO - 总文件大小: 0.95 MB
2025-05-30 14:18:29,978 - src.utils.document_loader - INFO - 总内容长度: 130,192 字符
2025-05-30 14:18:29,979 - src.utils.document_loader - INFO - 平均加载时间: 4.061 秒
2025-05-30 14:18:30,048 - training_automation - INFO - 标准文档豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\SGM_SQ_SOR_EN-CH_2014-12_上海通用汽
车对供应商质量要求的规定-V3.0.pdf
处理enterprise_standards:  33%|█████████████████████████████████████████                                                                                  | 10/30 [00:30<01:07,  3.36s/it]2025-05-30 14:18:30,068 - src.utils.document_loader - INFO - 开始加载 1 个文档
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\xa0' in position 170: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 240, in run_training_with_monitoring
    batch_chunks = self.training_pipeline.process_documents(batch_documents)
  File "F:\software\md_vector_processor\training_automation.py", line 211, in process_documents
    chunks = self._process_single_document(file_path, category)
  File "F:\software\md_vector_processor\training_automation.py", line 236, in _process_single_document
    documents, doc_infos = loader.load_documents([str(file_path)])
  File "F:\software\md_vector_processor\src\utils\document_loader.py", line 98, in load_documents
    self.logger.info(f"成功加载文档: {file_path}")
Message: '成功加载文档: training_data\\raw_documents\\enterprise_standards\\OEM_standards\\06_通用\\GMW\\STBA_006_CH-EN_2008-12_塑料零件上\xa0SGM\xa0标识要求.pdf'
Arguments: ()
2025-05-30 14:18:30,263 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\STBA_006_CH-EN_2008-12_塑料零件上 SGM 标识要求.pdf
2025-05-30 14:18:30,271 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:30,272 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:30,272 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:30,273 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:30,273 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:30,274 - src.utils.document_loader - INFO - 总文件大小: 0.12 MB
2025-05-30 14:18:30,274 - src.utils.document_loader - INFO - 总内容长度: 6,526 字符
2025-05-30 14:18:30,275 - src.utils.document_loader - INFO - 平均加载时间: 0.100 秒
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\xa0' in position 168: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 240, in run_training_with_monitoring
    batch_chunks = self.training_pipeline.process_documents(batch_documents)
  File "F:\software\md_vector_processor\training_automation.py", line 211, in process_documents
    chunks = self._process_single_document(file_path, category)
  File "F:\software\md_vector_processor\training_automation.py", line 254, in _process_single_document
    if not self._validate_document_quality(content, file_path):
  File "F:\software\md_vector_processor\training_automation.py", line 324, in _validate_document_quality
    logger.info(f"企业标准豁免质量检查: {file_path}")
Message: '企业标准豁免质量检查: training_data\\raw_documents\\enterprise_standards\\OEM_standards\\06_通用\\GMW\\STBA_006_CH-EN_2008-12_塑料零件上\xa0SGM\xa0标识要求.pdf'
Arguments: ()
2025-05-30 14:18:30,279 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\STBA_006_CH-EN_2008-12_塑料零件上 SGM 标识要求.pdf
处理enterprise_standards:  37%|█████████████████████████████████████████████                                                                              | 11/30 [00:30<00:48,  2.56s/it2 
025-05-30 14:18:30,292 - src.utils.document_loader - INFO - 开始加载 1 个文档
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\xa0' in position 167: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 240, in run_training_with_monitoring
    batch_chunks = self.training_pipeline.process_documents(batch_documents)
  File "F:\software\md_vector_processor\training_automation.py", line 211, in process_documents
    chunks = self._process_single_document(file_path, category)
  File "F:\software\md_vector_processor\training_automation.py", line 236, in _process_single_document
    documents, doc_infos = loader.load_documents([str(file_path)])
  File "F:\software\md_vector_processor\src\utils\document_loader.py", line 98, in load_documents
    self.logger.info(f"成功加载文档: {file_path}")
Message: '成功加载文档: training_data\\raw_documents\\enterprise_standards\\OEM_standards\\06_通用\\GMW\\STBA_006_CH_2008-12_塑料零件上\xa0SGM\xa0标识要求.pdf'
Arguments: ()
2025-05-30 14:18:30,659 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\STBA_006_CH_2008-12_塑料零件上 SGM 标
识要求.pdf
2025-05-30 14:18:30,669 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:30,670 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:30,670 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:30,671 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:30,671 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:30,671 - src.utils.document_loader - INFO - 总文件大小: 0.32 MB
2025-05-30 14:18:30,672 - src.utils.document_loader - INFO - 总内容长度: 3,671 字符
2025-05-30 14:18:30,673 - src.utils.document_loader - INFO - 平均加载时间: 0.207 秒
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\xa0' in position 165: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 240, in run_training_with_monitoring
    batch_chunks = self.training_pipeline.process_documents(batch_documents)
  File "F:\software\md_vector_processor\training_automation.py", line 211, in process_documents
    chunks = self._process_single_document(file_path, category)
  File "F:\software\md_vector_processor\training_automation.py", line 254, in _process_single_document
    if not self._validate_document_quality(content, file_path):
  File "F:\software\md_vector_processor\training_automation.py", line 324, in _validate_document_quality
    logger.info(f"企业标准豁免质量检查: {file_path}")
Message: '企业标准豁免质量检查: training_data\\raw_documents\\enterprise_standards\\OEM_standards\\06_通用\\GMW\\STBA_006_CH_2008-12_塑料零件上\xa0SGM\xa0标识要求.pdf'
Arguments: ()
2025-05-30 14:18:30,675 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\GMW\STBA_006_CH_2008-12_塑料零件上 SGM 标识要求.pdf
处理enterprise_standards:  40%|█████████████████████████████████████████████████▏                                                                         | 12/30 [00:30<00:35,  1.99s/it]2025-05-30 14:18:30,685 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:35,217 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_10012_CH_2015-01_泛亚汽车电线束
技术要求.pdf
2025-05-30 14:18:35,218 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:35,219 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:35,220 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:35,221 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:35,221 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:35,222 - src.utils.document_loader - INFO - 总文件大小: 2.56 MB
2025-05-30 14:18:35,222 - src.utils.document_loader - INFO - 总内容长度: 60,167 字符
2025-05-30 14:18:35,222 - src.utils.document_loader - INFO - 平均加载时间: 2.228 秒
2025-05-30 14:18:35,245 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_10012_CH_2015-01_泛亚汽车电线
束技术要求.pdf
处理enterprise_standards:  43%|█████████████████████████████████████████████████████▎                                                                     | 13/30 [00:35<00:45,  2.70s/it]2025-05-30 14:18:35,254 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:36,407 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11080_CH-EN_2012-05_车用材料及零
部件散发性能测试标准及要求.pdf
2025-05-30 14:18:36,409 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:36,410 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:36,410 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:36,411 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:36,412 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:36,413 - src.utils.document_loader - INFO - 总文件大小: 0.24 MB
2025-05-30 14:18:36,413 - src.utils.document_loader - INFO - 总内容长度: 12,564 字符
2025-05-30 14:18:36,414 - src.utils.document_loader - INFO - 平均加载时间: 0.465 秒
2025-05-30 14:18:36,438 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11080_CH-EN_2012-05_车用材料及
零部件散发性能测试标准及要求.pdf
处理enterprise_standards:  47%|█████████████████████████████████████████████████████████▍                                                                 | 14/30 [00:36<00:36,  2.27s/it]2025-05-30 14:18:36,442 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:38,806 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11080_CH-EN_2014-01_车用材料及零
部件散发性能测试标准及要求.pdf
2025-05-30 14:18:38,806 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:38,807 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:38,807 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:38,809 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:38,809 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:38,809 - src.utils.document_loader - INFO - 总文件大小: 0.55 MB
2025-05-30 14:18:38,810 - src.utils.document_loader - INFO - 总内容长度: 18,056 字符
2025-05-30 14:18:38,810 - src.utils.document_loader - INFO - 平均加载时间: 1.088 秒
2025-05-30 14:18:38,819 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11080_CH-EN_2014-01_车用材料及
零部件散发性能测试标准及要求.pdf
处理enterprise_standards:  50%|█████████████████████████████████████████████████████████████▌                                                             | 15/30 [00:38<00:34,  2.30s/it]2025-05-30 14:18:38,822 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:40,400 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11106_CH_2017-11_电源及线束零件
的发布要求.pdf
2025-05-30 14:18:40,402 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:40,403 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:40,404 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:40,405 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:40,406 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:40,406 - src.utils.document_loader - INFO - 总文件大小: 1.15 MB
2025-05-30 14:18:40,407 - src.utils.document_loader - INFO - 总内容长度: 13,708 字符
2025-05-30 14:18:40,408 - src.utils.document_loader - INFO - 平均加载时间: 0.705 秒
2025-05-30 14:18:40,431 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\OEM_standards\06_通用\泛亚\PES_11106_CH_2017-11_电源及线束零
件的发布要求.pdf
处理enterprise_standards:  53%|█████████████████████████████████████████████████████████████████▌                                                         | 16/30 [00:40<00:29,  2.10s/it]2025-05-30 14:18:40,436 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:40,527 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:40,528 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:40,529 - src.utils.document_loader - INFO - 成功加载: 0
2025-05-30 14:18:40,529 - src.utils.document_loader - INFO - 加载失败: 1
2025-05-30 14:18:40,530 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:40,530 - src.utils.document_loader - INFO - 总文件大小: 0.00 MB
2025-05-30 14:18:40,531 - src.utils.document_loader - INFO - 总内容长度: 0 字符
2025-05-30 14:18:40,531 - src.utils.document_loader - INFO - 平均加载时间: 0.000 秒
2025-05-30 14:18:40,532 - src.utils.document_loader - WARNING - 加载失败的文件:
2025-05-30 14:18:40,532 - src.utils.document_loader - WARNING -   training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-GCZTBZ-1_CH_2019-03_Capital软件工程制图标准.pdf: 完整性验证失败: 文档内容为空
2025-05-30 14:18:40,533 - training_automation - ERROR - 处理文档 training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-GCZTBZ-1_CH_2019-03_Capital软件工程制图标准.pdf 时出错: 文档加载失败
处理enterprise_standards:  57%|█████████████████████████████████████████████████████████████████████▋                                                     | 17/30 [00:40<00:19,  1.51s/it]2025-05-30 14:18:40,535 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:40,591 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:40,592 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:40,593 - src.utils.document_loader - INFO - 成功加载: 0
2025-05-30 14:18:40,593 - src.utils.document_loader - INFO - 加载失败: 1
2025-05-30 14:18:40,594 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:40,594 - src.utils.document_loader - INFO - 总文件大小: 0.00 MB
2025-05-30 14:18:40,594 - src.utils.document_loader - INFO - 总内容长度: 0 字符
2025-05-30 14:18:40,595 - src.utils.document_loader - INFO - 平均加载时间: 0.000 秒
2025-05-30 14:18:40,595 - src.utils.document_loader - WARNING - 加载失败的文件:
2025-05-30 14:18:40,596 - src.utils.document_loader - WARNING -   training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-GYSYKHCHBMBZ-1_CH_2018-08_昆山沪光零部件供应商
存货编码与客户存货编码标准.pdf: 完整性验证失败: 文档内容为空
2025-05-30 14:18:40,597 - training_automation - ERROR - 处理文档 training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-GYSYKHCHBMBZ-1_CH_2018-08_昆山沪光零部件供应商 
存货编码与客户存货编码标准.pdf 时出错: 文档加载失败
2025-05-30 14:18:40,599 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:44,836 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-HJBZ-1_CH_2016-10_沪光线束超声波焊接
标准.pdf
2025-05-30 14:18:44,838 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:44,839 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:44,839 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:44,840 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:44,841 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:44,841 - src.utils.document_loader - INFO - 总文件大小: 1.06 MB
2025-05-30 14:18:44,842 - src.utils.document_loader - INFO - 总内容长度: 13,954 字符
2025-05-30 14:18:44,843 - src.utils.document_loader - INFO - 平均加载时间: 2.178 秒
2025-05-30 14:18:44,870 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-HJBZ-1_CH_2016-10_沪光线束超声波焊
接标准.pdf
处理enterprise_standards:  63%|█████████████████████████████████████████████████████████████████████████████▉                                             | 19/30 [00:44<00:19,  1.81s/it]2025-05-30 14:18:44,874 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:18:45,846 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-HJBZ-1_CH_2018-09_沪光线束超声波焊接
标准.pdf
2025-05-30 14:18:45,847 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:18:45,848 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:18:45,848 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:18:45,848 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:18:45,849 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:18:45,849 - src.utils.document_loader - INFO - 总文件大小: 0.84 MB
2025-05-30 14:18:45,850 - src.utils.document_loader - INFO - 总内容长度: 11,984 字符
2025-05-30 14:18:45,850 - src.utils.document_loader - INFO - 平均加载时间: 0.545 秒
2025-05-30 14:18:45,857 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-HJBZ-1_CH_2018-09_沪光线束超声波焊 
接标准.pdf
处理enterprise_standards:  67%|██████████████████████████████████████████████████████████████████████████████████                                         | 20/30 [00:45<00:16,  1.61s/it]2025-05-30 14:18:45,860 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:12,449 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-JS.HV.004_CH_2019-03_昆山沪光汽车高压
电线束压接标准.pdf
2025-05-30 14:19:12,451 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:12,451 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:12,452 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:12,452 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:12,453 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:12,453 - src.utils.document_loader - INFO - 总文件大小: 1.57 MB
2025-05-30 14:19:12,454 - src.utils.document_loader - INFO - 总内容长度: 12,573 字符
2025-05-30 14:19:12,454 - src.utils.document_loader - INFO - 平均加载时间: 13.019 秒
2025-05-30 14:19:12,462 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-JS.HV.004_CH_2019-03_昆山沪光汽车高
压电线束压接标准.pdf
处理enterprise_standards:  70%|██████████████████████████████████████████████████████████████████████████████████████                                     | 21/30 [01:12<01:12,  8.11s/it]2025-05-30 14:19:12,467 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:16,490 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-JXBZ-1_CH_2016-10_沪光绞线标准.pdf
2025-05-30 14:19:16,491 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:16,492 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:16,492 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:16,492 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:16,493 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:16,493 - src.utils.document_loader - INFO - 总文件大小: 0.63 MB
2025-05-30 14:19:16,494 - src.utils.document_loader - INFO - 总内容长度: 13,516 字符
2025-05-30 14:19:16,494 - src.utils.document_loader - INFO - 平均加载时间: 1.978 秒
2025-05-30 14:19:16,502 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-JXBZ-1_CH_2016-10_沪光绞线标准.pdf 
处理enterprise_standards:  73%|██████████████████████████████████████████████████████████████████████████████████████████▏                                | 22/30 [01:16<00:56,  7.00s/it]2025-05-30 14:19:16,504 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:18,062 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-RSBZ-1_CH_2016-10_沪光线束热缩标准.pdf
2025-05-30 14:19:18,063 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:18,064 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:18,064 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:18,065 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:18,065 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:18,066 - src.utils.document_loader - INFO - 总文件大小: 0.41 MB
2025-05-30 14:19:18,066 - src.utils.document_loader - INFO - 总内容长度: 5,088 字符
2025-05-30 14:19:18,067 - src.utils.document_loader - INFO - 平均加载时间: 0.782 秒
2025-05-30 14:19:18,070 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-RSBZ-1_CH_2016-10_沪光线束热缩标准.pdf
处理enterprise_standards:  77%|██████████████████████████████████████████████████████████████████████████████████████████████▎                            | 23/30 [01:18<00:38,  5.49s/it]2025-05-30 14:19:18,072 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:18,080 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:18,080 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:18,081 - src.utils.document_loader - INFO - 成功加载: 0
2025-05-30 14:19:18,081 - src.utils.document_loader - INFO - 加载失败: 1
2025-05-30 14:19:18,081 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:18,082 - src.utils.document_loader - INFO - 总文件大小: 0.00 MB
2025-05-30 14:19:18,082 - src.utils.document_loader - INFO - 总内容长度: 0 字符
2025-05-30 14:19:18,082 - src.utils.document_loader - INFO - 平均加载时间: 0.000 秒
2025-05-30 14:19:18,082 - src.utils.document_loader - WARNING - 加载失败的文件:
2025-05-30 14:19:18,083 - src.utils.document_loader - WARNING -   training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-WGZCJTZGLBZ-1_CH_2019-03_外购总成件图纸管理标 
准.pdf: 完整性验证失败: 文档内容为空
2025-05-30 14:19:18,083 - training_automation - ERROR - 处理文档 training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-WGZCJTZGLBZ-1_CH_2019-03_外购总成件图纸管理标准
.pdf 时出错: 文档加载失败
2025-05-30 14:19:18,084 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:21,366 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-XSZCBZ-1_CH_2017-06_沪光线束总成制造
标准.pdf
2025-05-30 14:19:21,367 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:21,368 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:21,368 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:21,369 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:21,369 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:21,370 - src.utils.document_loader - INFO - 总文件大小: 0.46 MB
2025-05-30 14:19:21,370 - src.utils.document_loader - INFO - 总内容长度: 15,190 字符
2025-05-30 14:19:21,371 - src.utils.document_loader - INFO - 平均加载时间: 1.637 秒
2025-05-30 14:19:21,380 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-XSZCBZ-1_CH_2017-06_沪光线束总成制 
造标准.pdf
处理enterprise_standards:  83%|██████████████████████████████████████████████████████████████████████████████████████████████████████▌                    | 25/30 [01:21<00:18,  3.79s/it]2025-05-30 14:19:21,383 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:22,032 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-XSZCBZ-1_CH_2018-09_沪光线束总成制造
标准.pdf
2025-05-30 14:19:22,033 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:22,033 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:22,034 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:22,034 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:22,035 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:22,035 - src.utils.document_loader - INFO - 总文件大小: 0.41 MB
2025-05-30 14:19:22,035 - src.utils.document_loader - INFO - 总内容长度: 12,481 字符
2025-05-30 14:19:22,036 - src.utils.document_loader - INFO - 平均加载时间: 0.325 秒
2025-05-30 14:19:22,044 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-XSZCBZ-1_CH_2018-09_沪光线束总成制 
造标准.pdf
处理enterprise_standards:  87%|██████████████████████████████████████████████████████████████████████████████████████████████████████████▌                | 26/30 [01:22<00:12,  3.04s/it]2025-05-30 14:19:22,046 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:30,646 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-1_CH_2016-10_沪光压接标准-开式压
接的接插件端子.pdf
2025-05-30 14:19:30,647 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:30,648 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:30,648 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:30,649 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:30,649 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:30,649 - src.utils.document_loader - INFO - 总文件大小: 1.56 MB
2025-05-30 14:19:30,650 - src.utils.document_loader - INFO - 总内容长度: 26,528 字符
2025-05-30 14:19:30,650 - src.utils.document_loader - INFO - 平均加载时间: 4.325 秒
2025-05-30 14:19:30,663 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-1_CH_2016-10_沪光压接标准-开式
压接的接插件端子.pdf
处理enterprise_standards:  90%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████▋            | 27/30 [01:30<00:13,  4.46s/it]2025-05-30 14:19:30,666 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:34,871 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-2_CH_2016-10_沪光压接标准-封闭式
压接筒电瓶端子.pdf
2025-05-30 14:19:34,872 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:34,872 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:34,873 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:34,873 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:34,874 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:34,874 - src.utils.document_loader - INFO - 总文件大小: 0.73 MB
2025-05-30 14:19:34,875 - src.utils.document_loader - INFO - 总内容长度: 14,359 字符
2025-05-30 14:19:34,875 - src.utils.document_loader - INFO - 平均加载时间: 2.237 秒
2025-05-30 14:19:34,890 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-2_CH_2016-10_沪光压接标准-封闭
式压接筒电瓶端子.pdf
处理enterprise_standards:  93%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████▊        | 28/30 [01:34<00:08,  4.40s/it]2025-05-30 14:19:34,893 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:38,472 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-3_CH_2017-06_沪光压接标准-挂点连
接-封闭式在线压接筒.pdf
2025-05-30 14:19:38,473 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:38,474 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:38,474 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:38,474 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:38,475 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:38,475 - src.utils.document_loader - INFO - 总文件大小: 0.85 MB
2025-05-30 14:19:38,476 - src.utils.document_loader - INFO - 总内容长度: 12,834 字符
2025-05-30 14:19:38,476 - src.utils.document_loader - INFO - 平均加载时间: 1.807 秒
2025-05-30 14:19:38,487 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-3_CH_2017-06_沪光压接标准-挂点
连接-封闭式在线压接筒.pdf
处理enterprise_standards:  97%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉    | 29/30 [01:38<00:04,  4.18s/it]2025-05-30 14:19:38,491 - src.utils.document_loader - INFO - 开始加载 1 个文档
2025-05-30 14:19:45,124 - src.utils.document_loader - INFO - 成功加载文档: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-4_CH_2016-10_沪光压接标准-挂点连
接-U型端子.pdf
2025-05-30 14:19:45,126 - src.utils.document_loader - INFO - === 文档加载报告 ===
2025-05-30 14:19:45,127 - src.utils.document_loader - INFO - 总文档数: 1
2025-05-30 14:19:45,127 - src.utils.document_loader - INFO - 成功加载: 1
2025-05-30 14:19:45,127 - src.utils.document_loader - INFO - 加载失败: 0
2025-05-30 14:19:45,128 - src.utils.document_loader - INFO - 跳过文件: 0
2025-05-30 14:19:45,129 - src.utils.document_loader - INFO - 总文件大小: 1.17 MB
2025-05-30 14:19:45,129 - src.utils.document_loader - INFO - 总内容长度: 20,404 字符
2025-05-30 14:19:45,130 - src.utils.document_loader - INFO - 平均加载时间: 3.370 秒
2025-05-30 14:19:45,141 - training_automation - INFO - 企业标准豁免质量检查: training_data\raw_documents\enterprise_standards\supplier_specs\01_沪光\HG-YJBZ-4_CH_2016-10_沪光压接标准-挂点
连接-U型端子.pdf
处理enterprise_standards: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 30/30 [01:45<00:00,  3.51s/it] 
2025-05-30 14:19:45,144 - training_automation - INFO - 文档处理完成，生成 647 个文本块
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c8' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 246, in run_training_with_monitoring
    logger.info(f"📈 类别 {category} 进度: {progress:.1f}% ({processed}/{len(files)})")
Message: '📈 类别 enterprise_standards 进度: 100.0% (830/830)'
Arguments: ()
2025-05-30 14:19:45,145 - __main__ - INFO - 📈 类别 enterprise_standards 进度: 100.0% (830/830)
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 251, in run_training_with_monitoring
    logger.info(f"📝 总共生成 {len(all_chunks)} 个文本块")
Message: '📝 总共生成 36904 个文本块'
Arguments: ()
2025-05-30 14:19:45,151 - __main__ - INFO - 📝 总共生成 36904 个文本块
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f522' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 254, in run_training_with_monitoring
    logger.info("🔢 开始向量化...")
Message: '🔢 开始向量化...'
Arguments: ()
2025-05-30 14:19:45,157 - __main__ - INFO - 🔢 开始向量化...
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f522' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 255, in run_training_with_monitoring
    vectors, vector_ids, processed_chunks = self.training_pipeline.vectorize_chunks(all_chunks)
  File "F:\software\md_vector_processor\training_automation.py", line 398, in vectorize_chunks
    logger.info("🔢 向量化文本块...")
Message: '🔢 向量化文本块...'
Arguments: ()
2025-05-30 14:19:45,163 - training_automation - INFO - 🔢 向量化文本块...
2025-05-30 14:19:58,090 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
2025-05-30 14:20:03,749 - src.vectorizer.embeddings - INFO - 成功加载模型: sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2 (类型: sentence-transformer)
2025-05-30 14:20:03,773 - src.vectorizer.embeddings - INFO - 加载了 180 个缓存向量
向量化: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1154/1154 [31:02<00:00,  1.61s/it]
2025-05-30 14:51:05,973 - training_automation - INFO - 成功生成 36904 个向量，维度: 384
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3d7' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 258, in run_training_with_monitoring
    logger.info("🏗️ 开始索引训练...")
Message: '🏗️ 开始索引训练...'
Arguments: ()
2025-05-30 14:51:06,019 - __main__ - INFO - 🏗️ 开始索引训练...
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3d7' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 259, in run_training_with_monitoring
    trained_indices = self.training_pipeline.train_indices(vectors, vector_ids)
  File "F:\software\md_vector_processor\training_automation.py", line 453, in train_indices
    logger.info("🏗️ 训练索引...")
Message: '🏗️ 训练索引...'
Arguments: ()
2025-05-30 14:51:06,023 - training_automation - INFO - 🏗️ 训练索引...
2025-05-30 14:51:06,026 - training_automation - INFO - 训练IVF索引 (向量数: 36904)
2025-05-30 14:51:06,028 - training_automation - INFO - 开始训练IVF索引 (nlist=19)
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 259, in run_training_with_monitoring
    trained_indices = self.training_pipeline.train_indices(vectors, vector_ids)
  File "F:\software\md_vector_processor\training_automation.py", line 493, in train_indices
    logger.info(f"✅ IVF索引训练完成，耗时: {training_time:.2f}秒，保存至: {ivf_path}")
Message: '✅ IVF索引训练完成，耗时: 0.78秒，保存至: training_data/models/ivf_indices/automotive_ivf_20250530_145106.idx'
Arguments: ()
2025-05-30 14:51:06,910 - training_automation - INFO - ✅ IVF索引训练完成，耗时: 0.78秒，保存至: training_data/models/ivf_indices/automotive_ivf_20250530_145106.idx
2025-05-30 14:51:06,915 - training_automation - INFO - 训练PQ索引 (向量数: 36904)
2025-05-30 14:51:06,915 - training_automation - INFO - 开始训练PQ索引 (m=8, bits=8)
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 259, in run_training_with_monitoring
    trained_indices = self.training_pipeline.train_indices(vectors, vector_ids)
  File "F:\software\md_vector_processor\training_automation.py", line 522, in train_indices
    logger.info(f"✅ PQ索引训练完成，耗时: {training_time:.2f}秒，保存至: {pq_path}")
Message: '✅ PQ索引训练完成，耗时: 4.92秒，保存至: training_data/models/pq_indices/automotive_pq_20250530_145112.idx'
Arguments: ()
2025-05-30 14:51:12,010 - training_automation - INFO - ✅ PQ索引训练完成，耗时: 4.92秒，保存至: training_data/models/pq_indices/automotive_pq_20250530_145112.idx
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4be' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 262, in run_training_with_monitoring
    logger.info("💾 保存训练数据...")
Message: '💾 保存训练数据...'
Arguments: ()
2025-05-30 14:51:12,025 - __main__ - INFO - 💾 保存训练数据...
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4be' in position 55: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 263, in run_training_with_monitoring
    self.training_pipeline.save_training_data(processed_chunks, vectors, vector_ids)
  File "F:\software\md_vector_processor\training_automation.py", line 532, in save_training_data
    logger.info("💾 保存训练数据...")
Message: '💾 保存训练数据...'
Arguments: ()
2025-05-30 14:51:12,028 - training_automation - INFO - 💾 保存训练数据...
2025-05-30 14:51:17,170 - training_automation - INFO - 训练数据已保存: training_data/training_vectors/automotive_vectors_20250530_145112.npz, training_data/processed_documents/metadata/automotive_metadata_20250530_145115.json
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f50d' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 266, in run_training_with_monitoring
    self.monitor.stop_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 57, in stop_monitoring
    logger.info("🔍 系统监控已停止")
Message: '🔍 系统监控已停止'
Arguments: ()
2025-05-30 14:51:17,171 - __main__ - INFO - 🔍 系统监控已停止
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4ca' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 269, in run_training_with_monitoring
    self._generate_final_report(len(all_chunks), len(vectors), trained_indices)
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 281, in _generate_final_report
    logger.info("📊 生成训练报告...")
Message: '📊 生成训练报告...'
Arguments: ()
2025-05-30 14:51:17,177 - __main__ - INFO - 📊 生成训练报告...

================================================================================
🎯 训练完成报告
================================================================================
📝 生成文本块: 36,904
🔢 生成向量: 36,904
🏗️ 训练索引: 2
⏱️ 运行时间: 66.8 分钟
💾 最大内存使用: 71.9%
🖥️ 平均CPU使用: 43.0%
📄 详细报告: training_report_20250530_145117.json
================================================================================
--- Logging error ---
Traceback (most recent call last):
  File "D:\Miniforge\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 44: illegal multibyte sequence
Call stack:
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 355, in <module>
    success = main()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 345, in main
    success = launcher.run_training_with_monitoring()
  File "F:\software\md_vector_processor\optimized_training_launcher.py", line 271, in run_training_with_monitoring
    logger.info("🎉 训练完成!")
Message: '🎉 训练完成!'
Arguments: ()
2025-05-30 14:51:17,192 - __main__ - INFO - 🎉 训练完成!
🎉 训练成功完成!