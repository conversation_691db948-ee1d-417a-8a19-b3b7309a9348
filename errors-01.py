(base) PS F:\software\md_vector_processor> 2025-05-12 15:43:23,532 - faiss - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.        
>> 2025-05-12 15:43:23,627 - src.gui.widgets.index - INFO - 创建384维度的索引...
>> 2025-05-12 15:43:23,627 - src.indexer.builder - INFO - 创建了新的flat索引，维度: 384
>> 2025-05-12 15:43:23,628 - src.gui.widgets.index - INFO - 保存索引到 data\indices\new_index.idx...
>> 2025-05-12 15:43:23,628 - src.indexer.builder - ERROR - 保存索引时出错: 'IndexFlatIP' object has no attribute 'save_index'
>> 2025-05-12 15:43:23,630 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:23,634 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> 2025-05-12 15:43:54,539 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:54,548 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> 2025-05-12 15:43:55,533 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:55,567 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\dashboard.py", line 289, in _on_create_index
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'
哎呀，出现问题。请在报告此 Bug 时添加以下详细信息。
在 GitHub 上报告: https://github.com/lzybkr/PSReadLine/issues/new
-----------------------------------------------------------------------
上 200 个密钥:
 n d e x Enter
 Space Space Space Space f o r Space i Space i n Space r a n g e ( s e l f . p a r e n t ( ) . t a b _ w i d g e t . c o u n t ( ) ) : Enter
 Space Space Space Space Space Space Space Space Space Space Space Space Space Space Space Space Space Space Space ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ Enter
 A t t r i b u t e E r r o r : Space ' Q S t a c k e d W i d g e t ' Space o b j e c t Space h a s Space n o Space a t t r i b u t e Space ' t a b _ w i d g e t ' . Space D i d Space y o u Space m e a n : Space ' a d d W i d g e t ' ?

异常:
System.ArgumentOutOfRangeException: 该值必须大于或等于零，且必须小于控制台缓冲区在该维度的大小。
参数名: top
实际值是 -10。
   在 System.Console.SetCursorPosition(Int32 left, Int32 top)
   在 Microsoft.PowerShell.PSConsoleReadLine.ReallyRender(RenderData renderData, String defaultColor)
   在 Microsoft.PowerShell.PSConsoleReadLine.ForceRender()
   在 Microsoft.PowerShell.PSConsoleReadLine.Insert(Char c)
   在 Microsoft.PowerShell.PSConsoleReadLine.SelfInsert(Nullable`1 key, Object arg)
   在 Microsoft.PowerShell.PSConsoleReadLine.ProcessOneKey(ConsoleKeyInfo key, Dictionary`2 dispatchTable, Boolean ignoreIfNoAction, Object arg)
   在 Microsoft.PowerShell.PSConsoleReadLine.InputLoop()
   在 Microsoft.PowerShell.PSConsoleReadLine.ReadLine(Runspace runspace, EngineIntrinsics engineIntrinsics)
-----------------------------------------------------------------------
(base) PS F:\software\md_vector_processor> 2025-05-12 15:43:23,532 - faiss - INFO - Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.        
>> 2025-05-12 15:43:23,627 - src.gui.widgets.index - INFO - 创建384维度的索引...
>> 2025-05-12 15:43:23,627 - src.indexer.builder - INFO - 创建了新的flat索引，维度: 384
>> 2025-05-12 15:43:23,628 - src.gui.widgets.index - INFO - 保存索引到 data\indices\new_index.idx...
>> 2025-05-12 15:43:23,628 - src.indexer.builder - ERROR - 保存索引时出错: 'IndexFlatIP' object has no attribute 'save_index'
>> 2025-05-12 15:43:23,630 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:23,634 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> 2025-05-12 15:43:54,539 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:54,548 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> 2025-05-12 15:43:55,533 - src.gui.widgets.index - ERROR - 刷新索引列表时出错: 'QStackedWidget' object has no attribute 'tab_widget'
>> 2025-05-12 15:43:55,567 - src.gui.widgets.index - ERROR - Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\index.py", line 340, in _on_refresh_indices
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?
>>
>> Traceback (most recent call last):
>>   File "F:\software\md_vector_processor\src\gui\widgets\dashboard.py", line 289, in _on_create_index
>>     for i in range(self.parent().tab_widget.count()):
>>                    ^^^^^^^^^^^^^^^^^^^^^^^^
>> AttributeError: 'QStackedWidget' object has no attribute 'tab_widget'. Did you mean: 'addWidget'?