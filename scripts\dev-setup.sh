
#!/bin/bash

# 开发环境设置脚本

# 确保脚本在错误时退出
set -e

# 显示欢迎信息
echo "=== MD Vector Processor 开发环境设置 ==="
echo

# 检查必要的工具
echo "检查必要工具..."
command -v docker >/dev/null 2>&1 || { echo "需要安装 Docker"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "需要安装 Docker Compose"; exit 1; }
command -v python3 >/dev/null 2>&1 || { echo "需要安装 Python 3"; exit 1; }

# 创建必要的目录
echo "创建目录结构..."
mkdir -p data/raw data/processed data/vectors
mkdir -p logs
mkdir -p cache
mkdir -p notebooks

# 创建Python虚拟环境
echo "创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 安装依赖
echo "安装项目依赖..."
pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 设置pre-commit hooks
echo "设置Git hooks..."
pre-commit install

# 构建开发容器
echo "构建开发容器..."
docker-compose -f docker-compose-dev.yml build

# 创建示例配置文件
echo "创建配置文件..."
if [ ! -f config/config.yaml ]; then
    cp config/config.yaml.example config/config.yaml
fi

if [ ! -f .env ]; then
    cp .env.example .env
fi

# 运行快速测试
echo "运行快速测试..."
python quick_test.py

echo
echo "=== 开发环境设置完成 ==="
echo
echo "使用以下命令启动开发环境："
echo "docker-compose -f docker-compose-dev.yml up"
echo
echo "访问服务："
echo "- API: http://localhost:8000"
echo "- Jupyter Lab: http://localhost:8888 (token: mdvector)"
echo "- Adminer: http://localhost:8080"
echo "- Redis Commander: http://localhost:8081"
echo
echo "开发工具："
echo "- 运行测试: pytest"
echo "- 代码格式化: black ."
echo "- 类型检查: mypy src"
echo "- 运行API服务: python run.py --mode api --debug --reload"
echo