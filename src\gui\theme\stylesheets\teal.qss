/* 青色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #E0F2F1;
}

QLabel {
    color: #004D40;
}

QPushButton {
    background-color: #00897B;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #00796B;
}

QPushButton:pressed {
    background-color: #00695C;
}

QPushButton:disabled {
    background-color: #B2DFDB;
    color: #80CBC4;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #004D40;
    border: 1px solid #80CBC4;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #00897B;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #E0F2F1;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #80CBC4;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #E0F2F1;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #80CBC4;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #00897B;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #00796B;
}

QMenu {
    background-color: #FFFFFF;
    color: #004D40;
    border: 1px solid #80CBC4;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #E0F2F1;
}

QTabWidget::pane {
    border: 1px solid #80CBC4;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #B2DFDB;
    color: #004D40;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #00897B;
}

QStatusBar {
    background-color: #00897B;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #00897B;
}

#sidebarWidget {
    background-color: #00897B;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #E0F2F1;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #80CBC4;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #00897B;
    margin-bottom: 8px;
}

.card-content {
    color: #004D40;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #00897B;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #00897B;
    box-shadow: 0 0 10px #00897B;
}
