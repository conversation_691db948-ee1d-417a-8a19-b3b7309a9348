
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试脚本
用于快速验证项目的核心功能
"""

import sys
from pathlib import Path
import logging
from typing import List, Dict, Any

from src.utils import setup_logger, ConfigUtils, Timer
from src.data_loader import FileScanner, MDLoader
from src.preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from src.vectorizer import TextEmbedding, VectorTransformer
from src.indexer import IndexBuilder, VectorSearcher
from src.storage import VectorStore, MetadataManager

def create_test_documents() -> List[str]:
    """创建测试文档"""
    return [
        """# 测试文档1
        
        这是第一个测试文档，用于测试基本功能。
        
        ## 特点
        * 简单的Markdown格式
        * 包含中文内容
        * 用于测试目的
        
        详细信息请访问[示例链接](http://example.com)
        """,
        
        """# 测试文档2
        
        这是第二个测试文档，用于验证相似度搜索。
        
        ## 功能测试
        1. 文本处理
        2. 向量化
        3. 索引搜索
        
        ```python
        print("Hello, World!")
        ```
        """,
        
        """# Test Document 3
        
        This is a test document in English.
        
        ## Features
        * Multi-language support
        * Vector similarity
        * Search functionality
        
        For more information, check the documentation.
        """
    ]

def run_quick_test():
    """运行快速测试"""
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        config = ConfigUtils.load_config('config/config.yaml')
        if not config:
            raise ValueError("无法加载配置文件")
        
        # 创建测试文档
        test_docs = create_test_documents()
        logger.info(f"创建了 {len(test_docs)} 个测试文档")
        
        # 初始化组件
        cleaner = TextCleaner(config)
        normalizer = TextNormalizer(config)
        tokenizer = TextTokenizer(config)
        embedder = TextEmbedding(config)
        transformer = VectorTransformer(config)
        index_builder = IndexBuilder(config)
        searcher = VectorSearcher(config)
        vector_store = VectorStore(config)
        metadata_manager = MetadataManager(config)
        
        # 处理文档
        logger.info("\n1. 测试文档处理")
        all_vectors = []
        for i, doc in enumerate(test_docs):
            with Timer() as t:
                # 清理和标准化
                cleaned = cleaner.clean_text(doc)
                normalized = normalizer.normalize_text(cleaned)
                tokens = tokenizer.tokenize_text(normalized)
                
                # 向量化
                vector = embedder.encode_text(' '.join(tokens))
                all_vectors.append(vector)
                
                # 存储
                vector_store.store_vectors(vector.reshape(1, -1))
                metadata_manager.store_metadata(i, {
                    'text': doc,
                    'tokens': tokens
                })
            
            logger.info(f"处理文档 {i+1} 完成，耗时：{t.elapsed:.3f}秒")
        
        # 构建索引
        logger.info("\n2. 测试索引构建")
        with Timer() as t:
            index_builder.create_index(embedder.vector_dimension)
            index_builder.add_vectors(np.vstack(all_vectors))
            searcher.set_index(index_builder.index)
            searcher.set_metadata(metadata_manager.get_all_metadata())
        logger.info(f"索引构建完成，耗时：{t.elapsed:.3f}秒")
        
        # 测试搜索
        logger.info("\n3. 测试搜索功能")
        test_queries = [
            "测试文档",
            "Python programming",
            "向量相似度"
        ]
        
        for query in test_queries:
            with Timer() as t:
                # 编码查询
                query_vector = embedder.encode_text(query)
                
                # 搜索
                results = searcher.search(query_vector, k=2)
                
                logger.info(f"\n查询: {query}")
                logger.info(f"搜索耗时: {t.elapsed:.3f}秒")
                
                # 显示结果
                for i, result in enumerate(results, 1):
                    metadata = metadata_manager.get_metadata(result.id)
                    logger.info(f"\n结果 {i}:")
                    logger.info(f"相似度: {result.score:.4f}")
                    logger.info(f"文档片段: {metadata['text'][:100]}...")
        
        logger.info("\n快速测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False

if __name__ == '__main__':
    success = run_quick_test()
    sys.exit(0 if success else 1)