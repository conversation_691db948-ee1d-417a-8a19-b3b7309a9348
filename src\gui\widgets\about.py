#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关于小部件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QTextBrowser, QPushButton,
    QHBoxLayout, QSpacerItem, QSizePolicy
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QFont, QPixmap, QIcon

from ..i18n import Translator

class AboutWidget(QWidget):
    """关于小部件"""
    
    def __init__(self, translator: Translator):
        """
        初始化关于小部件
        
        Args:
            translator: 翻译器实例
        """
        super().__init__()
        
        self.translator = translator
        self.translator.add_observer(self)
        
        # 设置对象名，用于样式表
        self.setObjectName("aboutWidget")
        
        # 初始化UI
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 创建标题标签
        title_label = QLabel("MD Vector Processor")
        title_label.setObjectName("aboutTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        main_layout.addWidget(title_label)
        
        # 创建版本标签
        version_label = QLabel(self.translator.get_text("version", "版本") + ": 1.0.0")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setFont(QFont("Arial", 12))
        main_layout.addWidget(version_label)
        
        # 创建描述文本浏览器
        description_browser = QTextBrowser()
        description_browser.setObjectName("descriptionBrowser")
        description_browser.setOpenExternalLinks(True)
        description_browser.setMaximumHeight(300)
        
        # 设置描述文本
        description_html = f"""
        <div style="text-align: center;">
            <p>{self.translator.get_text("about_description", "MD向量处理器是一个功能强大的向量处理工具，支持多种索引类型和量化方法。")}</p>
            <p>{self.translator.get_text("about_features", "主要功能：")}</p>
            <ul style="text-align: left;">
                <li>{self.translator.get_text("feature_1", "支持多种索引类型（Flat、IVF、HNSW、Hybrid）")}</li>
                <li>{self.translator.get_text("feature_2", "支持多种量化方法（PQ、OPQ、SQ）")}</li>
                <li>{self.translator.get_text("feature_3", "支持多语言文本向量化")}</li>
                <li>{self.translator.get_text("feature_4", "支持向量可视化")}</li>
                <li>{self.translator.get_text("feature_5", "支持GPU加速")}</li>
            </ul>
            <p>{self.translator.get_text("about_license", "本软件基于MIT许可证开源。")}</p>
            <p><a href="https://github.com/example/md-vector-processor">{self.translator.get_text("github_repo", "GitHub仓库")}</a></p>
        </div>
        """
        
        description_browser.setHtml(description_html)
        main_layout.addWidget(description_browser)
        
        # 创建按钮布局
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 添加弹性空间
        buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        # 创建按钮
        self.github_button = QPushButton(self.translator.get_text("github", "GitHub"))
        self.github_button.setObjectName("githubButton")
        self.github_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        self.docs_button = QPushButton(self.translator.get_text("documentation", "文档"))
        self.docs_button.setObjectName("docsButton")
        self.docs_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        self.license_button = QPushButton(self.translator.get_text("license", "许可证"))
        self.license_button.setObjectName("licenseButton")
        self.license_button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 添加按钮到布局
        buttons_layout.addWidget(self.github_button)
        buttons_layout.addWidget(self.docs_button)
        buttons_layout.addWidget(self.license_button)
        
        # 添加弹性空间
        buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        
        main_layout.addLayout(buttons_layout)
        
        # 创建版权标签
        copyright_label = QLabel("© 2025 MD Vector Team. " + self.translator.get_text("all_rights_reserved", "保留所有权利。"))
        copyright_label.setObjectName("copyrightLabel")
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(copyright_label)
        
        # 连接信号
        self._connect_signals()
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.github_button.clicked.connect(self._on_github_clicked)
        self.docs_button.clicked.connect(self._on_docs_clicked)
        self.license_button.clicked.connect(self._on_license_clicked)
    
    def _on_github_clicked(self):
        """GitHub按钮点击处理"""
        # 在浏览器中打开GitHub仓库
        import webbrowser
        webbrowser.open("https://github.com/example/md-vector-processor")
    
    def _on_docs_clicked(self):
        """文档按钮点击处理"""
        # 在浏览器中打开文档
        import webbrowser
        webbrowser.open("https://example.com/md-vector-processor/docs")
    
    def _on_license_clicked(self):
        """许可证按钮点击处理"""
        # 显示许可证文本
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle(self.translator.get_text("license", "许可证"))
        dialog.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(dialog)
        
        license_browser = QTextBrowser()
        license_browser.setPlainText("""MIT License

Copyright (c) 2025 MD Vector Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.""")
        
        layout.addWidget(license_browser)
        
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        button_box.accepted.connect(dialog.accept)
        layout.addWidget(button_box)
        
        dialog.exec()
    
    def on_language_changed(self):
        """语言变更回调"""
        # 更新版本标签
        self.findChild(QLabel, "versionLabel").setText(
            self.translator.get_text("version", "版本") + ": 1.0.0"
        )
        
        # 更新描述文本
        description_browser = self.findChild(QTextBrowser, "descriptionBrowser")
        if description_browser:
            description_html = f"""
            <div style="text-align: center;">
                <p>{self.translator.get_text("about_description", "MD向量处理器是一个功能强大的向量处理工具，支持多种索引类型和量化方法。")}</p>
                <p>{self.translator.get_text("about_features", "主要功能：")}</p>
                <ul style="text-align: left;">
                    <li>{self.translator.get_text("feature_1", "支持多种索引类型（Flat、IVF、HNSW、Hybrid）")}</li>
                    <li>{self.translator.get_text("feature_2", "支持多种量化方法（PQ、OPQ、SQ）")}</li>
                    <li>{self.translator.get_text("feature_3", "支持多语言文本向量化")}</li>
                    <li>{self.translator.get_text("feature_4", "支持向量可视化")}</li>
                    <li>{self.translator.get_text("feature_5", "支持GPU加速")}</li>
                </ul>
                <p>{self.translator.get_text("about_license", "本软件基于MIT许可证开源。")}</p>
                <p><a href="https://github.com/example/md-vector-processor">{self.translator.get_text("github_repo", "GitHub仓库")}</a></p>
            </div>
            """
            
            description_browser.setHtml(description_html)
        
        # 更新按钮
        self.github_button.setText(self.translator.get_text("github", "GitHub"))
        self.docs_button.setText(self.translator.get_text("documentation", "文档"))
        self.license_button.setText(self.translator.get_text("license", "许可证"))
        
        # 更新版权标签
        self.findChild(QLabel, "copyrightLabel").setText(
            "© 2025 MD Vector Team. " + self.translator.get_text("all_rights_reserved", "保留所有权利。")
        )
