
# Prometheus配置文件

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "rules/*.yml"

scrape_configs:
  # 应用API监控
  - job_name: 'md_vector_processor'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['app:8000']
    scrape_interval: 10s
    scrape_timeout: 5s

  # Node Exporter监控
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

# 告警规则
alerting_rules:
  groups:
    - name: md_vector_processor_alerts
      rules:
        # API服务可用性
        - alert: APIServiceDown
          expr: up{job="md_vector_processor"} == 0
          for: 1m
          labels:
            severity: critical
          annotations:
            summary: "API服务不可用"
            description: "实例 {{ $labels.instance }} 已停止运行"

        # 高响应时间
        - alert: HighResponseTime
          expr: http_request_duration_seconds{quantile="0.9"} > 1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "API响应时间过高"
            description: "90%的请求响应时间超过1秒"

        # 内存使用率
        - alert: HighMemoryUsage
          expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "内存使用率过高"
            description: "内存使用率超过90%"

        # CPU使用率
        - alert: HighCPUUsage
          expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "CPU使用率过高"
            description: "CPU使用率超过90%"

        # 磁盘空间
        - alert: LowDiskSpace
          expr: node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"} * 100 < 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "磁盘空间不足"
            description: "剩余磁盘空间少于10%"

# 存储配置
storage:
  tsdb:
    path: /prometheus
    retention:
      time: 15d
      size: 10GB

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9090/api/v1/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9090/api/v1/read"