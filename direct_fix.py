#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接修复特定文件
"""

import json
from pathlib import Path

def direct_fix():
    """直接修复特定文件"""
    
    # 具体的修复映射
    fixes = {
        'XS71 14A121 AA EN 1998 09 绾挎潫鎬绘垚鍒堕€犳爣鍑?': 'XS71 14A121 AA EN 1998 09 线束总成制造标准',
        'A 009 000 04 99 CH 2013 07 濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': 'A 009 000 04 99 CH 2013 07 奔驰线束焊接测试标准',
        '绾挎潫': '线束',
        '鐢ㄦ埛鎻愪緵': '用户提供',
        '绂忕壒': '福特',
        '濂旈┌': '奔驰',
        '鍒堕€?': '制造',
        '鎬绘垚': '总成',
        '鐒婃帴': '焊接',
        '娴嬭瘯': '测试',
        '鏍囧噯': '标准',
        '绾挎潫鎬绘垚鍒堕€犳爣鍑?': '线束总成制造标准',
        '濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '奔驰线束焊接测试标准'
    }
    
    # 要修复的文件列表
    files_to_fix = [
        "training_data/raw_documents/enterprise_standards/metadata/ES_XS71-14A121-AA_EN_1998-09_线束总成制造标准_metadata.json",
        "training_data/raw_documents/enterprise_standards/metadata/A 009 000 04 99_CH_2013-07_奔驰线束焊接测试标准_metadata.json"
    ]
    
    for file_path_str in files_to_fix:
        file_path = Path(file_path_str)
        
        if not file_path.exists():
            print(f"文件不存在: {file_path}")
            continue
        
        print(f"\n处理文件: {file_path.name}")
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            print(f"原始内容长度: {len(content)}")
            
            # 直接在文本内容上进行替换
            fixed_content = content
            for wrong, correct in fixes.items():
                if wrong in fixed_content:
                    print(f"发现并替换: {wrong} -> {correct}")
                    fixed_content = fixed_content.replace(wrong, correct)
            
            # 保存修复后的内容
            if fixed_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                print("✅ 修复完成并保存")
                
                # 验证修复结果
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        new_content = f.read()
                    metadata = json.loads(new_content)
                    print(f"修复后标题: {metadata.get('title', 'N/A')}")
                    print(f"修复后来源: {metadata.get('source', 'N/A')}")
                except Exception as e:
                    print(f"验证失败: {e}")
            else:
                print("⏭️ 无需修复")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    print("🚀 开始直接修复")
    print("=" * 80)
    
    direct_fix()
    
    print("\n🎯 直接修复完成")
    print("=" * 80)
