#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 PyQt5 安装情况
"""

import sys
import traceback

def test_pyqt5():
    """测试 PyQt5 安装情况"""
    try:
        print("尝试导入 PyQt5...")
        import PyQt5
        print(f"PyQt5 版本: {PyQt5.__version__}")
        
        print("\n尝试导入 PyQt5.QtWidgets...")
        from PyQt5 import QtWidgets
        print("成功导入 PyQt5.QtWidgets")
        
        print("\n尝试导入 PyQt5.QtCore...")
        from PyQt5 import QtCore
        print("成功导入 PyQt5.QtCore")
        
        print("\n尝试导入 PyQt5.QtGui...")
        from PyQt5 import QtGui
        print("成功导入 PyQt5.QtGui")
        
        print("\n尝试创建 QApplication...")
        app = QtWidgets.QApplication(sys.argv)
        print("成功创建 QApplication")
        
        print("\n尝试创建 QMainWindow...")
        window = QtWidgets.QMainWindow()
        print("成功创建 QMainWindow")
        
        print("\n尝试创建 QMenuBar...")
        menu_bar = window.menuBar()
        print("成功创建 QMenuBar")
        
        print("\n尝试创建 QMenu...")
        menu = menu_bar.addMenu("测试菜单")
        print("成功创建 QMenu")
        
        print("\n尝试创建 QAction...")
        action = QtGui.QAction("测试动作", window)
        print("成功创建 QAction")
        
        print("\n尝试添加 QAction 到 QMenu...")
        menu.addAction(action)
        print("成功添加 QAction 到 QMenu")
        
        print("\nPyQt5 测试成功!")
        return True
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装 PyQt5:")
        print("pip install PyQt5")
        return False
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pyqt5()
