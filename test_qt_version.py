#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 PyQt 版本
"""

import sys
import traceback

def test_qt_version():
    """测试 PyQt 版本"""
    try:
        # 尝试导入 PyQt6
        try:
            print("尝试导入 PyQt6...")
            import PyQt6
            print(f"找到 PyQt6，版本: {PyQt6.__version__ if hasattr(PyQt6, '__version__') else '未知'}")
            
            # 尝试导入 PyQt6.QtWidgets
            print("尝试导入 PyQt6.QtWidgets...")
            from PyQt6 import QtWidgets
            print("成功导入 PyQt6.QtWidgets")
            
            # 尝试导入 PyQt6.QtCore
            print("尝试导入 PyQt6.QtCore...")
            from PyQt6 import QtCore
            print("成功导入 PyQt6.QtCore")
            
            # 尝试导入 PyQt6.QtGui
            print("尝试导入 PyQt6.QtGui...")
            from PyQt6 import QtGui
            print("成功导入 PyQt6.QtGui")
            
            # 尝试创建 QApplication
            print("尝试创建 QApplication...")
            app = QtWidgets.QApplication(sys.argv)
            print("成功创建 QApplication")
            
            # 尝试创建 QMainWindow
            print("尝试创建 QMainWindow...")
            window = QtWidgets.QMainWindow()
            print("成功创建 QMainWindow")
            
            # 尝试创建 QMenuBar
            print("尝试创建 QMenuBar...")
            menu_bar = window.menuBar()
            print("成功创建 QMenuBar")
            
            # 尝试创建 QMenu
            print("尝试创建 QMenu...")
            menu = menu_bar.addMenu("测试菜单")
            print("成功创建 QMenu")
            
            # 尝试创建 QAction
            print("尝试创建 QAction...")
            action = QtGui.QAction("测试动作", window)
            print("成功创建 QAction")
            
            # 尝试添加 QAction 到 QMenu
            print("尝试添加 QAction 到 QMenu...")
            menu.addAction(action)
            print("成功添加 QAction 到 QMenu")
            
            # 尝试创建 QLabel
            print("尝试创建 QLabel...")
            label = QtWidgets.QLabel("测试标签")
            print("成功创建 QLabel")
            
            # 尝试设置 QLabel 的对齐方式
            print("尝试设置 QLabel 的对齐方式...")
            label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
            print("成功设置 QLabel 的对齐方式")
            
            # 尝试创建 QVBoxLayout
            print("尝试创建 QVBoxLayout...")
            layout = QtWidgets.QVBoxLayout()
            print("成功创建 QVBoxLayout")
            
            # 尝试添加 QLabel 到 QVBoxLayout
            print("尝试添加 QLabel 到 QVBoxLayout...")
            layout.addWidget(label)
            print("成功添加 QLabel 到 QVBoxLayout")
            
            # 尝试创建 QWidget
            print("尝试创建 QWidget...")
            widget = QtWidgets.QWidget()
            print("成功创建 QWidget")
            
            # 尝试设置 QWidget 的布局
            print("尝试设置 QWidget 的布局...")
            widget.setLayout(layout)
            print("成功设置 QWidget 的布局")
            
            # 尝试设置 QMainWindow 的中央部件
            print("尝试设置 QMainWindow 的中央部件...")
            window.setCentralWidget(widget)
            print("成功设置 QMainWindow 的中央部件")
            
            print("\nPyQt6 测试成功!")
            return True
        
        except ImportError:
            print("未找到 PyQt6，尝试导入 PyQt5...")
            import PyQt5
            print(f"找到 PyQt5，版本: {PyQt5.__version__ if hasattr(PyQt5, '__version__') else '未知'}")
            
            # 尝试导入 PyQt5.QtWidgets
            print("尝试导入 PyQt5.QtWidgets...")
            from PyQt5 import QtWidgets
            print("成功导入 PyQt5.QtWidgets")
            
            # 尝试导入 PyQt5.QtCore
            print("尝试导入 PyQt5.QtCore...")
            from PyQt5 import QtCore
            print("成功导入 PyQt5.QtCore")
            
            # 尝试导入 PyQt5.QtGui
            print("尝试导入 PyQt5.QtGui...")
            from PyQt5 import QtGui
            print("成功导入 PyQt5.QtGui")
            
            # 尝试创建 QApplication
            print("尝试创建 QApplication...")
            app = QtWidgets.QApplication(sys.argv)
            print("成功创建 QApplication")
            
            # 尝试创建 QMainWindow
            print("尝试创建 QMainWindow...")
            window = QtWidgets.QMainWindow()
            print("成功创建 QMainWindow")
            
            # 尝试创建 QMenuBar
            print("尝试创建 QMenuBar...")
            menu_bar = window.menuBar()
            print("成功创建 QMenuBar")
            
            # 尝试创建 QMenu
            print("尝试创建 QMenu...")
            menu = menu_bar.addMenu("测试菜单")
            print("成功创建 QMenu")
            
            # 尝试创建 QAction
            print("尝试创建 QAction...")
            action = QtGui.QAction("测试动作", window)
            print("成功创建 QAction")
            
            # 尝试添加 QAction 到 QMenu
            print("尝试添加 QAction 到 QMenu...")
            menu.addAction(action)
            print("成功添加 QAction 到 QMenu")
            
            # 尝试创建 QLabel
            print("尝试创建 QLabel...")
            label = QtWidgets.QLabel("测试标签")
            print("成功创建 QLabel")
            
            # 尝试设置 QLabel 的对齐方式
            print("尝试设置 QLabel 的对齐方式...")
            label.setAlignment(QtCore.Qt.AlignCenter)
            print("成功设置 QLabel 的对齐方式")
            
            # 尝试创建 QVBoxLayout
            print("尝试创建 QVBoxLayout...")
            layout = QtWidgets.QVBoxLayout()
            print("成功创建 QVBoxLayout")
            
            # 尝试添加 QLabel 到 QVBoxLayout
            print("尝试添加 QLabel 到 QVBoxLayout...")
            layout.addWidget(label)
            print("成功添加 QLabel 到 QVBoxLayout")
            
            # 尝试创建 QWidget
            print("尝试创建 QWidget...")
            widget = QtWidgets.QWidget()
            print("成功创建 QWidget")
            
            # 尝试设置 QWidget 的布局
            print("尝试设置 QWidget 的布局...")
            widget.setLayout(layout)
            print("成功设置 QWidget 的布局")
            
            # 尝试设置 QMainWindow 的中央部件
            print("尝试设置 QMainWindow 的中央部件...")
            window.setCentralWidget(widget)
            print("成功设置 QMainWindow 的中央部件")
            
            print("\nPyQt5 测试成功!")
            return True
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装 PyQt5 或 PyQt6:")
        print("pip install PyQt5")
        print("或")
        print("pip install PyQt6")
        return False
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_qt_version()
