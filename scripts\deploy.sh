
#!/bin/bash

# 生产环境部署脚本

# 确保脚本在错误时退出
set -e

# 配置变量
DEPLOY_ENV=${1:-production}
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
APP_NAME="md_vector_processor"
DOCKER_REGISTRY="your-registry.com"  # 替换为实际的Docker仓库地址

# 显示部署信息
echo "=== 部署 MD Vector Processor ==="
echo "环境: $DEPLOY_ENV"
echo "时间: $(date)"
echo

# 检查必要条件
check_prerequisites() {
    echo "检查部署条件..."
    command -v docker >/dev/null 2>&1 || { echo "需要安装 Docker"; exit 1; }
    command -v docker-compose >/dev/null 2>&1 || { echo "需要安装 Docker Compose"; exit 1; }
}

# 创建备份
create_backup() {
    echo "创建数据备份..."
    mkdir -p "$BACKUP_DIR"
    
    # 备份数据目录
    if [ -d "data" ]; then
        tar czf "$BACKUP_DIR/data.tar.gz" data/
    fi
    
    # 备份配置文件
    if [ -d "config" ]; then
        tar czf "$BACKUP_DIR/config.tar.gz" config/
    fi
    
    # 备份数据库（如果使用）
    if docker-compose exec -T postgres pg_dump -U mdvector mdvector > "$BACKUP_DIR/database.sql" 2>/dev/null; then
        echo "数据库备份完成"
    else
        echo "警告: 数据库备份失败"
    fi
}

# 更新应用
update_application() {
    echo "更新应用..."
    
    # 拉取最新代码
    git fetch --all
    git checkout "$DEPLOY_ENV"
    git pull origin "$DEPLOY_ENV"
    
    # 更新环境配置
    if [ -f ".env.$DEPLOY_ENV" ]; then
        cp ".env.$DEPLOY_ENV" .env
    fi
    
    # 构建新镜像
    docker-compose build --no-cache
    
    # 推送到镜像仓库（如果配置了）
    if [ -n "$DOCKER_REGISTRY" ]; then
        docker tag "$APP_NAME:latest" "$DOCKER_REGISTRY/$APP_NAME:latest"
        docker tag "$APP_NAME:latest" "$DOCKER_REGISTRY/$APP_NAME:$(date +%Y%m%d_%H%M%S)"
        docker push "$DOCKER_REGISTRY/$APP_NAME:latest"
        docker push "$DOCKER_REGISTRY/$APP_NAME:$(date +%Y%m%d_%H%M%S)"
    fi
}

# 部署应用
deploy_application() {
    echo "部署应用..."
    
    # 停止当前服务
    docker-compose down
    
    # 启动新服务
    docker-compose up -d
    
    # 等待服务启动
    echo "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        echo "服务已成功启动"
    else
        echo "错误: 服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 执行数据库迁移（如果需要）
run_migrations() {
    echo "执行数据库迁移..."
    docker-compose exec -T app python -m alembic upgrade head
}

# 验证部署
verify_deployment() {
    echo "验证部署..."
    
    # 检查API健康状态
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "API服务正常"
    else
        echo "警告: API服务可能有问题"
    fi
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready >/dev/null 2>&1; then
        echo "数据库连接正常"
    else
        echo "警告: 数据库连接可能有问题"
    fi
    
    # 检查Redis连接
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        echo "Redis连接正常"
    else
        echo "警告: Redis连接可能有问题"
    fi
}

# 清理旧数据
cleanup() {
    echo "清理旧数据..."
    
    # 删除旧的备份
    find backups/ -type d -mtime +30 -exec rm -rf {} \;
    
    # 清理Docker资源
    docker system prune -f
}

# 发送通知
send_notification() {
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"MD Vector Processor 已部署到 $DEPLOY_ENV 环境\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
}

# 主部署流程
main() {
    echo "开始部署流程..."
    
    # 检查条件
    check_prerequisites
    
    # 创建备份
    create_backup
    
    # 更新应用
    update_application
    
    # 部署
    deploy_application
    
    # 运行迁移
    run_migrations
    
    # 验证部署
    verify_deployment
    
    # 清理
    cleanup
    
    # 发送通知
    send_notification
    
    echo
    echo "=== 部署完成 ==="
    echo "部署时间: $(date)"
    echo "环境: $DEPLOY_ENV"
    echo "备份位置: $BACKUP_DIR"
    echo
    echo "服务访问地址:"
    echo "- API: http://localhost:8000"
    echo "- 监控: http://localhost:9090"
    echo "- 管理面板: http://localhost:8080"
    echo
}

# 执行部署
main
