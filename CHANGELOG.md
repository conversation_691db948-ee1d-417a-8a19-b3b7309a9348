
# 更新日志

本项目遵循[语义化版本](https://semver.org/lang/zh-CN/)规范。

## [1.0.0] - 2024-01-15

### 新增
- 初始版本发布
- 核心功能实现：
  - Markdown文件批量处理
  - 文本清理和标准化
  - 向量化处理
  - 索引构建和搜索
  - 数据存储管理
- 基础组件：
  - 文件扫描器
  - Markdown加载器
  - 文本预处理器
  - 向量化模块
  - 索引管理器
  - 存储管理器
- 工具类：
  - 日志管理
  - 配置管理
  - 性能监控
  - 辅助功能
- 完整的文档：
  - 安装指南
  - 使用说明
  - API文档
  - 示例代码
- 测试套件：
  - 单元测试
  - 集成测试
  - 端到端测试

### 特性
- 支持多种向量化模型
- 高效的批处理机制
- 灵活的配置系统
- 完善的错误处理
- 详细的日志记录
- 性能优化选项

### 技术栈
- Python 3.8+支持
- CUDA支持（可选）
- 主要依赖：
  - sentence-transformers
  - faiss-cpu/gpu
  - numpy
  - torch
  - tqdm

## [0.9.0] - 2024-01-01 [预发布]

### 新增
- 基础框架搭建
- 核心功能开发
- 初步测试和优化

### 修复
- 内存使用优化
- 性能问题解决
- 错误处理完善

### 变更
- 配置系统重构
- API接口优化
- 文档更新

## [0.1.0] - 2023-12-01 [内部测试版]

### 新增
- 项目初始化
- 基本功能实现
- 测试环境搭建

## 变更类型

- `新增` 新功能
- `修复` Bug修复
- `变更` 对现有功能的变更
- `弃用` 即将被移除的功能
- `移除` 已经移除的功能
- `安全` 安全性相关的改进
- `性能` 性能相关的改进
- `文档` 文档更新
- `维护` 代码维护相关的变更
- `依赖` 依赖项更新

## 提交说明格式

每个提交消息应该包含以下部分：
1. 类型：变更的类型（见上面的变更类型）
2. 范围：变更影响的范围（可选）
3. 描述：对变更的简短描述
4. 详情：详细的变更说明（可选）
5. BREAKING CHANGE：不向后兼容的变更说明（如果有）

示例：
```
feat(vectorizer): 添加新的向量化模型支持

- 集成BERT模型
- 添加模型配置选项
- 优化批处理性能

BREAKING CHANGE: 更改了向量化接口的参数结构
```

## 版本号说明

版本格式：主版本号.次版本号.修订号，版本号递增规则如下：
1. 主版本号：当你做了不兼容的 API 修改
2. 次版本号：当你做了向下兼容的功能性新增
3. 修订号：当你做了向下兼容的问题修正

## 如何贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

请确保你的提交信息遵循上述格式规范，并在CHANGELOG.md中记录相应的变更。

[1.0.0]: https://github.com/yourusername/md_vector_processor/releases/tag/v1.0.0
[0.9.0]: https://github.com/yourusername/md_vector_processor/releases/tag/v0.9.0
[0.1.0]: https://github.com/yourusername/md_vector_processor/releases/tag/v0.1.0