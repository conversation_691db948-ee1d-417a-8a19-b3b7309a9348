
def enhanced_quality_check(self, file_path: Path, content: str, metadata: Dict) -> bool:
    """增强的文档质量检查"""
    
    # 1. 基本文件检查
    if not file_path.exists() or file_path.stat().st_size == 0:
        return False
    
    # 2. 内容长度检查 (降低要求)
    if len(content.strip()) < 100:  # 从1000降低到100
        return False
    
    # 3. 汽车相关性检查 (大幅降低阈值)
    automotive_relevance = self.calculate_enhanced_automotive_relevance(
        str(file_path), metadata.get('title', ''), content
    )
    
    # 降低阈值从0.5到0.1
    if automotive_relevance < 0.1:
        logger.warning(f"文档相关性较低但仍可处理: {file_path} (相关性: {automotive_relevance:.3f})")
        # 不直接返回False，而是继续处理
    
    # 4. 特殊文档类型豁免
    filename_lower = file_path.name.lower()
    
    # 标准文档豁免
    if any(prefix in filename_lower for prefix in ['sae_', 'uscar', 'iso_', 'din_', 'vw_', 'gm_']):
        logger.info(f"标准文档豁免质量检查: {file_path}")
        return True
    
    # 企业标准豁免
    if 'enterprise_standards' in str(file_path).lower():
        logger.info(f"企业标准豁免质量检查: {file_path}")
        return True
    
    # 5. 最终检查 (非常宽松)
    return automotive_relevance >= 0.05  # 极低阈值
