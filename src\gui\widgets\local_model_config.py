#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
本地模型配置小部件

提供本地模型的配置和管理界面
"""

from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QFormLayout, QLineEdit, QComboBox, QSpinBox, QCheckBox,
    QTextEdit, QMessageBox, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from ..i18n import Translator
from ...utils.local_model_manager import get_local_model_manager, LocalModel


class ModelTestWorker(QThread):
    """模型测试工作线程"""

    test_completed = pyqtSignal(bool, str)

    def __init__(self, model):
        super().__init__()
        self.model = model
        self.model_manager = get_local_model_manager()

    def run(self):
        try:
            success, message = self.model_manager.test_model_connection(self.model)
            self.test_completed.emit(success, message)
        except Exception as e:
            self.test_completed.emit(False, f"测试过程中出错: {str(e)}")


class ModelConfigDialog(QDialog):
    """模型配置对话框"""

    def __init__(self, translator: Translator, model: LocalModel = None, parent=None):
        super().__init__(parent)
        self.translator = translator
        self.model = model
        self.is_edit_mode = model is not None

        self.setWindowTitle("编辑模型" if self.is_edit_mode else "添加模型")
        self.setModal(True)
        self.resize(500, 400)

        self._init_ui()

        if self.is_edit_mode:
            self._load_model_data()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("模型名称")
        basic_layout.addRow("名称:", self.name_edit)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["ollama", "huggingface", "openai_compatible"])
        basic_layout.addRow("类型:", self.type_combo)

        self.endpoint_edit = QLineEdit()
        self.endpoint_edit.setPlaceholderText("http://localhost:11434")
        basic_layout.addRow("端点:", self.endpoint_edit)

        self.model_id_edit = QLineEdit()
        self.model_id_edit.setPlaceholderText("llama2")
        basic_layout.addRow("模型ID:", self.model_id_edit)

        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        self.description_edit.setPlaceholderText("模型描述")
        basic_layout.addRow("描述:", self.description_edit)

        layout.addWidget(basic_group)

        # 功能配置组
        features_group = QGroupBox("功能配置")
        features_layout = QFormLayout(features_group)

        self.vector_dimension_spin = QSpinBox()
        self.vector_dimension_spin.setRange(1, 4096)
        self.vector_dimension_spin.setValue(384)
        features_layout.addRow("向量维度:", self.vector_dimension_spin)

        self.supports_embedding_check = QCheckBox()
        self.supports_embedding_check.setChecked(True)
        features_layout.addRow("支持向量化:", self.supports_embedding_check)

        self.supports_chat_check = QCheckBox()
        self.supports_chat_check.setChecked(True)
        features_layout.addRow("支持对话:", self.supports_chat_check)

        layout.addWidget(features_group)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)

    def _load_model_data(self):
        """加载模型数据到表单"""
        if not self.model:
            return

        self.name_edit.setText(self.model.name)
        self.type_combo.setCurrentText(self.model.type)
        self.endpoint_edit.setText(self.model.endpoint)
        self.model_id_edit.setText(self.model.model_id)
        self.description_edit.setPlainText(self.model.description)
        self.vector_dimension_spin.setValue(self.model.vector_dimension)
        self.supports_embedding_check.setChecked(self.model.supports_embedding)
        self.supports_chat_check.setChecked(self.model.supports_chat)

    def get_model_data(self) -> LocalModel:
        """从表单获取模型数据"""
        return LocalModel(
            name=self.name_edit.text().strip(),
            type=self.type_combo.currentText(),
            endpoint=self.endpoint_edit.text().strip(),
            model_id=self.model_id_edit.text().strip(),
            description=self.description_edit.toPlainText().strip(),
            vector_dimension=self.vector_dimension_spin.value(),
            supports_embedding=self.supports_embedding_check.isChecked(),
            supports_chat=self.supports_chat_check.isChecked()
        )


class LocalModelConfigWidget(QWidget):
    """本地模型配置小部件"""

    def __init__(self, translator: Translator):
        super().__init__()
        self.translator = translator
        self.model_manager = get_local_model_manager()
        self.test_worker = None

        self._init_ui()
        self._refresh_models()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("本地模型配置")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self._refresh_models)
        toolbar_layout.addWidget(self.refresh_button)

        self.add_button = QPushButton("添加模型")
        self.add_button.clicked.connect(self._add_model)
        toolbar_layout.addWidget(self.add_button)

        self.edit_button = QPushButton("编辑")
        self.edit_button.clicked.connect(self._edit_model)
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)

        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self._test_model)
        self.test_button.setEnabled(False)
        toolbar_layout.addWidget(self.test_button)

        self.remove_button = QPushButton("删除")
        self.remove_button.clicked.connect(self._remove_model)
        self.remove_button.setEnabled(False)
        toolbar_layout.addWidget(self.remove_button)

        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)

        # 模型表格
        self.models_table = QTableWidget()
        self.models_table.setColumnCount(6)
        self.models_table.setHorizontalHeaderLabels([
            "名称", "类型", "模型ID", "状态", "向量化", "对话"
        ])

        # 设置表格属性
        header = self.models_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        self.models_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.models_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.models_table.itemSelectionChanged.connect(self._on_selection_changed)

        layout.addWidget(self.models_table)

        # 状态栏
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)

    def _refresh_models(self):
        """刷新模型列表"""
        self.status_label.setText("正在刷新模型列表...")

        # 刷新模型管理器
        self.model_manager.refresh_models()

        # 更新表格
        models = list(self.model_manager.models.values())
        self.models_table.setRowCount(len(models))

        for row, model in enumerate(models):
            # 名称
            self.models_table.setItem(row, 0, QTableWidgetItem(model.name))

            # 类型
            self.models_table.setItem(row, 1, QTableWidgetItem(model.type))

            # 模型ID
            self.models_table.setItem(row, 2, QTableWidgetItem(model.model_id))

            # 状态
            status = "可用" if model.is_available else "不可用"
            status_item = QTableWidgetItem(status)
            if model.is_available:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.models_table.setItem(row, 3, status_item)

            # 向量化支持
            embedding_item = QTableWidgetItem("✓" if model.supports_embedding else "✗")
            self.models_table.setItem(row, 4, embedding_item)

            # 对话支持
            chat_item = QTableWidgetItem("✓" if model.supports_chat else "✗")
            self.models_table.setItem(row, 5, chat_item)

        self.status_label.setText(f"找到 {len(models)} 个模型")

    def _on_selection_changed(self):
        """选择变更处理"""
        has_selection = len(self.models_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.test_button.setEnabled(has_selection)
        self.remove_button.setEnabled(has_selection)

    def _get_selected_model(self) -> Optional[LocalModel]:
        """获取选中的模型"""
        current_row = self.models_table.currentRow()
        if current_row < 0:
            return None

        model_name = self.models_table.item(current_row, 0).text()
        return self.model_manager.get_model(model_name)

    def _add_model(self):
        """添加模型"""
        dialog = ModelConfigDialog(self.translator, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            model = dialog.get_model_data()
            if model.name:
                self.model_manager.add_model(model)
                self._refresh_models()
                self.status_label.setText(f"已添加模型: {model.name}")

    def _edit_model(self):
        """编辑模型"""
        model = self._get_selected_model()
        if not model:
            return

        dialog = ModelConfigDialog(self.translator, model, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_model = dialog.get_model_data()
            # 移除旧模型，添加新模型
            self.model_manager.remove_model(model.name)
            self.model_manager.add_model(updated_model)
            self._refresh_models()
            self.status_label.setText(f"已更新模型: {updated_model.name}")

    def _test_model(self):
        """测试模型连接"""
        model = self._get_selected_model()
        if not model:
            return

        self.status_label.setText(f"正在测试模型: {model.name}...")
        self.test_button.setEnabled(False)

        # 启动测试线程
        self.test_worker = ModelTestWorker(model)
        self.test_worker.test_completed.connect(self._on_test_completed)
        self.test_worker.start()

    def _on_test_completed(self, success: bool, message: str):
        """测试完成处理"""
        self.test_button.setEnabled(True)

        if success:
            QMessageBox.information(self, "测试成功", f"模型连接测试成功！\n\n{message}")
            self.status_label.setText("测试成功")
        else:
            QMessageBox.warning(self, "测试失败", f"模型连接测试失败：\n\n{message}")
            self.status_label.setText("测试失败")

    def _remove_model(self):
        """删除模型"""
        model = self._get_selected_model()
        if not model:
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除模型 '{model.name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.model_manager.remove_model(model.name)
            self._refresh_models()
            self.status_label.setText(f"已删除模型: {model.name}")
