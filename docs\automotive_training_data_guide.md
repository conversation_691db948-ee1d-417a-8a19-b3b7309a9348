# 汽车电器领域训练数据准备指导

## 概述

本指导文档专门针对汽车电器研发设计领域，提供详细的训练数据准备方法，包括国家标准、国际标准、行业规范和企业规范等大文档的处理流程。

## 目标数据量规划

### IVF索引训练数据
- **最小需求**: 1,000个向量 (约50-100个标准文档)
- **推荐规模**: 10,000+个向量 (约500-1000个文档)
- **预期训练时间**: 1-5分钟

### PQ量化索引训练数据  
- **最小需求**: 10,000个向量 (约500-1000个文档)
- **推荐规模**: 100,000+个向量 (约5000-10000个文档)
- **预期训练时间**: 5-30分钟

## 汽车电器领域文档分类

### 1. 国家标准 (GB标准)
```
优先级: 高
数量目标: 200-300个文档
示例文档:
- GB/T 18384.1-2015 电动汽车安全要求 第1部分：车载可充电储能系统
- GB/T 18384.2-2015 电动汽车安全要求 第2部分：操作安全和故障防护
- GB/T 18384.3-2015 电动汽车安全要求 第3部分：人员触电防护
- GB 11567-2017 汽车及挂车侧面和后面反射器
- GB 4785-2019 汽车及挂车外部照明和光信号装置的安装规定
- GB 15742-2019 机动车用喇叭的性能要求及试验方法
- GB/T 19596-2017 电动汽车术语
- GB/T 28046.1-2019 道路车辆 电气及电子设备的环境条件和试验
```

### 2. 国际标准 (ISO/IEC/SAE标准)
```
优先级: 高
数量目标: 300-400个文档
示例文档:
- ISO 26262-1:2018 道路车辆功能安全 第1部分：词汇
- ISO 26262-3:2018 道路车辆功能安全 第3部分：概念阶段
- ISO 26262-4:2018 道路车辆功能安全 第4部分：产品开发系统级
- ISO 11898-1:2015 道路车辆 控制器局域网(CAN)
- ISO 14229-1:2020 道路车辆 统一诊断服务(UDS)
- IEC 61851-1:2017 电动车辆传导充电系统
- SAE J1772:2017 电动车辆和插电式混合动力车辆传导充电耦合器
- SAE J2847-1:2019 通信车辆到电网的通信
```

### 3. 行业规范
```
优先级: 中
数量目标: 150-200个文档
示例文档:
- QC/T 413-2002 汽车电气设备基本技术条件
- QC/T 417.1-2001 车用电线束插接器 第1部分：定义和试验方法
- QC/T 29106-2014 汽车用继电器
- JT/T 794-2011 道路运输车辆卫星定位系统终端技术要求
- 中国汽车工程学会标准 T/CSAE 53-2017 电动汽车无线充电系统
```

### 4. 企业规范
```
优先级: 中
数量目标: 100-150个文档
示例文档:
- 大众汽车集团标准 VW 80000
- 通用汽车全球标准 GMW3172
- 福特汽车标准 WSS-M2G234-A
- 丰田技术标准 TSC1234G
- 比亚迪企业标准 Q/BYD 001-2020
```

### 5. 技术文档和设计指南
```
优先级: 中
数量目标: 200-300个文档
示例文档:
- 汽车电子控制单元(ECU)设计指南
- 车载网络架构设计规范
- 汽车线束设计手册
- 电动汽车充电系统设计指南
- 汽车电磁兼容性(EMC)设计规范
- 车用传感器选型指南
- 汽车照明系统设计标准
```

## 文档收集策略

### 1. 官方渠道
```bash
# 国家标准
- 国家标准化管理委员会 (SAC): http://www.sac.gov.cn/
- 全国标准信息公共服务平台: http://std.samr.gov.cn/
- 中国标准在线服务网: http://www.spc.org.cn/

# 国际标准
- ISO官网: https://www.iso.org/
- IEC官网: https://www.iec.ch/
- SAE International: https://www.sae.org/

# 行业标准
- 中国汽车技术研究中心: http://www.catarc.ac.cn/
- 中国汽车工程学会: http://www.sae-china.org/
```

### 2. 学术资源
```bash
# 学术数据库
- 中国知网 (CNKI): https://www.cnki.net/
- 万方数据: http://www.wanfangdata.com.cn/
- IEEE Xplore: https://ieeexplore.ieee.org/

# 专业期刊
- 《汽车工程》
- 《汽车技术》  
- 《电子技术应用》
- 《汽车电器》
```

### 3. 企业资源
```bash
# 汽车制造商技术文档
- 技术规范书
- 设计指导文件
- 测试标准
- 质量手册

# 供应商技术资料
- 产品规格书
- 应用指南
- 技术白皮书
- 认证文件
```

## 文档预处理流程

### 1. 文档格式标准化
```python
# 支持的文档格式
SUPPORTED_FORMATS = {
    '.pdf': 'PDF文档 (主要格式)',
    '.docx': 'Word文档',
    '.doc': 'Word文档 (旧版)',
    '.txt': '纯文本',
    '.md': 'Markdown',
    '.html': 'HTML文档',
    '.xml': 'XML文档'
}

# 文档命名规范
NAMING_CONVENTION = {
    'national_standard': 'GB_[标准号]_[年份]_[标题].pdf',
    'international_standard': 'ISO_[标准号]_[年份]_[标题].pdf',
    'industry_standard': 'QC_[标准号]_[年份]_[标题].pdf',
    'enterprise_standard': '[企业简称]_[标准号]_[年份]_[标题].pdf',
    'technical_doc': 'TECH_[分类]_[序号]_[标题].pdf'
}
```

### 2. 文档质量检查
```python
# 质量检查标准
QUALITY_CRITERIA = {
    'min_length': 1000,      # 最小字符数
    'max_length': 1000000,   # 最大字符数
    'min_pages': 5,          # 最小页数
    'encoding': 'utf-8',     # 编码要求
    'language': 'zh-cn',     # 主要语言
    'completeness': 0.95     # 完整性要求
}

# 内容验证规则
CONTENT_VALIDATION = {
    'has_title': True,       # 必须有标题
    'has_abstract': True,    # 必须有摘要/概述
    'has_sections': True,    # 必须有章节结构
    'has_technical_terms': True,  # 必须包含技术术语
    'no_corruption': True    # 不能有乱码
}
```

### 3. 元数据提取
```python
# 元数据结构
METADATA_SCHEMA = {
    'document_id': 'str',           # 文档唯一标识
    'title': 'str',                 # 文档标题
    'standard_number': 'str',       # 标准号
    'publication_year': 'int',      # 发布年份
    'category': 'str',              # 文档类别
    'subcategory': 'str',           # 子类别
    'language': 'str',              # 语言
    'page_count': 'int',            # 页数
    'word_count': 'int',            # 字数
    'technical_domain': 'list',     # 技术领域
    'keywords': 'list',             # 关键词
    'abstract': 'str',              # 摘要
    'source': 'str',                # 来源
    'file_path': 'str',             # 文件路径
    'file_size': 'int',             # 文件大小
    'checksum': 'str',              # 文件校验和
    'processing_date': 'datetime'   # 处理日期
}
```

## 文本分块策略

### 1. 智能分块算法
```python
# 汽车电器领域专用分块策略
CHUNKING_STRATEGIES = {
    'standard_document': {
        'method': 'semantic_section',
        'chunk_size': 800,
        'overlap': 100,
        'split_on': ['章', '节', '条', '款'],
        'preserve_structure': True,
        'min_chunk_size': 200,
        'max_chunk_size': 1200
    },
    'technical_specification': {
        'method': 'technical_paragraph',
        'chunk_size': 600,
        'overlap': 80,
        'split_on': ['技术要求', '试验方法', '检验规则'],
        'preserve_tables': True,
        'preserve_figures': True
    },
    'design_guide': {
        'method': 'topic_based',
        'chunk_size': 1000,
        'overlap': 150,
        'split_on': ['设计原则', '实施方法', '注意事项'],
        'preserve_examples': True
    }
}
```

### 2. 领域特定处理
```python
# 汽车电器术语识别
AUTOMOTIVE_TERMS = {
    'electrical_components': [
        'ECU', '电控单元', '传感器', '执行器', '继电器', '保险丝',
        '线束', '连接器', '开关', '电机', '发电机', '蓄电池'
    ],
    'standards_terms': [
        '技术要求', '试验方法', '检验规则', '标志包装',
        '安全要求', '性能指标', '环境条件', '可靠性'
    ],
    'testing_terms': [
        '型式试验', '出厂试验', '抽样检验', '耐久性试验',
        '环境试验', '电磁兼容', '功能安全', '可靠性试验'
    ]
}

# 表格和图表处理
TABLE_PROCESSING = {
    'extract_tables': True,
    'table_to_text': True,
    'preserve_structure': True,
    'include_captions': True
}
```

## 数据目录结构

```
training_data/
├── raw_documents/                    # 原始文档
│   ├── national_standards/          # 国家标准
│   │   ├── GB_electrical/          # 电气类国标
│   │   ├── GB_safety/              # 安全类国标
│   │   └── GB_testing/             # 测试类国标
│   ├── international_standards/     # 国际标准
│   │   ├── ISO_26262/              # 功能安全
│   │   ├── ISO_CAN/                # CAN总线
│   │   └── IEC_charging/           # 充电标准
│   ├── industry_standards/          # 行业标准
│   │   ├── QC_electrical/          # 电气行标
│   │   └── JT_telematics/          # 车联网行标
│   ├── enterprise_standards/        # 企业标准
│   │   ├── OEM_standards/          # 主机厂标准
│   │   └── supplier_specs/         # 供应商规范
│   └── technical_docs/              # 技术文档
│       ├── design_guides/          # 设计指南
│       ├── application_notes/      # 应用说明
│       └── white_papers/           # 技术白皮书
├── processed_documents/             # 处理后文档
│   ├── cleaned/                    # 清理后文档
│   ├── chunked/                    # 分块后文本
│   └── metadata/                   # 元数据文件
├── training_vectors/               # 训练向量
│   ├── ivf_training/              # IVF训练数据
│   ├── pq_training/               # PQ训练数据
│   └── validation/                # 验证数据
└── models/                        # 训练后模型
    ├── ivf_indices/               # IVF索引
    ├── pq_indices/                # PQ索引
    └── embeddings/                # 嵌入模型
```

## 数据质量保证

### 1. 自动化质量检查
```python
# 质量检查流程
QUALITY_CHECKS = [
    'encoding_validation',      # 编码验证
    'content_completeness',     # 内容完整性
    'language_detection',       # 语言检测
    'technical_relevance',      # 技术相关性
    'duplicate_detection',      # 重复检测
    'format_consistency',       # 格式一致性
    'metadata_completeness'     # 元数据完整性
]

# 质量评分标准
QUALITY_SCORING = {
    'excellent': 0.9,          # 优秀 (直接使用)
    'good': 0.7,               # 良好 (轻微处理)
    'acceptable': 0.5,         # 可接受 (需要处理)
    'poor': 0.3,               # 较差 (需要重新处理)
    'unacceptable': 0.1        # 不可接受 (排除)
}
```

### 2. 人工审核流程
```python
# 审核检查点
REVIEW_CHECKPOINTS = {
    'document_relevance': '文档与汽车电器领域的相关性',
    'technical_accuracy': '技术内容的准确性',
    'standard_compliance': '标准格式的符合性',
    'language_quality': '语言表达的质量',
    'completeness': '内容的完整性'
}

# 审核标准
REVIEW_CRITERIA = {
    'must_have': [
        '包含汽车电器相关技术内容',
        '具有明确的技术规范或要求',
        '语言表达清晰准确',
        '格式规范完整'
    ],
    'nice_to_have': [
        '包含实际应用案例',
        '有详细的技术参数',
        '包含测试验证方法',
        '有图表说明'
    ]
}
```

## 参考案例

### 案例1: GB/T 18384.1-2015 处理示例
```python
# 原始文档信息
document_info = {
    'title': 'GB/T 18384.1-2015 电动汽车安全要求 第1部分：车载可充电储能系统',
    'pages': 45,
    'sections': [
        '1 范围',
        '2 规范性引用文件', 
        '3 术语和定义',
        '4 一般要求',
        '5 电气安全',
        '6 功能安全',
        '7 机械安全',
        '8 化学安全',
        '9 热安全'
    ]
}

# 分块结果
chunks_generated = 28
chunk_examples = [
    {
        'chunk_id': 1,
        'content': '本部分规定了电动汽车车载可充电储能系统的安全要求...',
        'section': '1 范围',
        'length': 245,
        'technical_terms': ['电动汽车', '储能系统', '安全要求']
    },
    {
        'chunk_id': 15,
        'content': '储能系统应具备过充电保护功能，当单体电池电压超过...',
        'section': '5 电气安全',
        'length': 387,
        'technical_terms': ['过充电保护', '单体电池', '电压']
    }
]
```

### 案例2: ISO 26262-3:2018 处理示例
```python
# 原始文档信息
document_info = {
    'title': 'ISO 26262-3:2018 道路车辆功能安全 第3部分：概念阶段',
    'pages': 89,
    'language': 'en-zh',  # 双语文档
    'technical_domain': ['功能安全', '汽车电子', '风险评估']
}

# 处理策略
processing_strategy = {
    'language_handling': 'extract_both',  # 提取中英文内容
    'technical_focus': 'safety_requirements',
    'chunk_size': 600,
    'preserve_standards_structure': True
}

# 生成向量数量
vectors_generated = 156
```

通过以上详细的指导方法和参考案例，可以系统性地准备汽车电器领域的训练数据，确保数据质量和训练效果。
