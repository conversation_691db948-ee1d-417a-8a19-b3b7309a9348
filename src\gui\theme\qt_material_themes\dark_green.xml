<!--?xml version="1.0" encoding="UTF-8"?-->
<resources>
  <color name="primaryColor">#4caf50</color>
  <color name="primaryLightColor">#80e27e</color>
  <color name="secondaryColor">#4caf50</color>
  <color name="secondaryLightColor">#80e27e</color>
  <color name="secondaryDarkColor">#087f23</color>
  <color name="primaryTextColor">#000000</color>
  <color name="secondaryTextColor">#ffffff</color>
  
  <color name="primaryDarkColor">#087f23</color>
  <color name="background">#121212</color>
  <color name="surface">#1e1e1e</color>
  <color name="error">#cf6679</color>
  <color name="onPrimary">#000000</color>
  <color name="onSecondary">#000000</color>
  <color name="onBackground">#ffffff</color>
  <color name="onSurface">#ffffff</color>
  <color name="onError">#000000</color>
  <color name="disabled">#424242</color>
  <color name="disabledText">#757575</color>
  <color name="divider">#333333</color>
  <color name="scrollbar">#424242</color>
  <color name="scrollbarHover">#616161</color>
  <color name="scrollbarPressed">#757575</color>
  <color name="selection">#4caf50</color>
  <color name="highlight">#4caf50</color>
  <color name="link">#4caf50</color>
  <color name="visited">#087f23</color>
  <color name="altBackground">#1e1e1e</color>
  <color name="tooltip">#1e1e1e</color>
  <color name="tooltipText">#ffffff</color>
  <color name="button">#4caf50</color>
  <color name="buttonText">#ffffff</color>
  <color name="buttonHover">#80e27e</color>
  <color name="buttonPressed">#087f23</color>
  <color name="buttonDisabled">#424242</color>
  <color name="buttonDisabledText">#757575</color>
  <color name="buttonFocus">#4caf50</color>
  <color name="buttonFocusText">#ffffff</color>
  <color name="tab">#1e1e1e</color>
  <color name="tabText">#ffffff</color>
  <color name="tabHover">#333333</color>
  <color name="tabHoverText">#ffffff</color>
  <color name="tabSelected">#4caf50</color>
  <color name="tabSelectedText">#ffffff</color>
  <color name="tabDisabled">#424242</color>
  <color name="tabDisabledText">#757575</color>
  <color name="menu">#1e1e1e</color>
  <color name="menuText">#ffffff</color>
  <color name="menuHover">#333333</color>
  <color name="menuHoverText">#ffffff</color>
  <color name="menuSelected">#4caf50</color>
  <color name="menuSelectedText">#ffffff</color>
  <color name="menuDisabled">#424242</color>
  <color name="menuDisabledText">#757575</color>
  <color name="menuSeparator">#333333</color>
  <color name="menuIcon">#ffffff</color>
  <color name="menuIconDisabled">#757575</color>
  <color name="menuBar">#1e1e1e</color>
  <color name="menuBarText">#ffffff</color>
  <color name="menuBarHover">#333333</color>
  <color name="menuBarHoverText">#ffffff</color>
  <color name="menuBarSelected">#4caf50</color>
  <color name="menuBarSelectedText">#ffffff</color>
  <color name="toolBar">#1e1e1e</color>
  <color name="toolBarText">#ffffff</color>
  <color name="toolBarHover">#333333</color>
  <color name="toolBarHoverText">#ffffff</color>
  <color name="toolBarSelected">#4caf50</color>
  <color name="toolBarSelectedText">#ffffff</color>
  <color name="toolBarDisabled">#424242</color>
  <color name="toolBarDisabledText">#757575</color>
  <color name="toolBarSeparator">#333333</color>
  <color name="toolBarIcon">#ffffff</color>
  <color name="toolBarIconDisabled">#757575</color>
  <color name="dockWidget">#1e1e1e</color>
  <color name="dockWidgetText">#ffffff</color>
  <color name="dockWidgetTitle">#1e1e1e</color>
  <color name="dockWidgetTitleText">#ffffff</color>
  <color name="dockWidgetTitleHover">#333333</color>
  <color name="dockWidgetTitleHoverText">#ffffff</color>
  <color name="dockWidgetTitleSelected">#4caf50</color>
  <color name="dockWidgetTitleSelectedText">#ffffff</color>
  <color name="dockWidgetTitleDisabled">#424242</color>
  <color name="dockWidgetTitleDisabledText">#757575</color>
  <color name="dockWidgetSeparator">#333333</color>
  <color name="dockWidgetIcon">#ffffff</color>
  <color name="dockWidgetIconDisabled">#757575</color>
  <color name="statusBar">#1e1e1e</color>
  <color name="statusBarText">#ffffff</color>
  <color name="statusBarSeparator">#333333</color>
  <color name="statusBarIcon">#ffffff</color>
  <color name="statusBarIconDisabled">#757575</color>
  <color name="titleBar">#1e1e1e</color>
  <color name="titleBarText">#ffffff</color>
  <color name="titleBarIcon">#ffffff</color>
  <color name="titleBarIconDisabled">#757575</color>
  <color name="titleBarMinimize">#1e1e1e</color>
  <color name="titleBarMinimizeText">#ffffff</color>
  <color name="titleBarMinimizeHover">#333333</color>
  <color name="titleBarMinimizeHoverText">#ffffff</color>
  <color name="titleBarMaximize">#1e1e1e</color>
  <color name="titleBarMaximizeText">#ffffff</color>
  <color name="titleBarMaximizeHover">#333333</color>
  <color name="titleBarMaximizeHoverText">#ffffff</color>
  <color name="titleBarClose">#1e1e1e</color>
  <color name="titleBarCloseText">#ffffff</color>
  <color name="titleBarCloseHover">#cf6679</color>
  <color name="titleBarCloseHoverText">#ffffff</color>
  <color name="groupBox">#1e1e1e</color>
  <color name="groupBoxText">#ffffff</color>
  <color name="groupBoxTitle">#ffffff</color>
  <color name="groupBoxTitleBackground">#1e1e1e</color>
  <color name="groupBoxBorder">#333333</color>
  <color name="groupBoxIcon">#ffffff</color>
  <color name="groupBoxIconDisabled">#757575</color>
  <color name="frame">#1e1e1e</color>
  <color name="frameText">#ffffff</color>
  <color name="frameBorder">#333333</color>
  <color name="frameFocus">#4caf50</color>
  <color name="frameFocusText">#ffffff</color>
  <color name="frameDisabled">#424242</color>
  <color name="frameDisabledText">#757575</color>
  <color name="frameIcon">#ffffff</color>
  <color name="frameIconDisabled">#757575</color>
  <color name="spinBox">#1e1e1e</color>
  <color name="spinBoxText">#ffffff</color>
  <color name="spinBoxBorder">#333333</color>
  <color name="spinBoxFocus">#4caf50</color>
  <color name="spinBoxFocusText">#ffffff</color>
  <color name="spinBoxDisabled">#424242</color>
  <color name="spinBoxDisabledText">#757575</color>
  <color name="spinBoxIcon">#ffffff</color>
  <color name="spinBoxIconDisabled">#757575</color>
  <color name="spinBoxButton">#1e1e1e</color>
  <color name="spinBoxButtonText">#ffffff</color>
  <color name="spinBoxButtonHover">#333333</color>
  <color name="spinBoxButtonHoverText">#ffffff</color>
  <color name="spinBoxButtonPressed">#4caf50</color>
  <color name="spinBoxButtonPressedText">#ffffff</color>
  <color name="spinBoxButtonDisabled">#424242</color>
  <color name="spinBoxButtonDisabledText">#757575</color>
  <color name="comboBox">#1e1e1e</color>
  <color name="comboBoxText">#ffffff</color>
  <color name="comboBoxBorder">#333333</color>
  <color name="comboBoxFocus">#4caf50</color>
  <color name="comboBoxFocusText">#ffffff</color>
  <color name="comboBoxDisabled">#424242</color>
  <color name="comboBoxDisabledText">#757575</color>
  <color name="comboBoxIcon">#ffffff</color>
  <color name="comboBoxIconDisabled">#757575</color>
  <color name="comboBoxDropDown">#1e1e1e</color>
  <color name="comboBoxDropDownText">#ffffff</color>
  <color name="comboBoxDropDownHover">#333333</color>
  <color name="comboBoxDropDownHoverText">#ffffff</color>
  <color name="comboBoxDropDownSelected">#4caf50</color>
  <color name="comboBoxDropDownSelectedText">#ffffff</color>
  <color name="comboBoxDropDownDisabled">#424242</color>
  <color name="comboBoxDropDownDisabledText">#757575</color>
  <color name="comboBoxDropDownSeparator">#333333</color>
  <color name="comboBoxDropDownIcon">#ffffff</color>
  <color name="comboBoxDropDownIconDisabled">#757575</color>
  <color name="comboBoxButton">#1e1e1e</color>
  <color name="comboBoxButtonText">#ffffff</color>
  <color name="comboBoxButtonHover">#333333</color>
  <color name="comboBoxButtonHoverText">#ffffff</color>
  <color name="comboBoxButtonPressed">#4caf50</color>
  <color name="comboBoxButtonPressedText">#ffffff</color>
  <color name="comboBoxButtonDisabled">#424242</color>
  <color name="comboBoxButtonDisabledText">#757575</color>
  <color name="slider">#1e1e1e</color>
  <color name="sliderText">#ffffff</color>
  <color name="sliderBorder">#333333</color>
  <color name="sliderFocus">#4caf50</color>
  <color name="sliderFocusText">#ffffff</color>
  <color name="sliderDisabled">#424242</color>
  <color name="sliderDisabledText">#757575</color>
  <color name="sliderIcon">#ffffff</color>
  <color name="sliderIconDisabled">#757575</color>
  <color name="sliderHandle">#4caf50</color>
  <color name="sliderHandleText">#ffffff</color>
  <color name="sliderHandleHover">#80e27e</color>
  <color name="sliderHandleHoverText">#ffffff</color>
  <color name="sliderHandlePressed">#087f23</color>
  <color name="sliderHandlePressedText">#ffffff</color>
  <color name="sliderHandleDisabled">#424242</color>
  <color name="sliderHandleDisabledText">#757575</color>
  <color name="sliderGroove">#333333</color>
  <color name="sliderGrooveText">#ffffff</color>
  <color name="sliderGrooveDisabled">#424242</color>
  <color name="sliderGrooveDisabledText">#757575</color>
  <color name="progressBar">#1e1e1e</color>
  <color name="progressBarText">#ffffff</color>
  <color name="progressBarBorder">#333333</color>
  <color name="progressBarFocus">#4caf50</color>
  <color name="progressBarFocusText">#ffffff</color>
  <color name="progressBarDisabled">#424242</color>
  <color name="progressBarDisabledText">#757575</color>
  <color name="progressBarIcon">#ffffff</color>
  <color name="progressBarIconDisabled">#757575</color>
  <color name="progressBarChunk">#4caf50</color>
  <color name="progressBarChunkText">#ffffff</color>
  <color name="progressBarChunkDisabled">#424242</color>
  <color name="progressBarChunkDisabledText">#757575</color>
  <color name="progressBarGroove">#333333</color>
  <color name="progressBarGrooveText">#ffffff</color>
  <color name="progressBarGrooveDisabled">#424242</color>
  <color name="progressBarGrooveDisabledText">#757575</color>
</resources>
