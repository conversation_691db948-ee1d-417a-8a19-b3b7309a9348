/* 矩阵主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Courier New', monospace;
    font-size: 10pt;
}

QMainWindow {
    background-color: #000000;
}

QLabel {
    color: #00FF00;
}

QPushButton {
    background-color: #001800;
    color: #00FF00;
    border: 1px solid #00FF00;
    border-radius: 0px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #003000;
    border: 1px solid #00FF00;
}

QPushButton:pressed {
    background-color: #004800;
    border: 1px solid #00FF00;
}

QPushButton:disabled {
    background-color: #001000;
    color: #006000;
    border: 1px solid #006000;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #000800;
    color: #00FF00;
    border: 1px solid #00FF00;
    border-radius: 0px;
    padding: 4px;
    selection-background-color: #00FF00;
    selection-color: #000000;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #00FF00;
    background-color: #001800;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: 1px solid #00FF00;
    background-color: #000000;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #00FF00;
    min-height: 20px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: 1px solid #00FF00;
    background-color: #000000;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #00FF00;
    min-width: 20px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #000800;
    color: #00FF00;
    border-bottom: 1px solid #00FF00;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #003000;
}

QMenu {
    background-color: #000800;
    color: #00FF00;
    border: 1px solid #00FF00;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #003000;
}

QTabWidget::pane {
    border: 1px solid #00FF00;
    background-color: #000800;
}

QTabBar::tab {
    background-color: #000800;
    color: #00FF00;
    padding: 8px 12px;
    border: 1px solid #00FF00;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #001800;
}

QStatusBar {
    background-color: #000800;
    color: #00FF00;
    border-top: 1px solid #00FF00;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #00FF00;
}

#sidebarWidget {
    background-color: #000800;
    min-width: 200px;
    max-width: 200px;
    border-right: 1px solid #00FF00;
}

#contentWidget {
    background-color: #000000;
}

#dashboardWidget {
    background-color: #000000;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #000800;
    border-radius: 0px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #00FF00;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #00FF00;
    margin-bottom: 8px;
}

.card-content {
    color: #00FF00;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #00FF00;
    border-radius: 0px;
}

.glow-effect {
    border: 1px solid #00FF00;
    box-shadow: 0 0 10px #00FF00;
}
