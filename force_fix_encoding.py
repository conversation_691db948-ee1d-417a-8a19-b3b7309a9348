#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
强制修复编码问题
"""

import json
import re
from pathlib import Path

def force_fix_encoding():
    """强制修复编码问题"""
    
    # 具体的乱码映射
    fixes = {
        # 从您提供的乱码示例中提取的映射
        'XS71 14A121 AA EN 1998 09 绾挎潫鎬绘垚鍒堕€犳爣鍑?': 'XS71 14A121 AA EN 1998 09 线束总成制造标准',
        'A 009 000 04 99 CH 2013 07 濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': 'A 009 000 04 99 CH 2013 07 奔驰线束焊接测试标准',
        '绾挎潫鎬绘垚鍒堕€犳爣鍑?': '线束总成制造标准',
        '濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '奔驰线束焊接测试标准',
        '绾挎潫': '线束',
        '鐢ㄦ埛鎻愪緵': '用户提供',
        '绂忕壒': '福特',
        '濂旈┌': '奔驰',
        '鍒堕€?': '制造',
        '鎬绘垚': '总成',
        '鐒婃帴': '焊接',
        '娴嬭瘯': '测试',
        '鏍囧噯': '标准',
        '姹借溅': '汽车',
        '鍥剧焊': '图纸',
        '瑕佹眰': '要求',
        '鐢靛瓙': '电子',
        '鐢垫皵': '电气',
        '閮ㄤ欢': '部件',
        '缁勪欢': '组件',
        '杩炴帴鍣?': '连接器',
        '绔瓙': '端子',
        '鎺ユ彃浠?': '接插件',
        '淇濋櫓涓?': '保险丝',
        '缁ф斁鍣?': '继电器',
        '浼犳劅鍣?': '传感器',
        '鎵ц鍣?': '执行器',
        '鎺у埗鍣?': '控制器',
        '妯″潡': '模块',
        '绯荤粺': '系统',
        '缃戠粶': '网络',
        '閫氫俊': '通信',
        '鍗忚': '协议',
        '鏁版嵁': '数据',
        '淇″彿': '信号',
        '鐢垫簮': '电源',
        '鎺ュ湴': '接地',
        '灞忚斀': '屏蔽',
        '缁濈紭': '绝缘',
        '闃叉按': '防水',
        '闃插皹': '防尘',
        '鑰愭俯': '耐温',
        '鑰愮(': '耐磨',
        '鑰愯厫铓?': '耐腐蚀',
        '鑰愭补': '耐油',
        '鑰愮噧': '耐燃',
        '闃荤噧': '阻燃',
        '鐜繚': '环保',
        '瀹夊叏': '安全',
        '鍙潬鎬?': '可靠性',
        '鑰愪箙鎬?': '耐久性',
        '绋冲畾鎬?': '稳定性',
        '鍏煎鎬?': '兼容性',
        '鎬ц兘': '性能',
        '鍔熻兘': '功能',
        '鍙傛暟': '参数',
        '瑙勬牸': '规格',
        '灏哄': '尺寸',
        '閲嶉噺': '重量',
        '鏉愭枡': '材料',
        '宸ヨ壓': '工艺',
        '鍒堕€?': '制造',
        '鐢熶骇': '生产',
        '瑁呴厤': '装配',
        '瀹夎': '安装',
        '璋冭瘯': '调试',
        '妫€娴?': '检测',
        '娴嬭瘯': '测试',
        '楠岃瘉': '验证',
        '璁よ瘉': '认证',
        '瀹℃牳': '审核',
        '鎵瑰噯': '批准',
        '鍙戝竷': '发布',
        '瀹炴柦': '实施',
        '鎵ц': '执行',
        '绠＄悊': '管理',
        '鎺у埗': '控制',
        '鐩戞帶': '监控',
        '缁存姢': '维护',
        '淇濆吇': '保养',
        '缁翠慨': '维修',
        '鏇存崲': '更换',
        '鍗囩骇': '升级',
        '鏀硅繘': '改进',
        '浼樺寲': '优化',
        '鍒涙柊': '创新',
        '鍙戝睍': '发展',
        '搴旂敤': '应用',
        '瀹炵幇': '实现'
    }
    
    def fix_text(text):
        """修复文本"""
        if not text:
            return text
        
        fixed = text
        # 按长度排序，先替换长的字符串
        for wrong, correct in sorted(fixes.items(), key=lambda x: len(x[0]), reverse=True):
            fixed = fixed.replace(wrong, correct)
        
        return fixed
    
    # 查找所有包含乱码的元数据文件
    metadata_dir = Path("training_data/raw_documents/enterprise_standards/metadata")
    
    if not metadata_dir.exists():
        print("元数据目录不存在")
        return
    
    # 查找所有元数据文件
    metadata_files = list(metadata_dir.glob("*_metadata.json"))
    
    print(f"找到 {len(metadata_files)} 个元数据文件")
    
    fixed_count = 0
    
    for file_path in metadata_files:
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # 检查是否包含乱码
            has_garbled = any(char in content for char in ['绾', '濂', '鐢', '鍒', '鎬', '鐒', '娴', '鏍', '姹', '鍥', '瑕'])
            
            if not has_garbled:
                continue
            
            print(f"\n处理文件: {file_path.name}")
            
            # 解析JSON
            metadata = json.loads(content)
            
            # 显示原始内容
            print(f"原标题: {metadata.get('title', 'N/A')}")
            print(f"原来源: {metadata.get('source', 'N/A')}")
            
            # 修复各个字段
            changed = False
            
            for field in ['title', 'source', 'file_path', 'abstract']:
                if field in metadata and metadata[field]:
                    original = metadata[field]
                    fixed = fix_text(original)
                    if fixed != original:
                        metadata[field] = fixed
                        changed = True
                        print(f"修复{field}: {fixed}")
            
            if 'keywords' in metadata and isinstance(metadata['keywords'], list):
                original_keywords = metadata['keywords']
                fixed_keywords = [fix_text(kw) for kw in original_keywords]
                if fixed_keywords != original_keywords:
                    metadata['keywords'] = fixed_keywords
                    changed = True
                    print(f"修复关键词: {fixed_keywords}")
            
            # 保存修复后的文件
            if changed:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                
                fixed_count += 1
                print("✅ 修复完成并保存")
            else:
                print("⏭️ 无需修复")
                
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path.name}: {e}")
    
    print(f"\n📊 修复完成:")
    print(f"  总共修复: {fixed_count} 个文件")

if __name__ == "__main__":
    print("🚀 开始强制编码修复")
    print("=" * 80)
    
    force_fix_encoding()
    
    print("\n🎯 强制编码修复完成")
    print("=" * 80)
