
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MD Vector Processor - 主程序入口
用于处理Markdown文件并生成向量化表示
"""

import argparse
import logging
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any

def setup_logging(config: Dict[str, Any]) -> None:
    """设置日志配置"""
    log_config = config['logging']
    log_dir = Path(log_config['log_dir'])
    log_dir.mkdir(parents=True, exist_ok=True)
    
    log_file = log_dir / log_config['log_file']
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout) if log_config['console_log'] else None
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成")

# def load_config(config_path: str) -> Dict[str, Any]:                                                      #新增修改，此处备份，下记为新增
#     """加载配置文件"""
#     try:
#         with open(config_path, 'r', encoding='utf-8') as f:
#             config = yaml.safe_load(f)
#         return config
#     except Exception as e:
#         print(f"Error loading config file: {e}")
#         sys.exit(1)



def load_config():
    """加载配置"""
    config_path = Path('config/config.yaml')
    models_path = Path('config/models.yaml')
    
    # 加载主配置
    config = ConfigUtils.load_config(config_path)
    
    # 如果存在models.yaml，加载并合并
    if models_path.exists():
        models_config = ConfigUtils.load_config(models_path)
        config.update(models_config)
    
    return config

def validate_config(config: Dict[str, Any]) -> bool:
    """验证配置文件的完整性和正确性"""
    required_sections = ['file_processing', 'preprocessing', 'vectorization', 'indexing', 'storage', 'system', 'logging']
    
    for section in required_sections:
        if section not in config:
            logging.error(f"配置文件缺少必要的部分: {section}")
            return False
    
    # 验证目录配置
    try:
        Path(config['file_processing']['input_dir']).mkdir(parents=True, exist_ok=True)
        Path(config['file_processing']['output_dir']).mkdir(parents=True, exist_ok=True)
        Path(config['file_processing']['vector_dir']).mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logging.error(f"创建必要目录时出错: {e}")
        return False
    
    return True

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor - 文档向量化处理工具')
    parser.add_argument(
        '--config',
        type=str,
        default='config/config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--input-dir',
        type=str,
        help='输入目录路径（覆盖配置文件设置）'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    return parser.parse_args()

class MDVectorProcessor:
    """MD文档向量化处理器主类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def initialize(self) -> bool:
        """初始化处理器"""
        try:
            # TODO: 初始化各个模块
            self.logger.info("初始化处理器...")
            return True
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    def process(self) -> bool:
        """执行处理流程"""
        try:
            # TODO: 实现主要处理流程
            self.logger.info("开始处理...")
            return True
        except Exception as e:
            self.logger.error(f"处理过程中出错: {e}")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            # TODO: 清理资源
            self.logger.info("清理资源...")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

def main():
    """主程序入口"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 加载配置
    config = load_config(args.config)
    
    # 如果提供了命令行输入目录，覆盖配置
    if args.input_dir:
        config['file_processing']['input_dir'] = args.input_dir
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    # 验证配置
    if not validate_config(config):
        logger.error("配置验证失败")
        sys.exit(1)
    
    # 创建处理器实例
    processor = MDVectorProcessor(config)
    
    # 初始化处理器
    if not processor.initialize():
        logger.error("处理器初始化失败")
        sys.exit(1)
    
    try:
        # 执行处理流程
        if not processor.process():
            logger.error("处理过程失败")
            sys.exit(1)
        
        logger.info("处理完成")
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在清理...")
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
    finally:
        # 清理资源
        processor.cleanup()

if __name__ == "__main__":
    main()