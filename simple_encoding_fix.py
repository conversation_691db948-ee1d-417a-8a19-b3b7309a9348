#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单编码修复脚本
"""

import json
import os
from pathlib import Path

def fix_encoding_issues():
    """修复编码问题"""
    
    # 编码映射表
    encoding_fixes = {
        '绾挎潫': '线束',
        '姊呰禌寰锋柉濂旈┌': '梅赛德斯奔驰',
        '濂旈┌': '奔驰',
        '绂忕壒': '福特',
        '鐢ㄦ埛鎻愪緵': '用户提供',
        '鍒堕€犳爣鍑?': '制造标准',
        '鎬绘垚': '总成',
        '鐒婃帴': '焊接',
        '娴嬭瘯': '测试',
        '鏍囧噯': '标准',
        '姹借溅': '汽车',
        '鍥剧焊': '图纸',
        '瑕佹眰': '要求',
        '濂旈': '奔驰',
        '鍒堕€?': '制造',
        '鎬绘垚鍒堕€犳爣鍑?': '总成制造标准',
        '绾挎潫鎬绘垚鍒堕€犳爣鍑?': '线束总成制造标准',
        '绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '线束焊接测试标准',
        '濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '奔驰线束焊接测试标准',
    }
    
    def fix_text(text):
        """修复文本中的编码问题"""
        if not text:
            return text
        
        fixed = text
        for wrong, correct in encoding_fixes.items():
            fixed = fixed.replace(wrong, correct)
        return fixed
    
    # 要处理的具体文件
    problem_files = [
        "training_data/raw_documents/enterprise_standards/metadata/ES_XS71-14A121-AA_EN_1998-09_线束总成制造标准_metadata.json",
        "training_data/raw_documents/enterprise_standards/metadata/A 009 000 04 99_CH_2013-07_奔驰线束焊接测试标准_metadata.json"
    ]
    
    for file_path in problem_files:
        file_path = Path(file_path)
        if not file_path.exists():
            print(f"文件不存在: {file_path}")
            continue
            
        print(f"处理文件: {file_path.name}")
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            metadata = json.loads(content)
            
            print(f"原标题: {metadata.get('title', 'N/A')}")
            
            # 修复各个字段
            changed = False
            
            if 'title' in metadata:
                original = metadata['title']
                fixed = fix_text(original)
                if fixed != original:
                    metadata['title'] = fixed
                    changed = True
                    print(f"修复标题: {fixed}")
            
            if 'source' in metadata:
                original = metadata['source']
                fixed = fix_text(original)
                if fixed != original:
                    metadata['source'] = fixed
                    changed = True
                    print(f"修复来源: {fixed}")
            
            if 'file_path' in metadata:
                original = metadata['file_path']
                fixed = fix_text(original)
                if fixed != original:
                    metadata['file_path'] = fixed
                    changed = True
                    print(f"修复路径: {fixed}")
            
            if 'keywords' in metadata and isinstance(metadata['keywords'], list):
                original_keywords = metadata['keywords']
                fixed_keywords = [fix_text(kw) for kw in original_keywords]
                if fixed_keywords != original_keywords:
                    metadata['keywords'] = fixed_keywords
                    changed = True
                    print(f"修复关键词: {fixed_keywords}")
            
            if 'abstract' in metadata:
                original = metadata['abstract']
                fixed = fix_text(original)
                if fixed != original:
                    metadata['abstract'] = fixed
                    changed = True
                    print(f"修复摘要: {fixed}")
            
            # 保存修复后的文件
            if changed:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                print("✅ 修复完成并保存")
            else:
                print("⏭️ 无需修复")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    print("🚀 开始简单编码修复")
    print("=" * 80)
    
    fix_encoding_issues()
    
    print("\n🎯 编码修复完成")
    print("=" * 80)
