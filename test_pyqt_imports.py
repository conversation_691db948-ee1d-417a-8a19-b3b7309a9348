#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 PyQt6 导入
"""

import sys
import traceback

def test_pyqt_imports():
    """测试 PyQt6 导入"""
    try:
        print("尝试导入 PyQt6...")
        import PyQt6
        print(f"成功导入 PyQt6，版本: {PyQt6.__version__ if hasattr(PyQt6, '__version__') else '未知'}")
        
        print("\n尝试导入 PyQt6.QtWidgets...")
        from PyQt6 import QtWidgets
        print("成功导入 PyQt6.QtWidgets")
        
        print("\n尝试导入 PyQt6.QtCore...")
        from PyQt6 import QtCore
        print("成功导入 PyQt6.QtCore")
        
        print("\n尝试导入 PyQt6.QtGui...")
        from PyQt6 import QtGui
        print("成功导入 PyQt6.QtGui")
        
        print("\n尝试导入 QActionGroup...")
        try:
            from PyQt6.QtWidgets import QActionGroup
            print("成功从 PyQt6.QtWidgets 导入 QActionGroup")
        except ImportError:
            print("无法从 PyQt6.QtWidgets 导入 QActionGroup，尝试从 PyQt6.QtGui 导入...")
            try:
                from PyQt6.QtGui import QActionGroup
                print("成功从 PyQt6.QtGui 导入 QActionGroup")
            except ImportError:
                print("无法从 PyQt6.QtGui 导入 QActionGroup")
                print("尝试检查 QActionGroup 在哪个模块中...")
                
                # 尝试查找 QActionGroup 在哪个模块中
                for module_name in ["QtWidgets", "QtGui", "QtCore"]:
                    module = getattr(PyQt6, module_name)
                    if hasattr(module, "QActionGroup"):
                        print(f"找到 QActionGroup 在 PyQt6.{module_name} 模块中")
                    else:
                        print(f"QActionGroup 不在 PyQt6.{module_name} 模块中")
        
        print("\n尝试创建 QApplication...")
        app = QtWidgets.QApplication(sys.argv)
        print("成功创建 QApplication")
        
        print("\n尝试创建 QMainWindow...")
        window = QtWidgets.QMainWindow()
        print("成功创建 QMainWindow")
        
        print("\n尝试创建 QAction...")
        action = QtGui.QAction("测试动作", window)
        print("成功创建 QAction")
        
        print("\n尝试创建 QActionGroup...")
        try:
            action_group = QtGui.QActionGroup(window)
            print("成功创建 QActionGroup")
            
            # 尝试使用 QActionGroup
            action_group.setExclusive(True)
            print("成功设置 QActionGroup 为互斥")
            
            action_group.addAction(action)
            print("成功添加 QAction 到 QActionGroup")
        except Exception as e:
            print(f"创建或使用 QActionGroup 时出错: {e}")
        
        print("\nPyQt6 导入测试成功!")
        return True
    
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装 PyQt6:")
        print("pip install PyQt6")
        return False
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pyqt_imports()
