#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的 PyQt 应用程序
"""

import sys
import traceback

def run_simple_gui():
    """运行简单的 PyQt 应用程序"""
    try:
        # 尝试导入 PyQt6
        try:
            print("尝试导入 PyQt6...")
            from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
            from PyQt6.QtCore import Qt
            print("成功导入 PyQt6")
            qt_version = 6
        except ImportError:
            print("未找到 PyQt6，尝试导入 PyQt5...")
            from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
            from PyQt5.QtCore import Qt
            print("成功导入 PyQt5")
            qt_version = 5
        
        # 创建应用程序
        print("创建应用程序...")
        app = QApplication(sys.argv)
        
        # 创建主窗口
        print("创建主窗口...")
        window = QMainWindow()
        window.setWindowTitle("简单的 PyQt 应用程序")
        window.setGeometry(100, 100, 400, 200)
        
        # 创建中央部件
        print("创建中央部件...")
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        print("创建布局...")
        layout = QVBoxLayout(central_widget)
        
        # 创建标签
        print("创建标签...")
        label = QLabel(f"这是一个简单的 PyQt{qt_version} 应用程序")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter if qt_version == 6 else Qt.AlignCenter)
        layout.addWidget(label)
        
        # 显示窗口
        print("显示窗口...")
        window.show()
        
        # 运行应用程序
        print("运行应用程序...")
        sys.exit(app.exec() if qt_version == 6 else app.exec_())
    
    except Exception as e:
        print(f"运行简单的 PyQt 应用程序时出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    run_simple_gui()
