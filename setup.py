
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MD Vector Processor
设置脚本
"""

import os
from setuptools import setup, find_packages

# 读取README文件
def read_readme():
    with open('README.md', 'r', encoding='utf-8') as f:
        return f.read()

# 读取版本信息
def read_version():
    version_file = os.path.join('src', 'version.py')
    if os.path.exists(version_file):
        with open(version_file, 'r', encoding='utf-8') as f:
            exec(f.read())
            return locals().get('__version__', '1.0.0')
    return '1.0.0'

# 读取requirements文件
def read_requirements(filename='requirements.txt'):
    with open(filename, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name='md_vector_processor',
    version=read_version(),
    description='A powerful Markdown document vectorization and processing tool',
    long_description=read_readme(),
    long_description_content_type='text/markdown',
    author='Your Name',
    author_email='<EMAIL>',
    url='https://github.com/yourusername/md_vector_processor',
    
    # 包信息
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    include_package_data=True,
    
    # 依赖项
    install_requires=read_requirements(),
    extras_require={
        'dev': [
            'pytest>=6.2.5',
            'black>=21.7b0',
            'flake8>=3.9.0',
            'mypy>=0.910',
            'isort>=5.9.0',
        ],
        'gpu': [
            'faiss-gpu>=1.7.0',
            'torch>=1.9.0+cu111',  # 根据CUDA版本调整
        ],
    },
    
    # 项目分类
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Topic :: Text Processing :: Markup :: Markdown',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
    ],
    
    # 项目关键字
    keywords='markdown, vectorization, embeddings, document-processing, nlp',
    
    # Python版本要求
    python_requires='>=3.8',
    
    # 入口点
    entry_points={
        'console_scripts': [
            'md-vector-processor=md_vector_processor.main:main',
        ],
    },
    
    # 项目URLs
    project_urls={
        'Bug Reports': 'https://github.com/yourusername/md_vector_processor/issues',
        'Source': 'https://github.com/yourusername/md_vector_processor',
        'Documentation': 'https://md-vector-processor.readthedocs.io/',
    },
    
    # 测试套件
    test_suite='tests',
    
    # 其他元数据
    license='MIT',
    platforms=['any'],
    zip_safe=False,
)