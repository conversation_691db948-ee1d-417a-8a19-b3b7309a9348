#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文档加载器

提供文档完整性检查和详细的加载进度跟踪功能
"""

import os
import logging
import hashlib
from typing import Dict, List, Optional, Tuple, Callable
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)


class LoadingStatus(Enum):
    """加载状态枚举"""
    PENDING = "pending"
    LOADING = "loading"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class DocumentInfo:
    """文档信息"""
    path: str
    size: int
    encoding: Optional[str] = None
    content_hash: Optional[str] = None
    content_length: int = 0
    load_time: float = 0.0
    status: LoadingStatus = LoadingStatus.PENDING
    error_message: Optional[str] = None


class DocumentLoader:
    """文档加载器"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        """
        初始化文档加载器
        
        Args:
            progress_callback: 进度回调函数，接收 (current, total, message) 参数
        """
        self.progress_callback = progress_callback
        self.logger = logging.getLogger(__name__)
        
        # 支持的文件类型
        self.supported_extensions = {
            '.txt', '.md', '.markdown', '.rst', '.csv',
            '.json', '.xml', '.html', '.htm', '.log',
            '.py', '.js', '.css', '.sql', '.yaml', '.yml',
            '.xlsx', '.xls', '.xlsm', '.docx', '.doc', '.pdf'
        }
        
        # 编码检测顺序
        self.encoding_order = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252', 'utf-16']
    
    def load_documents(self, file_paths: List[str], 
                      validate_integrity: bool = True) -> Tuple[Dict[str, str], List[DocumentInfo]]:
        """
        批量加载文档
        
        Args:
            file_paths: 文件路径列表
            validate_integrity: 是否验证文档完整性
            
        Returns:
            Tuple[Dict[str, str], List[DocumentInfo]]: (文档内容字典, 文档信息列表)
        """
        documents = {}
        doc_infos = []
        
        total_files = len(file_paths)
        self.logger.info(f"开始加载 {total_files} 个文档")
        
        for i, file_path in enumerate(file_paths):
            if self.progress_callback:
                self.progress_callback(i, total_files, f"正在加载: {Path(file_path).name}")
            
            doc_info = self._load_single_document(file_path, validate_integrity)
            doc_infos.append(doc_info)
            
            if doc_info.status == LoadingStatus.COMPLETED:
                # 从文件路径读取内容（这里简化处理）
                try:
                    content = self._read_file_content(file_path, doc_info.encoding)
                    documents[file_path] = content
                    self.logger.info(f"成功加载文档: {file_path}")
                except Exception as e:
                    doc_info.status = LoadingStatus.FAILED
                    doc_info.error_message = f"读取内容失败: {str(e)}"
                    self.logger.error(f"读取文档内容失败: {file_path}, {e}")
        
        if self.progress_callback:
            self.progress_callback(total_files, total_files, "加载完成")
        
        # 生成加载报告
        self._generate_loading_report(doc_infos)
        
        return documents, doc_infos
    
    def _load_single_document(self, file_path: str, validate_integrity: bool) -> DocumentInfo:
        """
        加载单个文档
        
        Args:
            file_path: 文件路径
            validate_integrity: 是否验证完整性
            
        Returns:
            DocumentInfo: 文档信息
        """
        start_time = time.time()
        path = Path(file_path)
        
        # 初始化文档信息
        doc_info = DocumentInfo(
            path=file_path,
            size=0,
            status=LoadingStatus.LOADING
        )
        
        try:
            # 检查文件是否存在
            if not path.exists():
                doc_info.status = LoadingStatus.FAILED
                doc_info.error_message = "文件不存在"
                return doc_info
            
            # 检查是否是文件
            if not path.is_file():
                doc_info.status = LoadingStatus.SKIPPED
                doc_info.error_message = "不是文件"
                return doc_info
            
            # 检查文件扩展名
            if path.suffix.lower() not in self.supported_extensions:
                doc_info.status = LoadingStatus.SKIPPED
                doc_info.error_message = f"不支持的文件类型: {path.suffix}"
                return doc_info
            
            # 获取文件大小
            doc_info.size = path.stat().st_size
            
            # 检查文件大小限制（100MB）
            if doc_info.size > 100 * 1024 * 1024:
                doc_info.status = LoadingStatus.SKIPPED
                doc_info.error_message = "文件过大（超过100MB）"
                return doc_info
            
            # 检测文件编码
            doc_info.encoding = self._detect_encoding(file_path)
            if not doc_info.encoding:
                doc_info.status = LoadingStatus.FAILED
                doc_info.error_message = "无法检测文件编码"
                return doc_info
            
            # 读取文件内容进行验证
            content = self._read_file_content(file_path, doc_info.encoding)
            doc_info.content_length = len(content)
            
            # 验证文档完整性
            if validate_integrity:
                integrity_result = self._validate_document_integrity(content, path.suffix)
                if not integrity_result[0]:
                    doc_info.status = LoadingStatus.FAILED
                    doc_info.error_message = f"完整性验证失败: {integrity_result[1]}"
                    return doc_info
            
            # 计算内容哈希
            doc_info.content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            
            # 记录加载时间
            doc_info.load_time = time.time() - start_time
            doc_info.status = LoadingStatus.COMPLETED
            
            self.logger.debug(f"文档加载成功: {file_path}, 大小: {doc_info.size}, 编码: {doc_info.encoding}")
            
        except Exception as e:
            doc_info.status = LoadingStatus.FAILED
            doc_info.error_message = str(e)
            doc_info.load_time = time.time() - start_time
            self.logger.error(f"加载文档时出错: {file_path}, {e}")
        
        return doc_info
    
    def _detect_encoding(self, file_path: str) -> Optional[str]:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 检测到的编码，如果检测失败返回None
        """
        try:
            # 读取文件的前1024字节进行编码检测
            with open(file_path, 'rb') as f:
                raw_data = f.read(1024)
            
            # 尝试不同的编码
            for encoding in self.encoding_order:
                try:
                    raw_data.decode(encoding)
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，尝试使用chardet库
            try:
                import chardet
                result = chardet.detect(raw_data)
                if result['confidence'] > 0.7:
                    return result['encoding']
            except ImportError:
                pass
            
            return None
            
        except Exception as e:
            self.logger.error(f"检测文件编码时出错: {file_path}, {e}")
            return None
    
    def _read_file_content(self, file_path: str, encoding: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            encoding: 文件编码
            
        Returns:
            str: 文件内容
        """
        path = Path(file_path)
        
        # 处理特殊文件类型
        if path.suffix.lower() in ['.xlsx', '.xls', '.xlsm']:
            return self._read_excel_file(file_path)
        elif path.suffix.lower() in ['.docx']:
            return self._read_docx_file(file_path)
        elif path.suffix.lower() in ['.pdf']:
            return self._read_pdf_file(file_path)
        else:
            # 普通文本文件
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
    
    def _read_excel_file(self, file_path: str) -> str:
        """读取Excel文件"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            return df.to_string(index=False)
        except ImportError:
            raise Exception("需要安装pandas库来读取Excel文件")
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def _read_docx_file(self, file_path: str) -> str:
        """读取Word文档"""
        try:
            import docx
            doc = docx.Document(file_path)
            return '\n'.join([paragraph.text for paragraph in doc.paragraphs])
        except ImportError:
            raise Exception("需要安装python-docx库来读取Word文档")
        except Exception as e:
            raise Exception(f"读取Word文档失败: {str(e)}")
    
    def _read_pdf_file(self, file_path: str) -> str:
        """读取PDF文件"""
        try:
            import PyPDF2
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text = ''
                for page in reader.pages:
                    text += page.extract_text()
                return text
        except ImportError:
            raise Exception("需要安装PyPDF2库来读取PDF文件")
        except Exception as e:
            raise Exception(f"读取PDF文件失败: {str(e)}")
    
    def _validate_document_integrity(self, content: str, file_extension: str) -> Tuple[bool, str]:
        """
        验证文档完整性
        
        Args:
            content: 文档内容
            file_extension: 文件扩展名
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 错误信息)
        """
        try:
            # 基本检查
            if not content.strip():
                return False, "文档内容为空"
            
            # 检查内容长度
            if len(content) < 10:
                return False, "文档内容过短"
            
            # 根据文件类型进行特定验证
            if file_extension.lower() in ['.json']:
                try:
                    import json
                    json.loads(content)
                except json.JSONDecodeError as e:
                    return False, f"JSON格式错误: {str(e)}"
            
            elif file_extension.lower() in ['.xml']:
                try:
                    import xml.etree.ElementTree as ET
                    ET.fromstring(content)
                except ET.ParseError as e:
                    return False, f"XML格式错误: {str(e)}"
            
            elif file_extension.lower() in ['.yaml', '.yml']:
                try:
                    import yaml
                    yaml.safe_load(content)
                except yaml.YAMLError as e:
                    return False, f"YAML格式错误: {str(e)}"
            
            # 检查是否包含过多的控制字符
            control_chars = sum(1 for c in content if ord(c) < 32 and c not in '\n\r\t')
            if control_chars > len(content) * 0.1:
                return False, "文档包含过多控制字符，可能是二进制文件"
            
            return True, "验证通过"
            
        except Exception as e:
            return False, f"验证过程中出错: {str(e)}"
    
    def _generate_loading_report(self, doc_infos: List[DocumentInfo]):
        """
        生成加载报告
        
        Args:
            doc_infos: 文档信息列表
        """
        total = len(doc_infos)
        completed = sum(1 for info in doc_infos if info.status == LoadingStatus.COMPLETED)
        failed = sum(1 for info in doc_infos if info.status == LoadingStatus.FAILED)
        skipped = sum(1 for info in doc_infos if info.status == LoadingStatus.SKIPPED)
        
        total_size = sum(info.size for info in doc_infos if info.status == LoadingStatus.COMPLETED)
        total_content_length = sum(info.content_length for info in doc_infos if info.status == LoadingStatus.COMPLETED)
        avg_load_time = sum(info.load_time for info in doc_infos) / total if total > 0 else 0
        
        self.logger.info("=== 文档加载报告 ===")
        self.logger.info(f"总文档数: {total}")
        self.logger.info(f"成功加载: {completed}")
        self.logger.info(f"加载失败: {failed}")
        self.logger.info(f"跳过文件: {skipped}")
        self.logger.info(f"总文件大小: {total_size / 1024 / 1024:.2f} MB")
        self.logger.info(f"总内容长度: {total_content_length:,} 字符")
        self.logger.info(f"平均加载时间: {avg_load_time:.3f} 秒")
        
        # 记录失败的文件
        if failed > 0:
            self.logger.warning("加载失败的文件:")
            for info in doc_infos:
                if info.status == LoadingStatus.FAILED:
                    self.logger.warning(f"  {info.path}: {info.error_message}")
        
        # 记录跳过的文件
        if skipped > 0:
            self.logger.info("跳过的文件:")
            for info in doc_infos:
                if info.status == LoadingStatus.SKIPPED:
                    self.logger.info(f"  {info.path}: {info.error_message}")


def create_document_loader(progress_callback: Optional[Callable] = None) -> DocumentLoader:
    """
    创建文档加载器实例
    
    Args:
        progress_callback: 进度回调函数
        
    Returns:
        DocumentLoader: 文档加载器实例
    """
    return DocumentLoader(progress_callback)
