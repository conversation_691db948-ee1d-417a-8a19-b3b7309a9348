
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能测试脚本
用于评估系统各个组件的性能
"""

import sys
import time
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import argparse
import json
from datetime import datetime
import psutil
import torch
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.utils import setup_logger, ConfigUtils, Timer
from src.data_loader import FileScanner, MDLoader
from src.preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from src.vectorizer import TextEmbedding, VectorTransformer
from src.indexer import IndexBuilder, VectorSearcher
from src.storage import VectorStore, MetadataManager

class Benchmark:
    """性能测试类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化性能测试器"""
        self.config = ConfigUtils.load_config(config_path)
        self.logger = setup_logger(self.config, 'benchmark')
        self.results = {}
        
        # 初始化组件
        self._init_components()
    
    def _init_components(self):
        """初始化测试组件"""
        self.cleaner = TextCleaner(self.config)
        self.normalizer = TextNormalizer(self.config)
        self.tokenizer = TextTokenizer(self.config)
        self.embedder = TextEmbedding(self.config)
        self.transformer = VectorTransformer(self.config)
        self.index_builder = IndexBuilder(self.config)
        self.searcher = VectorSearcher(self.config)
        self.vector_store = VectorStore(self.config)
        self.metadata_manager = MetadataManager(self.config)
    
    def _generate_test_data(self, size: int = 1000, length: int = 1000) -> List[str]:
        """生成测试数据"""
        texts = []
        for i in range(size):
            # 生成随机文本
            words = np.random.choice(
                ['python', 'machine', 'learning', 'vector', 'processing', 
                 'document', 'text', 'analysis', 'data', 'model'],
                size=length // 10
            )
            text = ' '.join(words)
            texts.append(text)
        return texts
    
    def _measure_memory(self) -> Dict[str, float]:
        """测量内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
        }
    
    def benchmark_preprocessing(self, texts: List[str]) -> Dict[str, Any]:
        """测试预处理性能"""
        results = {}
        
        # 文本清理性能
        with Timer() as t:
            cleaned_texts = [self.cleaner.clean_text(text) for text in tqdm(texts)]
        results['cleaning'] = {
            'time': t.elapsed,
            'throughput': len(texts) / t.elapsed
        }
        
        # 文本标准化性能
        with Timer() as t:
            normalized_texts = [self.normalizer.normalize_text(text) for text in tqdm(cleaned_texts)]
        results['normalization'] = {
            'time': t.elapsed,
            'throughput': len(texts) / t.elapsed
        }
        
        # 分词性能
        with Timer() as t:
            tokenized_texts = [self.tokenizer.tokenize_text(text) for text in tqdm(normalized_texts)]
        results['tokenization'] = {
            'time': t.elapsed,
            'throughput': len(texts) / t.elapsed
        }
        
        return results
    
    def benchmark_vectorization(self, texts: List[str], batch_sizes: List[int]) -> Dict[str, Any]:
        """测试向量化性能"""
        results = {}
        
        for batch_size in batch_sizes:
            with Timer() as t:
                vectors = []
                for i in tqdm(range(0, len(texts), batch_size)):
                    batch = texts[i:i + batch_size]
                    batch_vectors = self.embedder.encode_batch(batch)
                    vectors.append(batch_vectors)
            
            vectors = np.vstack(vectors)
            results[f'batch_size_{batch_size}'] = {
                'time': t.elapsed,
                'throughput': len(texts) / t.elapsed,
                'memory': self._measure_memory()
            }
        
        return results
    
    def benchmark_indexing(self, vectors: np.ndarray, batch_sizes: List[int]) -> Dict[str, Any]:
        """测试索引性能"""
        results = {}
        
        for batch_size in batch_sizes:
            # 创建新索引
            self.index_builder.create_index(vectors.shape[1])
            
            with Timer() as t:
                for i in tqdm(range(0, len(vectors), batch_size)):
                    batch_vectors = vectors[i:i + batch_size]
                    self.index_builder.add_vectors(batch_vectors)
            
            # 测试搜索性能
            query_vectors = vectors[:100]  # 使用前100个向量作为查询
            with Timer() as search_timer:
                for query in tqdm(query_vectors):
                    self.searcher.search(query, k=10)
            
            results[f'batch_size_{batch_size}'] = {
                'build_time': t.elapsed,
                'build_throughput': len(vectors) / t.elapsed,
                'search_time': search_timer.elapsed,
                'search_throughput': len(query_vectors) / search_timer.elapsed,
                'memory': self._measure_memory()
            }
        
        return results
    
    def benchmark_storage(self, vectors: np.ndarray, metadata: List[Dict[str, Any]],
                        batch_sizes: List[int]) -> Dict[str, Any]:
        """测试存储性能"""
        results = {}
        
        for batch_size in batch_sizes:
            # 向量存储性能
            with Timer() as t:
                for i in tqdm(range(0, len(vectors), batch_size)):
                    batch_vectors = vectors[i:i + batch_size]
                    self.vector_store.store_vectors(batch_vectors)
            
            results[f'vector_storage_batch_{batch_size}'] = {
                'time': t.elapsed,
                'throughput': len(vectors) / t.elapsed,
                'memory': self._measure_memory()
            }
            
            # 元数据存储性能
            with Timer() as t:
                batch_metadata = {}
                for i in tqdm(range(0, len(metadata), batch_size)):
                    batch = metadata[i:i + batch_size]
                    for j, meta in enumerate(batch):
                        batch_metadata[i + j] = meta
                    self.metadata_manager.batch_store_metadata(batch_metadata)
                    batch_metadata.clear()
            
            results[f'metadata_storage_batch_{batch_size}'] = {
                'time': t.elapsed,
                'throughput': len(metadata) / t.elapsed,
                'memory': self._measure_memory()
            }
        
        return results
    
    def run_benchmark(self, data_size: int = 1000, text_length: int = 1000,
                     batch_sizes: List[int] = None) -> Dict[str, Any]:
        """运行完整的性能测试"""
        if batch_sizes is None:
            batch_sizes = [1, 8, 32, 128, 512]
        
        self.logger.info(f"开始性能测试 - 数据量: {data_size}, 文本长度: {text_length}")
        
        # 生成测试数据
        self.logger.info("生成测试数据...")
        texts = self._generate_test_data(data_size, text_length)
        
        # 测试预处理性能
        self.logger.info("测试预处理性能...")
        self.results['preprocessing'] = self.benchmark_preprocessing(texts)
        
        # 测试向量化性能
        self.logger.info("测试向量化性能...")
        self.results['vectorization'] = self.benchmark_vectorization(texts, batch_sizes)
        
        # 生成向量数据
        vectors = self.embedder.encode_batch(texts)
        metadata = [{'text': text, 'timestamp': datetime.now().isoformat()} for text in texts]
        
        # 测试索引性能
        self.logger.info("测试索引性能...")
        self.results['indexing'] = self.benchmark_indexing(vectors, batch_sizes)
        
        # 测试存储性能
        self.logger.info("测试存储性能...")
        self.results['storage'] = self.benchmark_storage(vectors, metadata, batch_sizes)
        
        # 添加系统信息
        self.results['system_info'] = {
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
            'gpu_available': torch.cuda.is_available(),
            'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
            'timestamp': datetime.now().isoformat()
        }
        
        return self.results
    
    def save_results(self, output_path: str = "benchmark_results.json"):
        """保存测试结果"""
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.logger.info(f"结果已保存到: {output_path}")
    
    def print_summary(self):
        """打印测试结果摘要"""
        print("\n=== 性能测试结果摘要 ===\n")
        
        # 系统信息
        print("系统信息:")
        system_info = self.results['system_info']
        print(f"CPU核心数: {system_info['cpu_count']}")
        print(f"内存总量: {system_info['memory_total']:.2f} GB")
        print(f"GPU可用: {system_info['gpu_available']}")
        if system_info['gpu_name']:
            print(f"GPU型号: {system_info['gpu_name']}")
        print()
        
        # 预处理性能
        print("预处理性能:")
        preprocessing = self.results['preprocessing']
        for stage, metrics in preprocessing.items():
            print(f"{stage}:")
            print(f"  处理时间: {metrics['time']:.2f} 秒")
            print(f"  处理速率: {metrics['throughput']:.2f} 文档/秒")
        print()
        
        # 向量化性能
        print("向量化性能:")
        vectorization = self.results['vectorization']
        for batch_size, metrics in vectorization.items():
            print(f"批次大小 {batch_size}:")
            print(f"  处理时间: {metrics['time']:.2f} 秒")
            print(f"  处理速率: {metrics['throughput']:.2f} 文档/秒")
            print(f"  内存使用: {metrics['memory']['rss']:.2f} MB")
        print()
        
        # 索引性能
        print("索引性能:")
        indexing = self.results['indexing']
        for batch_size, metrics in indexing.items():
            print(f"批次大小 {batch_size}:")
            print(f"  构建时间: {metrics['build_time']:.2f} 秒")
            print(f"  构建速率: {metrics['build_throughput']:.2f} 向量/秒")
            print(f"  搜索时间: {metrics['search_time']:.2f} 秒")
            print(f"  搜索速率: {metrics['search_throughput']:.2f} 查询/秒")
        print()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor性能测试工具')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-size', type=int, default=1000,
                       help='测试数据量')
    parser.add_argument('--text-length', type=int, default=1000,
                       help='测试文本长度')
    parser.add_argument('--output', type=str, default='benchmark_results.json',
                       help='结果输出路径')
    args = parser.parse_args()
    
    # 运行测试
    benchmark = Benchmark(args.config)
    benchmark.run_benchmark(args.data_size, args.text_length)
    benchmark.save_results(args.output)
    benchmark.print_summary()

if __name__ == '__main__':
    main()