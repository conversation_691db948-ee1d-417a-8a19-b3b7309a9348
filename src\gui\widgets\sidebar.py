#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
侧边栏小部件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QLabel, QSpacerItem,
    QSizePolicy, QFrame
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon, QPixmap, QFont

try:
    import qtawesome as qta
except ImportError:
    qta = None

from ..i18n import Translator

class SidebarWidget(QWidget):
    """侧边栏小部件"""
    
    def __init__(self, translator: Translator):
        """
        初始化侧边栏小部件
        
        Args:
            translator: 翻译器实例
        """
        super().__init__()
        
        self.translator = translator
        self.translator.add_observer(self)
        
        # 设置对象名，用于样式表
        self.setObjectName("sidebarWidget")
        
        # 设置最小宽度
        self.setMinimumWidth(200)
        self.setMaximumWidth(300)
        
        # 初始化UI
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI组件"""
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(10)
        
        # 创建标题标签
        title_label = QLabel("MD Vector")
        title_label.setObjectName("sidebarTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 创建按钮
        self.dashboard_button = self._create_sidebar_button("dashboard", "仪表盘")
        self.index_button = self._create_sidebar_button("index", "索引")
        self.vectorize_button = self._create_sidebar_button("vectorize", "向量化")
        self.search_button = self._create_sidebar_button("search", "搜索")
        self.visualize_button = self._create_sidebar_button("visualize", "可视化")
        
        # 添加按钮到布局
        layout.addWidget(self.dashboard_button)
        layout.addWidget(self.index_button)
        layout.addWidget(self.vectorize_button)
        layout.addWidget(self.search_button)
        layout.addWidget(self.visualize_button)
        
        # 添加弹性空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        # 添加设置和关于按钮
        self.settings_button = self._create_sidebar_button("settings", "设置")
        self.about_button = self._create_sidebar_button("about", "关于")
        
        layout.addWidget(self.settings_button)
        layout.addWidget(self.about_button)
    
    def _create_sidebar_button(self, icon_name: str, text: str) -> QPushButton:
        """
        创建侧边栏按钮
        
        Args:
            icon_name: 图标名称
            text: 按钮文本
        
        Returns:
            QPushButton: 创建的按钮
        """
        button = QPushButton(self.translator.get_text(icon_name, text))
        button.setObjectName(f"{icon_name}Button")
        button.setMinimumHeight(40)
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 设置图标（如果qtawesome可用）
        if qta:
            icon_map = {
                "dashboard": "fa5s.tachometer-alt",
                "index": "fa5s.database",
                "vectorize": "fa5s.vector-square",
                "search": "fa5s.search",
                "visualize": "fa5s.chart-bar",
                "settings": "fa5s.cog",
                "about": "fa5s.info-circle"
            }
            
            if icon_name in icon_map:
                icon = qta.icon(icon_map[icon_name], color="white")
                button.setIcon(icon)
                button.setIconSize(QSize(20, 20))
        
        return button
    
    def on_language_changed(self):
        """语言变更回调"""
        # 更新按钮文本
        self.dashboard_button.setText(self.translator.get_text("dashboard", "仪表盘"))
        self.index_button.setText(self.translator.get_text("index", "索引"))
        self.vectorize_button.setText(self.translator.get_text("vectorize", "向量化"))
        self.search_button.setText(self.translator.get_text("search", "搜索"))
        self.visualize_button.setText(self.translator.get_text("visualize", "可视化"))
        self.settings_button.setText(self.translator.get_text("settings", "设置"))
        self.about_button.setText(self.translator.get_text("about", "关于"))
