#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
可视化小部件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QFormLayout, QGroupBox, QSpinBox, QCheckBox,
    QSlider, QColorDialog, QSizePolicy
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor

try:
    import matplotlib
    matplotlib.use('QtAgg')  # 使用QtAgg后端，适用于PyQt6
    from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

from ..i18n import Translator

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib画布"""

    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """
        初始化Matplotlib画布

        Args:
            parent: 父部件
            width: 宽度（英寸）
            height: 高度（英寸）
            dpi: 分辨率
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)

        super().__init__(self.fig)
        self.setParent(parent)

        # 设置画布属性
        FigureCanvas.setSizePolicy(self,
                                  QSizePolicy.Policy.Expanding,
                                  QSizePolicy.Policy.Expanding)
        FigureCanvas.updateGeometry(self)

    def plot_scatter(self, x, y, labels=None, colors=None):
        """
        绘制散点图

        Args:
            x: x坐标数组
            y: y坐标数组
            labels: 点标签
            colors: 点颜色
        """
        self.axes.clear()
        scatter = self.axes.scatter(x, y, c=colors)

        if labels is not None:
            for i, label in enumerate(labels):
                self.axes.annotate(label, (x[i], y[i]))

        self.fig.tight_layout()
        self.draw()

    def plot_3d(self, x, y, z, labels=None, colors=None):
        """
        绘制3D散点图

        Args:
            x: x坐标数组
            y: y坐标数组
            z: z坐标数组
            labels: 点标签
            colors: 点颜色
        """
        self.axes.clear()
        self.axes = self.fig.add_subplot(111, projection='3d')
        scatter = self.axes.scatter(x, y, z, c=colors)

        if labels is not None:
            for i, label in enumerate(labels):
                self.axes.text(x[i], y[i], z[i], label)

        self.fig.tight_layout()
        self.draw()

class VisualizeWidget(QWidget):
    """可视化小部件"""

    def __init__(self, translator: Translator):
        """
        初始化可视化小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("visualizeWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(self.translator.get_text("vector_visualization", "向量可视化"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 创建控制面板和可视化区域的水平布局
        content_layout = QHBoxLayout()

        # 创建控制面板
        control_panel = QWidget()
        control_panel.setObjectName("controlPanel")
        control_panel.setMaximumWidth(300)
        control_layout = QVBoxLayout(control_panel)

        # 创建数据源选择组
        data_group = QGroupBox(self.translator.get_text("data_source", "数据源"))
        data_layout = QFormLayout(data_group)

        self.index_combo = QComboBox()
        # 索引列表将在refresh_indices方法中更新

        self.max_points_spin = QSpinBox()
        self.max_points_spin.setRange(10, 10000)
        self.max_points_spin.setValue(1000)
        self.max_points_spin.setSingleStep(100)

        data_layout.addRow(self.translator.get_text("select_index", "选择索引:"), self.index_combo)
        data_layout.addRow(self.translator.get_text("max_points", "最大点数:"), self.max_points_spin)

        control_layout.addWidget(data_group)

        # 创建可视化设置组
        vis_group = QGroupBox(self.translator.get_text("visualization_settings", "可视化设置"))
        vis_layout = QFormLayout(vis_group)

        self.method_combo = QComboBox()
        self.method_combo.addItems(["PCA", "t-SNE", "UMAP"])

        self.dimensions_combo = QComboBox()
        self.dimensions_combo.addItems(["2D", "3D"])

        self.perplexity_spin = QSpinBox()
        self.perplexity_spin.setRange(5, 100)
        self.perplexity_spin.setValue(30)

        self.iterations_spin = QSpinBox()
        self.iterations_spin.setRange(100, 10000)
        self.iterations_spin.setValue(1000)
        self.iterations_spin.setSingleStep(100)

        self.show_labels_check = QCheckBox(self.translator.get_text("show_labels", "显示标签"))

        vis_layout.addRow(self.translator.get_text("method", "方法:"), self.method_combo)
        vis_layout.addRow(self.translator.get_text("dimensions", "维度:"), self.dimensions_combo)
        vis_layout.addRow(self.translator.get_text("perplexity", "困惑度:"), self.perplexity_spin)
        vis_layout.addRow(self.translator.get_text("iterations", "迭代次数:"), self.iterations_spin)
        vis_layout.addRow("", self.show_labels_check)

        control_layout.addWidget(vis_group)

        # 创建颜色设置组
        color_group = QGroupBox(self.translator.get_text("color_settings", "颜色设置"))
        color_layout = QFormLayout(color_group)

        self.color_by_combo = QComboBox()
        self.color_by_combo.addItems([
            self.translator.get_text("none", "无"),
            self.translator.get_text("cluster", "聚类"),
            self.translator.get_text("category", "类别"),
            self.translator.get_text("custom", "自定义")
        ])

        self.color_map_combo = QComboBox()
        self.color_map_combo.addItems([
            "viridis", "plasma", "inferno", "magma", "cividis",
            "Spectral", "coolwarm", "bwr", "seismic", "twilight"
        ])

        self.point_size_spin = QSpinBox()
        self.point_size_spin.setRange(1, 100)
        self.point_size_spin.setValue(20)

        color_layout.addRow(self.translator.get_text("color_by", "着色依据:"), self.color_by_combo)
        color_layout.addRow(self.translator.get_text("color_map", "色彩映射:"), self.color_map_combo)
        color_layout.addRow(self.translator.get_text("point_size", "点大小:"), self.point_size_spin)

        control_layout.addWidget(color_group)

        # 添加按钮
        buttons_layout = QHBoxLayout()

        self.reset_button = QPushButton(self.translator.get_text("reset", "重置"))
        self.reset_button.setObjectName("secondaryButton")
        self.reset_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.visualize_button = QPushButton(self.translator.get_text("visualize", "可视化"))
        self.visualize_button.setObjectName("primaryButton")
        self.visualize_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.visualize_button)

        control_layout.addLayout(buttons_layout)

        # 添加弹性空间
        control_layout.addStretch()

        # 创建可视化区域
        vis_area = QWidget()
        vis_area.setObjectName("visualizationArea")
        vis_layout = QVBoxLayout(vis_area)

        if HAS_MATPLOTLIB:
            self.canvas = MatplotlibCanvas(self, width=5, height=4, dpi=100)
            vis_layout.addWidget(self.canvas)
        else:
            no_matplotlib_label = QLabel(self.translator.get_text(
                "matplotlib_not_found",
                "未找到Matplotlib库，无法显示可视化。请安装Matplotlib: pip install matplotlib"
            ))
            no_matplotlib_label.setWordWrap(True)
            no_matplotlib_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            vis_layout.addWidget(no_matplotlib_label)

        # 添加控制面板和可视化区域到内容布局
        content_layout.addWidget(control_panel)
        content_layout.addWidget(vis_area, 1)  # 可视化区域占据更多空间

        main_layout.addLayout(content_layout)

        # 连接信号
        self._connect_signals()

        # 如果有Matplotlib，显示一些示例数据
        if HAS_MATPLOTLIB:
            self._plot_example_data()

    def _connect_signals(self):
        """连接信号和槽"""
        self.reset_button.clicked.connect(self._on_reset)
        self.visualize_button.clicked.connect(self._on_visualize)
        self.method_combo.currentIndexChanged.connect(self._on_method_changed)
        self.dimensions_combo.currentIndexChanged.connect(self._on_dimensions_changed)

        # 初始化时刷新索引列表
        self.refresh_indices()

    def refresh_indices(self):
        """刷新索引列表"""
        try:
            # 导入必要的模块
            from pathlib import Path
            import pickle

            # 保存当前选择的索引
            current_index = self.index_combo.currentText()

            # 清空索引列表
            self.index_combo.clear()

            # 获取索引目录
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                return

            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))

            # 添加索引到下拉框
            for index_file in index_files:
                self.index_combo.addItem(index_file.stem)

            # 恢复之前选择的索引
            if current_index:
                index = self.index_combo.findText(current_index)
                if index >= 0:
                    self.index_combo.setCurrentIndex(index)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"刷新索引列表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_reset(self):
        """重置按钮点击处理"""
        self.method_combo.setCurrentIndex(0)
        self.dimensions_combo.setCurrentIndex(0)
        self.perplexity_spin.setValue(30)
        self.iterations_spin.setValue(1000)
        self.show_labels_check.setChecked(False)
        self.color_by_combo.setCurrentIndex(0)
        self.color_map_combo.setCurrentIndex(0)
        self.point_size_spin.setValue(20)

    def _on_visualize(self):
        """可视化按钮点击处理"""
        if HAS_MATPLOTLIB:
            self._plot_example_data()

    def _on_method_changed(self, index):
        """方法变更处理"""
        # 启用/禁用相关控件
        is_tsne = index == 1  # t-SNE
        self.perplexity_spin.setEnabled(is_tsne)

    def _on_dimensions_changed(self, index):
        """维度变更处理"""
        # 更新示例数据
        if HAS_MATPLOTLIB:
            self._plot_example_data()

    def _plot_example_data(self):
        """绘制示例数据"""
        if not HAS_MATPLOTLIB:
            return

        # 生成示例数据
        np.random.seed(42)
        n_samples = 100

        # 根据选择的维度生成数据
        is_3d = self.dimensions_combo.currentText() == "3D"

        if is_3d:
            # 3D数据
            x = np.random.randn(n_samples)
            y = np.random.randn(n_samples)
            z = np.random.randn(n_samples)

            # 生成聚类颜色
            colors = np.zeros(n_samples)
            for i in range(n_samples):
                if x[i]**2 + y[i]**2 + z[i]**2 < 1:
                    colors[i] = 0
                elif x[i] > 0 and y[i] > 0 and z[i] > 0:
                    colors[i] = 1
                else:
                    colors[i] = 2

            # 绘制3D散点图
            self.canvas.plot_3d(x, y, z, colors=colors)
        else:
            # 2D数据
            x = np.random.randn(n_samples)
            y = np.random.randn(n_samples)

            # 生成聚类颜色
            colors = np.zeros(n_samples)
            for i in range(n_samples):
                if x[i]**2 + y[i]**2 < 1:
                    colors[i] = 0
                elif x[i] > 0 and y[i] > 0:
                    colors[i] = 1
                else:
                    colors[i] = 2

            # 绘制2D散点图
            self.canvas.plot_scatter(x, y, colors=colors)

    def on_language_changed(self):
        """语言变更回调"""
        # 更新标题
        self.findChild(QLabel, "titleLabel").setText(
            self.translator.get_text("vector_visualization", "向量可视化")
        )

        # 更新数据源组
        data_group = self.findChildren(QGroupBox)[0]
        if data_group:
            data_group.setTitle(self.translator.get_text("data_source", "数据源"))

            data_layout = data_group.layout()
            if data_layout:
                data_layout.itemAt(0, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("select_index", "选择索引:"))
                data_layout.itemAt(1, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("max_points", "最大点数:"))

        # 更新可视化设置组
        vis_group = self.findChildren(QGroupBox)[1]
        if vis_group:
            vis_group.setTitle(self.translator.get_text("visualization_settings", "可视化设置"))

            vis_layout = vis_group.layout()
            if vis_layout:
                vis_layout.itemAt(0, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("method", "方法:"))
                vis_layout.itemAt(1, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("dimensions", "维度:"))
                vis_layout.itemAt(2, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("perplexity", "困惑度:"))
                vis_layout.itemAt(3, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("iterations", "迭代次数:"))

                self.show_labels_check.setText(self.translator.get_text("show_labels", "显示标签"))

        # 更新颜色设置组
        color_group = self.findChildren(QGroupBox)[2]
        if color_group:
            color_group.setTitle(self.translator.get_text("color_settings", "颜色设置"))

            color_layout = color_group.layout()
            if color_layout:
                color_layout.itemAt(0, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("color_by", "着色依据:"))
                color_layout.itemAt(1, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("color_map", "色彩映射:"))
                color_layout.itemAt(2, QFormLayout.ItemRole.LabelRole).widget().setText(
                    self.translator.get_text("point_size", "点大小:"))

        # 更新按钮
        self.reset_button.setText(self.translator.get_text("reset", "重置"))
        self.visualize_button.setText(self.translator.get_text("visualize", "可视化"))

        # 更新颜色依据下拉框
        self.color_by_combo.clear()
        self.color_by_combo.addItems([
            self.translator.get_text("none", "无"),
            self.translator.get_text("cluster", "聚类"),
            self.translator.get_text("category", "类别"),
            self.translator.get_text("custom", "自定义")
        ])
