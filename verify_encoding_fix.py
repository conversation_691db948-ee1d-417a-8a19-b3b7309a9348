#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证编码修复结果
"""

import json
from pathlib import Path

def verify_encoding_fix():
    """验证编码修复结果"""
    
    print("🔍 验证编码修复结果")
    print("=" * 80)
    
    # 测试文件列表
    test_files = [
        "training_data/raw_documents/enterprise_standards/metadata/ES_XS71-14A121-AA_EN_1998-09_线束总成制造标准_metadata.json",
        "training_data/raw_documents/enterprise_standards/metadata/A 009 000 04 99_CH_2013-07_奔驰线束焊接测试标准_metadata.json",
        "training_data/raw_documents/enterprise_standards/metadata/A 002 006 32 99_CH_2014-03_ZGS004_梅赛德斯奔驰汽车线束图纸要求_metadata.json"
    ]
    
    for file_path_str in test_files:
        file_path = Path(file_path_str)
        
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path.name}")
            continue
        
        print(f"\n📄 验证文件: {file_path.name}")
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析JSON
            metadata = json.loads(content)
            
            # 检查关键字段
            title = metadata.get('title', '')
            source = metadata.get('source', '')
            keywords = metadata.get('keywords', [])
            file_path_field = metadata.get('file_path', '')
            
            print(f"  📝 标题: {title}")
            print(f"  📂 来源: {source}")
            print(f"  🏷️ 关键词: {keywords}")
            print(f"  📁 文件路径: {file_path_field[:80]}...")
            
            # 检查是否还有乱码
            has_garbled = False
            garbled_chars = ['绾', '濂', '鐢', '鍒', '鎬', '鐒', '娴', '鏍', '姹', '鍥', '瑕']
            
            for field_name, field_value in [('title', title), ('source', source), ('file_path', file_path_field)]:
                if any(char in str(field_value) for char in garbled_chars):
                    print(f"  ⚠️ {field_name} 仍有乱码: {field_value}")
                    has_garbled = True
            
            for keyword in keywords:
                if any(char in str(keyword) for char in garbled_chars):
                    print(f"  ⚠️ 关键词仍有乱码: {keyword}")
                    has_garbled = True
            
            if not has_garbled:
                print(f"  ✅ 编码正常")
            else:
                print(f"  ❌ 仍有编码问题")
                
        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
    
    # 统计所有元数据文件的编码状态
    print(f"\n📊 整体编码状态统计")
    print("-" * 60)
    
    metadata_dir = Path("training_data/raw_documents/enterprise_standards/metadata")
    if metadata_dir.exists():
        metadata_files = list(metadata_dir.glob("*_metadata.json"))
        
        total_files = len(metadata_files)
        clean_files = 0
        garbled_files = 0
        error_files = 0
        
        garbled_chars = ['绾', '濂', '鐢', '鍒', '鎬', '鐒', '娴', '鏍', '姹', '鍥', '瑕']
        
        for file_path in metadata_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有乱码
                if any(char in content for char in garbled_chars):
                    garbled_files += 1
                else:
                    clean_files += 1
                    
            except Exception:
                error_files += 1
        
        print(f"  总文件数: {total_files}")
        print(f"  编码正常: {clean_files} ({clean_files/total_files*100:.1f}%)")
        print(f"  仍有乱码: {garbled_files} ({garbled_files/total_files*100:.1f}%)")
        print(f"  读取错误: {error_files} ({error_files/total_files*100:.1f}%)")
        
        if garbled_files == 0:
            print(f"  🎉 所有文件编码都正常！")
        else:
            print(f"  ⚠️ 还有 {garbled_files} 个文件需要修复")
    
    print(f"\n💡 注意事项:")
    print(f"  - 如果PowerShell显示乱码但Python读取正常，这是PowerShell编码设置问题")
    print(f"  - 程序内部处理和向量化时会使用正确的UTF-8编码")
    print(f"  - 可以通过Python脚本验证实际的文件内容")

if __name__ == "__main__":
    verify_encoding_fix()
