{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"title": "系统概览", "type": "row", "panels": [{"title": "API请求总数", "type": "stat", "datasource": "Prometheus", "targets": [{"expr": "sum(http_requests_total)", "legendFormat": "Total Requests"}]}, {"title": "平均响应时间", "type": "gauge", "datasource": "Prometheus", "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "legendFormat": "Avg Response Time"}]}]}, {"title": "性能指标", "type": "row", "panels": [{"title": "CPU使用率", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %"}]}, {"title": "内存使用率", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100", "legendFormat": "Memory Usage %"}]}]}, {"title": "向量处理", "type": "row", "panels": [{"title": "向量处理速率", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(vector_processing_total[5m])", "legendFormat": "Vectors/sec"}]}, {"title": "向量存储大小", "type": "gauge", "datasource": "Prometheus", "targets": [{"expr": "vector_storage_size_bytes", "legendFormat": "Storage Size"}]}]}, {"title": "搜索性能", "type": "row", "panels": [{"title": "搜索延迟", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(search_duration_seconds_sum[5m]) / rate(search_duration_seconds_count[5m])", "legendFormat": "Avg Search Time"}]}, {"title": "搜索命中率", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "rate(search_hits_total[5m]) / rate(search_requests_total[5m]) * 100", "legendFormat": "Hit Rate %"}]}]}, {"title": "存储状态", "type": "row", "panels": [{"title": "磁盘使用率", "type": "gauge", "datasource": "Prometheus", "targets": [{"expr": "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\"} * 100) / node_filesystem_size_bytes{mountpoint=\"/\"})", "legendFormat": "Disk Usage %"}]}, {"title": "数据库连接数", "type": "graph", "datasource": "Prometheus", "targets": [{"expr": "pg_stat_activity_count", "legendFormat": "Active Connections"}]}]}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": ["md_vector_processor"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "MD Vector Processor Dashboard", "uid": "md_vector_processor", "version": 1}