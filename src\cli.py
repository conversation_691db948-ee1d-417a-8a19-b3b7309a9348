
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
命令行接口模块
提供命令行工具功能
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional, List

from .utils import setup_logger, ConfigUtils, Timer
from .data_loader import FileScanner, MDLoader
from .preprocessor import TextCleaner, TextNormalizer, TextTokenizer
from .vectorizer import TextEmbedding, VectorTransformer
from .indexer import IndexBuilder, VectorSearcher
from .storage import VectorStore, MetadataManager

class CLI:
    """命令行接口类"""
    
    def __init__(self):
        """初始化CLI"""
        self.logger = logging.getLogger(__name__)
        self.config = None
        self.setup_argparse()
    
    def setup_argparse(self):
        """设置命令行参数"""
        self.parser = argparse.ArgumentParser(
            description='MD Vector Processor - 文档向量化处理工具',
            formatter_class=argparse.RawDescriptionHelpFormatter
        )
        
        # 全局选项
        self.parser.add_argument(
            '--config',
            type=str,
            default='config/config.yaml',
            help='配置文件路径'
        )
        self.parser.add_argument(
            '--debug',
            action='store_true',
            help='启用调试模式'
        )
        self.parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细输出'
        )
        
        # 子命令
        subparsers = self.parser.add_subparsers(dest='command', help='可用命令')
        
        # 处理命令
        process_parser = subparsers.add_parser('process', help='处理文档')
        process_parser.add_argument(
            'input',
            type=str,
            help='输入文件或目录路径'
        )
        process_parser.add_argument(
            '--output',
            type=str,
            help='输出目录路径'
        )
        process_parser.add_argument(
            '--batch-size',
            type=int,
            default=32,
            help='批处理大小'
        )
        
        # 搜索命令
        search_parser = subparsers.add_parser('search', help='搜索文档')
        search_parser.add_argument(
            'query',
            type=str,
            help='搜索查询文本'
        )
        search_parser.add_argument(
            '--index',
            type=str,
            required=True,
            help='索引文件路径'
        )
        search_parser.add_argument(
            '--top-k',
            type=int,
            default=5,
            help='返回结果数量'
        )
        
        # 索引命令
        index_parser = subparsers.add_parser('index', help='管理索引')
        index_parser.add_argument(
            '--build',
            action='store_true',
            help='构建新索引'
        )
        index_parser.add_argument(
            '--update',
            action='store_true',
            help='更新现有索引'
        )
        index_parser.add_argument(
            '--optimize',
            action='store_true',
            help='优化索引'
        )
        
        # 导出命令
        export_parser = subparsers.add_parser('export', help='导出数据')
        export_parser.add_argument(
            '--format',
            choices=['json', 'csv', 'pickle'],
            default='json',
            help='导出格式'
        )
        export_parser.add_argument(
            '--output',
            type=str,
            required=True,
            help='输出文件路径'
        )
    
    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            self.config = ConfigUtils.load_config(config_path)
            if not self.config:
                raise ValueError(f"无法加载配置文件: {config_path}")
            return True
        except Exception as e:
            self.logger.error(f"加载配置文件时出错: {e}")
            return False
    
    def process_documents(self, input_path: str, output_path: Optional[str] = None,
                        batch_size: int = 32) -> bool:
        """处理文档"""
        try:
            with Timer("文档处理"):
                # 初始化组件
                scanner = FileScanner(self.config)
                loader = MDLoader(self.config)
                cleaner = TextCleaner(self.config)
                normalizer = TextNormalizer(self.config)
                tokenizer = TextTokenizer(self.config)
                embedder = TextEmbedding(self.config)
                vector_store = VectorStore(self.config)
                metadata_manager = MetadataManager(self.config)
                
                # 扫描文件
                self.logger.info(f"扫描目录: {input_path}")
                total_files = scanner.get_file_count()
                self.logger.info(f"找到 {total_files} 个文件")
                
                # 处理文件
                for batch in scanner.scan_files():
                    for metadata in batch:
                        try:
                            # 加载和处理文本
                            content = loader.load_file(metadata)
                            if not content:
                                continue
                            
                            cleaned_text = cleaner.clean_text(content.text_content)
                            normalized_text = normalizer.normalize_text(cleaned_text)
                            tokens = tokenizer.tokenize_text(normalized_text)
                            
                            # 生成向量
                            vector = embedder.encode_text(' '.join(tokens))
                            
                            # 存储数据
                            vector_store.store_vectors(vector.reshape(1, -1))
                            metadata_manager.store_metadata(metadata.path.stem, {
                                'filename': metadata.filename,
                                'tokens': tokens,
                                'sections': content.sections,
                                'links': content.links
                            })
                            
                            self.logger.info(f"处理文件: {metadata.filename}")
                            
                        except Exception as e:
                            self.logger.error(f"处理文件 {metadata.filename} 时出错: {e}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"处理文档时出错: {e}")
            return False
    
    def search_documents(self, query: str, index_path: str, top_k: int = 5) -> bool:
        """搜索文档"""
        try:
            with Timer("文档搜索"):
                # 初始化组件
                embedder = TextEmbedding(self.config)
                searcher = VectorSearcher(self.config)
                metadata_manager = MetadataManager(self.config)
                
                # 加载索引
                self.logger.info(f"加载索引: {index_path}")
                index_builder = IndexBuilder(self.config)
                if not index_builder.load_index(Path(index_path)):
                    raise ValueError("无法加载索引")
                
                # 设置搜索器
                searcher.set_index(index_builder.index)
                
                # 生成查询向量
                query_vector = embedder.encode_text(query)
                
                # 执行搜索
                results = searcher.search(query_vector, k=top_k)
                
                # 显示结果
                print(f"\n查询: {query}")
                print("\n搜索结果:")
                for i, result in enumerate(results, 1):
                    metadata = metadata_manager.get_metadata(result.id)
                    print(f"\n{i}. 文档: {metadata['filename']}")
                    print(f"   相似度: {result.score:.4f}")
                    if 'sections' in metadata:
                        print(f"   章节数: {len(metadata['sections'])}")
                
                return True
                
        except Exception as e:
            self.logger.error(f"搜索文档时出错: {e}")
            return False
    
    def manage_index(self, build: bool = False, update: bool = False,
                    optimize: bool = False) -> bool:
        """管理索引"""
        try:
            with Timer("索引管理"):
                index_builder = IndexBuilder(self.config)
                
                if build:
                    self.logger.info("构建新索引...")
                    # 实现索引构建逻辑
                
                if update:
                    self.logger.info("更新索引...")
                    # 实现索引更新逻辑
                
                if optimize:
                    self.logger.info("优化索引...")
                    index_builder.optimize_index()
                
                return True
                
        except Exception as e:
            self.logger.error(f"管理索引时出错: {e}")
            return False
    
    def export_data(self, format: str, output_path: str) -> bool:
        """导出数据"""
        try:
            with Timer("数据导出"):
                self.logger.info(f"导出数据为 {format} 格式...")
                # 实现数据导出逻辑
                return True
                
        except Exception as e:
            self.logger.error(f"导出数据时出错: {e}")
            return False
    
    def run(self, args: Optional[List[str]] = None) -> int:
        """运行CLI"""
        args = self.parser.parse_args(args)
        
        # 加载配置
        if not self.load_config(args.config):
            return 1
        
        # 设置日志
        setup_logger(self.config, 'cli')
        
        try:
            # 执行命令
            if args.command == 'process':
                success = self.process_documents(
                    args.input,
                    args.output,
                    args.batch_size
                )
            elif args.command == 'search':
                success = self.search_documents(
                    args.query,
                    args.index,
                    args.top_k
                )
            elif args.command == 'index':
                success = self.manage_index(
                    args.build,
                    args.update,
                    args.optimize
                )
            elif args.command == 'export':
                success = self.export_data(
                    args.format,
                    args.output
                )
            else:
                self.parser.print_help()
                return 0
            
            return 0 if success else 1
            
        except Exception as e:
            self.logger.error(f"执行命令时出错: {e}")
            return 1

def main():
    """主函数"""
    cli = CLI()
    sys.exit(cli.run())

if __name__ == '__main__':
    main()