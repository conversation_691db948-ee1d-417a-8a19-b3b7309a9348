{"models": [{"name": "ollama_llama2", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "llama2", "description": "Llama 2 模型 (<PERSON><PERSON><PERSON>)", "parameters": {}, "is_available": false, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_mistral", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "mistral", "description": "Mistral 模型 (<PERSON><PERSON><PERSON>)", "parameters": {}, "is_available": false, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_nomic_embed", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "nomic-embed-text", "description": "Nomic Embed 向量化模型 (Ollama)", "parameters": {}, "is_available": false, "vector_dimension": 768, "supports_embedding": true, "supports_chat": false}, {"name": "ollama_deepseek-coder-v2_latest", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "deepseek-coder-v2:latest", "description": "自动检测的Ollama模型: deepseek-coder-v2:latest", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_qwen2.5-coder_32b", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "qwen2.5-coder:32b", "description": "自动检测的Ollama模型: qwen2.5-coder:32b", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_llamafamily/llama3-chinese-8b-instruct_latest", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "llamafamily/llama3-chinese-8b-instruct:latest", "description": "自动检测的Ollama模型: llamafamily/llama3-chinese-8b-instruct:latest", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_gemma3_27b", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "gemma3:27b", "description": "自动检测的Ollama模型: gemma3:27b", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_qwen3_30b-a3b", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "qwen3:30b-a3b", "description": "自动检测的Ollama模型: qwen3:30b-a3b", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_ModelFileofMy_latest", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "ModelFileofMy:latest", "description": "自动检测的Ollama模型: ModelFileofMy:latest", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_all-minilm_33m", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "all-minilm:33m", "description": "自动检测的Ollama模型: all-minilm:33m", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_nomic-embed-text_latest", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "nomic-embed-text:latest", "description": "自动检测的Ollama模型: nomic-embed-text:latest", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}, {"name": "ollama_deepseek-r1_32b", "type": "ollama", "endpoint": "http://localhost:11434", "model_id": "deepseek-r1:32b", "description": "自动检测的Ollama模型: deepseek-r1:32b", "parameters": {}, "is_available": true, "vector_dimension": 384, "supports_embedding": true, "supports_chat": true}]}