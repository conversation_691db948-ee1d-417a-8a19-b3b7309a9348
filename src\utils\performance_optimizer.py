#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能优化工具

提供向量化和搜索性能优化功能
"""

import os
import time
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self, config: Dict):
        """
        初始化性能优化器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 性能配置
        self.batch_size = config.get('performance', {}).get('batch_size', 32)
        self.max_workers = config.get('performance', {}).get('max_workers', 4)
        self.cache_size = config.get('performance', {}).get('cache_size', 1000)
        self.enable_gpu = config.get('performance', {}).get('enable_gpu', True)
        
        # 缓存
        self.vector_cache = {}
        self.metadata_cache = {}
        
    def optimize_vectorization(self, texts: List[str], embedder) -> Tuple[np.ndarray, float]:
        """
        优化向量化过程
        
        Args:
            texts: 文本列表
            embedder: 嵌入器对象
            
        Returns:
            Tuple[np.ndarray, float]: (向量数组, 处理时间)
        """
        start_time = time.time()
        
        try:
            # 检查缓存
            cached_vectors = []
            uncached_texts = []
            uncached_indices = []
            
            for i, text in enumerate(texts):
                text_hash = hash(text)
                if text_hash in self.vector_cache:
                    cached_vectors.append((i, self.vector_cache[text_hash]))
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(i)
            
            self.logger.info(f"缓存命中: {len(cached_vectors)}/{len(texts)}")
            
            # 批量处理未缓存的文本
            new_vectors = []
            if uncached_texts:
                if len(uncached_texts) <= self.batch_size:
                    # 单批处理
                    batch_vectors = self._vectorize_batch(uncached_texts, embedder)
                    new_vectors.extend(batch_vectors)
                else:
                    # 多批并行处理
                    new_vectors = self._vectorize_parallel_batches(uncached_texts, embedder)
                
                # 更新缓存
                for i, text in enumerate(uncached_texts):
                    text_hash = hash(text)
                    self.vector_cache[text_hash] = new_vectors[i]
                    
                    # 限制缓存大小
                    if len(self.vector_cache) > self.cache_size:
                        # 删除最旧的缓存项
                        oldest_key = next(iter(self.vector_cache))
                        del self.vector_cache[oldest_key]
            
            # 合并结果
            all_vectors = [None] * len(texts)
            
            # 填入缓存的向量
            for i, vector in cached_vectors:
                all_vectors[i] = vector
            
            # 填入新计算的向量
            for i, vector in zip(uncached_indices, new_vectors):
                all_vectors[i] = vector
            
            # 转换为numpy数组
            result_vectors = np.vstack(all_vectors)
            
            processing_time = time.time() - start_time
            self.logger.info(f"向量化完成，耗时: {processing_time:.3f}秒")
            
            return result_vectors, processing_time
            
        except Exception as e:
            self.logger.error(f"向量化优化时出错: {e}")
            raise
    
    def _vectorize_batch(self, texts: List[str], embedder) -> List[np.ndarray]:
        """批量向量化"""
        try:
            # 使用嵌入器的批量处理功能
            if hasattr(embedder, 'encode_batch'):
                return embedder.encode_batch(texts)
            else:
                # 逐个处理
                vectors = []
                for text in texts:
                    vector = embedder.encode_text(text)
                    vectors.append(vector)
                return vectors
        except Exception as e:
            self.logger.error(f"批量向量化时出错: {e}")
            raise
    
    def _vectorize_parallel_batches(self, texts: List[str], embedder) -> List[np.ndarray]:
        """并行批量向量化"""
        try:
            # 分割为批次
            batches = [texts[i:i + self.batch_size] for i in range(0, len(texts), self.batch_size)]
            
            all_vectors = []
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交批次任务
                future_to_batch = {
                    executor.submit(self._vectorize_batch, batch, embedder): i 
                    for i, batch in enumerate(batches)
                }
                
                # 收集结果
                batch_results = [None] * len(batches)
                for future in as_completed(future_to_batch):
                    batch_index = future_to_batch[future]
                    try:
                        batch_vectors = future.result()
                        batch_results[batch_index] = batch_vectors
                    except Exception as e:
                        self.logger.error(f"批次 {batch_index} 处理失败: {e}")
                        raise
                
                # 合并结果
                for batch_vectors in batch_results:
                    if batch_vectors:
                        all_vectors.extend(batch_vectors)
            
            return all_vectors
            
        except Exception as e:
            self.logger.error(f"并行向量化时出错: {e}")
            raise
    
    def optimize_search(self, query_vector: np.ndarray, index_builder, k: int = 10) -> Tuple[np.ndarray, np.ndarray, float]:
        """
        优化搜索过程
        
        Args:
            query_vector: 查询向量
            index_builder: 索引构建器
            k: 返回结果数量
            
        Returns:
            Tuple[np.ndarray, np.ndarray, float]: (距离, 索引, 搜索时间)
        """
        start_time = time.time()
        
        try:
            # 预处理查询向量
            if query_vector.ndim == 1:
                query_vector = query_vector.reshape(1, -1)
            
            # 归一化查询向量（如果需要）
            if hasattr(index_builder, 'metric') and index_builder.metric == 'cosine':
                query_vector = query_vector / np.linalg.norm(query_vector, axis=1, keepdims=True)
            
            # 执行搜索
            distances, indices = index_builder.search(query_vector, k * 2)  # 获取更多结果用于过滤
            
            # 后处理结果
            distances, indices = self._postprocess_search_results(distances, indices, k)
            
            search_time = time.time() - start_time
            self.logger.info(f"搜索完成，耗时: {search_time:.3f}秒")
            
            return distances, indices, search_time
            
        except Exception as e:
            self.logger.error(f"搜索优化时出错: {e}")
            raise
    
    def _postprocess_search_results(self, distances: np.ndarray, indices: np.ndarray, k: int) -> Tuple[np.ndarray, np.ndarray]:
        """后处理搜索结果"""
        try:
            # 过滤无效结果
            valid_mask = indices[0] >= 0
            valid_distances = distances[0][valid_mask]
            valid_indices = indices[0][valid_mask]
            
            # 限制结果数量
            if len(valid_indices) > k:
                valid_distances = valid_distances[:k]
                valid_indices = valid_indices[:k]
            
            return np.array([valid_distances]), np.array([valid_indices])
            
        except Exception as e:
            self.logger.error(f"后处理搜索结果时出错: {e}")
            return distances, indices
    
    def optimize_metadata_access(self, doc_ids: List[int], metadata_manager) -> Dict[int, Dict]:
        """
        优化元数据访问
        
        Args:
            doc_ids: 文档ID列表
            metadata_manager: 元数据管理器
            
        Returns:
            Dict[int, Dict]: 文档ID到元数据的映射
        """
        try:
            result = {}
            uncached_ids = []
            
            # 检查缓存
            for doc_id in doc_ids:
                if doc_id in self.metadata_cache:
                    result[doc_id] = self.metadata_cache[doc_id]
                else:
                    uncached_ids.append(doc_id)
            
            # 批量获取未缓存的元数据
            if uncached_ids:
                if hasattr(metadata_manager, 'get_metadata_batch'):
                    # 使用批量获取
                    batch_metadata = metadata_manager.get_metadata_batch(uncached_ids)
                    for doc_id, metadata in batch_metadata.items():
                        result[doc_id] = metadata
                        self.metadata_cache[doc_id] = metadata
                else:
                    # 逐个获取
                    for doc_id in uncached_ids:
                        metadata = metadata_manager.get_metadata(doc_id)
                        if metadata:
                            result[doc_id] = metadata
                            self.metadata_cache[doc_id] = metadata
                
                # 限制缓存大小
                if len(self.metadata_cache) > self.cache_size:
                    # 删除最旧的缓存项
                    keys_to_remove = list(self.metadata_cache.keys())[:len(self.metadata_cache) - self.cache_size]
                    for key in keys_to_remove:
                        del self.metadata_cache[key]
            
            return result
            
        except Exception as e:
            self.logger.error(f"优化元数据访问时出错: {e}")
            return {}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'vector_cache_size': len(self.vector_cache),
            'metadata_cache_size': len(self.metadata_cache),
            'batch_size': self.batch_size,
            'max_workers': self.max_workers,
            'cache_size_limit': self.cache_size,
            'gpu_enabled': self.enable_gpu
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.vector_cache.clear()
        self.metadata_cache.clear()
        self.logger.info("缓存已清空")
    
    def optimize_index_building(self, vectors: np.ndarray, doc_ids: np.ndarray, index_builder) -> float:
        """
        优化索引构建过程
        
        Args:
            vectors: 向量数组
            doc_ids: 文档ID数组
            index_builder: 索引构建器
            
        Returns:
            float: 构建时间
        """
        start_time = time.time()
        
        try:
            # 检查向量维度一致性
            if vectors.ndim != 2:
                raise ValueError(f"向量数组维度错误: {vectors.ndim}, 期望: 2")
            
            # 归一化向量（如果需要）
            if hasattr(index_builder, 'metric') and index_builder.metric == 'cosine':
                vectors = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
            
            # 批量添加向量
            if hasattr(index_builder, 'add_vectors_batch'):
                index_builder.add_vectors_batch(vectors, doc_ids)
            else:
                # 分批添加
                batch_size = min(self.batch_size, len(vectors))
                for i in range(0, len(vectors), batch_size):
                    end_idx = min(i + batch_size, len(vectors))
                    batch_vectors = vectors[i:end_idx]
                    batch_ids = doc_ids[i:end_idx]
                    
                    if hasattr(index_builder, 'add_vectors'):
                        index_builder.add_vectors(batch_vectors, batch_ids)
                    else:
                        # 逐个添加
                        for vector, doc_id in zip(batch_vectors, batch_ids):
                            index_builder.add_vector(vector, doc_id)
            
            build_time = time.time() - start_time
            self.logger.info(f"索引构建完成，耗时: {build_time:.3f}秒")
            
            return build_time
            
        except Exception as e:
            self.logger.error(f"优化索引构建时出错: {e}")
            raise


def create_performance_optimizer(config: Dict) -> PerformanceOptimizer:
    """
    创建性能优化器实例
    
    Args:
        config: 配置字典
        
    Returns:
        PerformanceOptimizer: 性能优化器实例
    """
    return PerformanceOptimizer(config)
