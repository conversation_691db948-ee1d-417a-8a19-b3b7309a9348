
# 使用官方Python基础镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY setup.py .
COPY README.md .
COPY src/ src/
COPY config/ config/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir .

# 创建非root用户
RUN useradd -m -u 1000 appuser \
    && chown -R appuser:appuser /app
USER appuser

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/cache

# 设置卷挂载点
VOLUME ["/app/data", "/app/logs", "/app/cache"]

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置入口点
ENTRYPOINT ["python", "-m", "md_vector_processor"]

# 设置默认命令
CMD ["--config", "config/config.yaml"]