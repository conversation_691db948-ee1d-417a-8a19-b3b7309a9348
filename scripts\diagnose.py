
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统诊断脚本
用于诊断和排查系统问题
"""

import sys
import logging
from pathlib import Path
from typing import Dict, Any, List
import argparse
import json
import psutil
import torch
import numpy as np
from datetime import datetime
import requests
import socket
import yaml
import sqlite3
import redis
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.utils import setup_logger, ConfigUtils, Timer

class SystemDiagnostic:
    """系统诊断类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化诊断器"""
        self.config = ConfigUtils.load_config(config_path)
        self.logger = setup_logger(self.config, 'diagnostic')
        self.results = {}
    
    def check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        results = {}
        
        # CPU信息
        cpu_info = {
            'cpu_count': psutil.cpu_count(),
            'cpu_percent': psutil.cpu_percent(interval=1, percpu=True),
            'load_avg': psutil.getloadavg()
        }
        results['cpu'] = cpu_info
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            'total': memory.total / 1024 / 1024 / 1024,  # GB
            'available': memory.available / 1024 / 1024 / 1024,  # GB
            'percent': memory.percent,
            'used': memory.used / 1024 / 1024 / 1024  # GB
        }
        results['memory'] = memory_info
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            'total': disk.total / 1024 / 1024 / 1024,  # GB
            'used': disk.used / 1024 / 1024 / 1024,  # GB
            'free': disk.free / 1024 / 1024 / 1024,  # GB
            'percent': disk.percent
        }
        results['disk'] = disk_info
        
        # GPU信息
        if torch.cuda.is_available():
            gpu_info = {
                'device_name': torch.cuda.get_device_name(0),
                'device_count': torch.cuda.device_count(),
                'memory_allocated': torch.cuda.memory_allocated(0) / 1024 / 1024,  # MB
                'memory_cached': torch.cuda.memory_reserved(0) / 1024 / 1024  # MB
            }
        else:
            gpu_info = {'available': False}
        results['gpu'] = gpu_info
        
        return results
    
    def check_network(self) -> Dict[str, Any]:
        """检查网络连接"""
        results = {}
        
        # 检查网络接口
        network_info = {}
        for interface, stats in psutil.net_if_stats().items():
            network_info[interface] = {
                'up': stats.isup,
                'speed': stats.speed,
                'mtu': stats.mtu
            }
        results['interfaces'] = network_info
        
        # 检查端口
        ports_to_check = [
            ('localhost', 8000),  # API
            ('localhost', 6379),  # Redis
            ('localhost', 5432)   # PostgreSQL
        ]
        
        port_status = {}
        for host, port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex((host, port))
            port_status[f'{host}:{port}'] = result == 0
            sock.close()
        results['ports'] = port_status
        
        # 检查API健康状态
        try:
            response = requests.get('http://localhost:8000/health', timeout=5)
            results['api_health'] = {
                'status': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
        except requests.exceptions.RequestException as e:
            results['api_health'] = {
                'status': False,
                'error': str(e)
            }
        
        return results
    
    def check_storage(self) -> Dict[str, Any]:
        """检查存储系统"""
        results = {}
        
        # 检查向量存储
        vector_dir = Path(self.config['storage']['base_dir'])
        results['vector_storage'] = {
            'exists': vector_dir.exists(),
            'is_dir': vector_dir.is_dir() if vector_dir.exists() else False,
            'size': sum(f.stat().st_size for f in vector_dir.glob('**/*') if f.is_file()) / 1024 / 1024  # MB
        }
        
        # 检查数据库
        try:
            db_path = self.config.get('storage', {}).get('db_path', 'metadata.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            db_info = {
                'connected': True,
                'tables': [table[0] for table in tables],
                'size': Path(db_path).stat().st_size / 1024 / 1024  # MB
            }
            
            conn.close()
            results['database'] = db_info
            
        except sqlite3.Error as e:
            results['database'] = {
                'connected': False,
                'error': str(e)
            }
        
        # 检查Redis缓存
        try:
            redis_host = self.config.get('cache', {}).get('redis_host', 'localhost')
            redis_port = self.config.get('cache', {}).get('redis_port', 6379)
            r = redis.Redis(host=redis_host, port=redis_port)
            
            info = r.info()
            results['redis'] = {
                'connected': True,
                'version': info['redis_version'],
                'used_memory': info['used_memory'] / 1024 / 1024,  # MB
                'connected_clients': info['connected_clients']
            }
            
        except redis.RedisError as e:
            results['redis'] = {
                'connected': False,
                'error': str(e)
            }
        
        return results
    
    def check_configuration(self) -> Dict[str, Any]:
        """检查配置"""
        results = {}
        
        # 检查必要的配置项
        required_configs = [
            'file_processing',
            'preprocessing',
            'vectorization',
            'indexing',
            'storage'
        ]
        
        config_status = {}
        for config_name in required_configs:
            config_status[config_name] = {
                'exists': config_name in self.config,
                'valid': bool(self.config.get(config_name))
            }
        results['required_configs'] = config_status
        
        # 检查文件权限
        paths_to_check = [
            self.config.get('file_processing', {}).get('input_dir'),
            self.config.get('file_processing', {}).get('output_dir'),
            self.config.get('storage', {}).get('base_dir')
        ]
        
        path_permissions = {}
        for path_str in paths_to_check:
            if path_str:
                path = Path(path_str)
                path_permissions[path_str] = {
                    'exists': path.exists(),
                    'readable': os.access(path, os.R_OK) if path.exists() else False,
                    'writable': os.access(path, os.W_OK) if path.exists() else False
                }
        results['path_permissions'] = path_permissions
        
        return results
    
    def run_diagnostics(self) -> Dict[str, Any]:
        """运行完整的系统诊断"""
        self.logger.info("开始系统诊断...")
        
        # 检查系统资源
        self.logger.info("检查系统资源...")
        self.results['system_resources'] = self.check_system_resources()
        
        # 检查网络
        self.logger.info("检查网络连接...")
        self.results['network'] = self.check_network()
        
        # 检查存储
        self.logger.info("检查存储系统...")
        self.results['storage'] = self.check_storage()
        
        # 检查配置
        self.logger.info("检查配置...")
        self.results['configuration'] = self.check_configuration()
        
        # 添加诊断元数据
        self.results['metadata'] = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform
        }
        
        return self.results
    
    def save_results(self, output_path: str = "diagnostic_results.json"):
        """保存诊断结果"""
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        self.logger.info(f"诊断结果已保存到: {output_path}")
    
    def print_summary(self):
        """打印诊断结果摘要"""
        print("\n=== 系统诊断结果摘要 ===\n")
        
        # 系统资源摘要
        print("系统资源状态:")
        resources = self.results['system_resources']
        print(f"CPU使用率: {np.mean(resources['cpu']['cpu_percent']):.1f}%")
        print(f"内存使用率: {resources['memory']['percent']:.1f}%")
        print(f"磁盘使用率: {resources['disk']['percent']:.1f}%")
        if resources['gpu'].get('available'):
            print(f"GPU内存使用: {resources['gpu']['memory_allocated']:.0f} MB")
        print()
        
        # 网络状态摘要
        print("网络状态:")
        network = self.results['network']
        for port, status in network['ports'].items():
            print(f"{port}: {'开放' if status else '关闭'}")
        print(f"API健康状态: {'正常' if network['api_health']['status'] else '异常'}")
        print()
        
        # 存储状态摘要
        print("存储状态:")
        storage = self.results['storage']
        print(f"向量存储大小: {storage['vector_storage']['size']:.2f} MB")
        if storage['database']['connected']:
            print(f"数据库状态: 正常")
            print(f"数据库大小: {storage['database']['size']:.2f} MB")
        else:
            print(f"数据库状态: 异常 - {storage['database'].get('error')}")
        print()
        
        # 配置检查摘要
        print("配置检查:")
        config = self.results['configuration']
        for name, status in config['required_configs'].items():
            print(f"{name}: {'有效' if status['valid'] else '无效'}")
        print()
        
        # 问题报告
        self._print_issues()
    
    def _print_issues(self):
        """打印发现的问题"""
        issues = []
        
        # 检查系统资源问题
        resources = self.results['system_resources']
        if resources['memory']['percent'] > 90:
            issues.append("内存使用率过高")
        if resources['disk']['percent'] > 90:
            issues.append("磁盘空间不足")
        if np.mean(resources['cpu']['cpu_percent']) > 90:
            issues.append("CPU使用率过高")
        
        # 检查网络问题
        network = self.results['network']
        if not network['api_health']['status']:
            issues.append("API服务异常")
        for port, status in network['ports'].items():
            if not status:
                issues.append(f"端口 {port} 未开放")
        
        # 检查存储问题
        storage = self.results['storage']
        if not storage['database']['connected']:
            issues.append("数据库连接失败")
        if not storage['vector_storage']['exists']:
            issues.append("向量存储目录不存在")
        
        if issues:
            print("发现的问题:")
            for issue in issues:
                print(f"- {issue}")
        else:
            print("未发现重大问题")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor系统诊断工具')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--output', type=str, default='diagnostic_results.json',
                       help='结果输出路径')
    args = parser.parse_args()
    
    # 运行诊断
    diagnostic = SystemDiagnostic(args.config)
    diagnostic.run_diagnostics()
    diagnostic.save_results(args.output)
    diagnostic.print_summary()

if __name__ == '__main__':
    main()