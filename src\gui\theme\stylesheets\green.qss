/* 绿色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #E8F5E9;
}

QLabel {
    color: #1B5E20;
}

QPushButton {
    background-color: #43A047;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #388E3C;
}

QPushButton:pressed {
    background-color: #2E7D32;
}

QPushButton:disabled {
    background-color: #C8E6C9;
    color: #A5D6A7;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #1B5E20;
    border: 1px solid #A5D6A7;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #43A047;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #E8F5E9;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #A5D6A7;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #E8F5E9;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #A5D6A7;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #43A047;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #388E3C;
}

QMenu {
    background-color: #FFFFFF;
    color: #1B5E20;
    border: 1px solid #A5D6A7;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #E8F5E9;
}

QTabWidget::pane {
    border: 1px solid #A5D6A7;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #C8E6C9;
    color: #1B5E20;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #43A047;
}

QStatusBar {
    background-color: #43A047;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #43A047;
}

#sidebarWidget {
    background-color: #43A047;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #E8F5E9;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #A5D6A7;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #43A047;
    margin-bottom: 8px;
}

.card-content {
    color: #1B5E20;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #43A047;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #43A047;
    box-shadow: 0 0 10px #43A047;
}
