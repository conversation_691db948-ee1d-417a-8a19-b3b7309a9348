
# EditorConfig is awesome: https://EditorConfig.org

# 顶级EditorConfig文件
root = true

# 所有文件
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Python文件
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88  # 与Black格式化工具保持一致

# Markdown文件
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# YAML文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON文件
[*.json]
indent_style = space
indent_size = 2

# 配置文件
[*.{ini,cfg}]
indent_style = space
indent_size = 4

# Makefile
[Makefile]
indent_style = tab

# 文档
[docs/**]
max_line_length = off

# 测试文件
[tests/**]
max_line_length = 88

# 要求文件
[requirements{,-*}.txt]
indent_style = space
indent_size = 2

# Shell脚本
[*.sh]
end_of_line = lf
indent_style = space
indent_size = 2

# Windows批处理文件
[*.{cmd,bat}]
end_of_line = crlf

# 前端文件
[*.{js,jsx,ts,tsx,css,scss,html}]
indent_style = space
indent_size = 2
