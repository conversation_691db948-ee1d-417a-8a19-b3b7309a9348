#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动化测试脚本
测试文档加载、向量化、检索和AI对话功能
"""

import os
import sys
import time
import json
import logging
from pathlib import Path
from datetime import datetime
import traceback

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomationTester:
    """自动化测试类"""
    
    def __init__(self):
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {},
            'recommendations': []
        }
        self.docs_dir = Path("docs")
        self.test_files = []
        
    def discover_test_files(self):
        """发现测试文件"""
        logger.info("🔍 发现测试文件...")
        
        supported_extensions = {'.md', '.txt', '.pdf', '.docx'}
        
        for file_path in self.docs_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                self.test_files.append(file_path)
                logger.info(f"发现文件: {file_path}")
        
        logger.info(f"总共发现 {len(self.test_files)} 个测试文件")
        return self.test_files
    
    def test_document_loading(self):
        """测试文档加载功能"""
        logger.info("📄 测试文档加载功能...")
        
        test_result = {
            'name': '文档加载测试',
            'status': 'running',
            'details': {},
            'errors': []
        }
        
        try:
            from src.utils.document_loader import create_document_loader
            
            # 创建进度回调
            def progress_callback(current, total, message):
                logger.info(f"加载进度: {current}/{total} - {message}")
            
            loader = create_document_loader(progress_callback)
            
            # 测试每个文件
            for file_path in self.test_files:
                try:
                    logger.info(f"加载文件: {file_path}")
                    documents, doc_infos = loader.load_documents([str(file_path)], validate_integrity=True)
                    
                    if str(file_path) in documents:
                        content = documents[str(file_path)]
                        doc_info = doc_infos[0] if doc_infos else None
                        
                        test_result['details'][str(file_path)] = {
                            'status': 'success',
                            'content_length': len(content),
                            'encoding': doc_info.encoding if doc_info else 'unknown',
                            'load_time': doc_info.load_time if doc_info else 0,
                            'file_size': doc_info.size if doc_info else 0
                        }
                        
                        logger.info(f"✅ 成功加载: {file_path.name} ({len(content)} 字符)")
                    else:
                        test_result['details'][str(file_path)] = {
                            'status': 'failed',
                            'error': '文档加载失败'
                        }
                        logger.error(f"❌ 加载失败: {file_path}")
                        
                except Exception as e:
                    error_msg = f"加载文件 {file_path} 时出错: {str(e)}"
                    test_result['errors'].append(error_msg)
                    logger.error(error_msg)
            
            # 计算成功率
            total_files = len(self.test_files)
            successful_files = sum(1 for details in test_result['details'].values() 
                                 if details.get('status') == 'success')
            
            test_result['success_rate'] = successful_files / total_files if total_files > 0 else 0
            test_result['status'] = 'completed'
            
            logger.info(f"文档加载测试完成: {successful_files}/{total_files} 成功")
            
        except Exception as e:
            test_result['status'] = 'failed'
            test_result['errors'].append(f"文档加载测试失败: {str(e)}")
            logger.error(f"文档加载测试失败: {e}")
            logger.error(traceback.format_exc())
        
        self.test_results['tests']['document_loading'] = test_result
        return test_result
    
    def test_vectorization(self):
        """测试向量化功能"""
        logger.info("🔢 测试向量化功能...")
        
        test_result = {
            'name': '向量化测试',
            'status': 'running',
            'details': {},
            'errors': []
        }
        
        try:
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager, VectorStore
            from src.utils.helpers import TextUtils
            import numpy as np
            import hashlib
            
            # 创建向量化配置
            vectorization_config = {
                'vectorization': {
                    'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                    'vector_dimension': 384,
                    'batch_size': 16,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }
            
            # 创建嵌入器
            embedder = TextEmbedding(vectorization_config)
            logger.info("✅ 成功创建文本嵌入器")
            
            # 创建存储管理器
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            metadata_manager = MetadataManager(storage_config)
            
            # 测试向量化
            test_text = "这是一个测试文本，用于验证向量化功能是否正常工作。"
            
            # 文本分块
            chunks = TextUtils.split_text(test_text, chunk_size=100, overlap=20)
            logger.info(f"文本分割为 {len(chunks)} 个块")
            
            # 向量化
            vectors = []
            doc_ids = []
            
            for i, chunk in enumerate(chunks):
                vector = embedder.encode_text(chunk)
                vectors.append(vector)
                
                # 生成文档ID
                chunk_id = int(hashlib.md5((test_text + chunk + str(i)).encode()).hexdigest(), 16) % (10 ** 10)
                doc_ids.append(chunk_id)
            
            if vectors:
                vectors_array = np.vstack(vectors)
                doc_ids_array = np.array(doc_ids)
                
                logger.info(f"成功向量化 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")
                
                # 存储向量
                success = vector_store.store_vectors(vectors_array, doc_ids_array)
                if success:
                    logger.info("✅ 成功存储向量")
                    test_result['details']['vector_storage'] = 'success'
                else:
                    logger.error("❌ 向量存储失败")
                    test_result['details']['vector_storage'] = 'failed'
                
                # 存储元数据
                for i, (chunk_id, chunk) in enumerate(zip(doc_ids, chunks)):
                    metadata = {
                        'text': chunk,
                        'chunk_index': i,
                        'total_chunks': len(chunks),
                        'test_type': 'automation_test',
                        'created_at': datetime.now().isoformat(),
                        'vector_dimension': vectors[i].shape[0]
                    }
                    
                    success = metadata_manager.store_metadata(chunk_id, metadata)
                    if not success:
                        logger.warning(f"元数据存储失败: chunk {i}")
                
                test_result['details']['vectorization'] = {
                    'chunks_count': len(chunks),
                    'vector_dimension': vectors_array.shape[1],
                    'total_vectors': len(vectors_array)
                }
                
            test_result['status'] = 'completed'
            logger.info("向量化测试完成")
            
        except Exception as e:
            test_result['status'] = 'failed'
            test_result['errors'].append(f"向量化测试失败: {str(e)}")
            logger.error(f"向量化测试失败: {e}")
            logger.error(traceback.format_exc())
        
        self.test_results['tests']['vectorization'] = test_result
        return test_result
    
    def test_index_creation(self):
        """测试索引创建功能"""
        logger.info("📊 测试索引创建功能...")
        
        test_result = {
            'name': '索引创建测试',
            'status': 'running',
            'details': {},
            'errors': []
        }
        
        try:
            from src.indexer import IndexBuilder
            import numpy as np
            
            # 创建测试向量
            test_vectors = np.random.rand(100, 384).astype('float32')
            
            # 测试不同类型的索引
            index_configs = {
                'flat': {
                    'indexing': {
                        'index_type': 'flat',
                        'metric': 'cosine',
                        'quantization': 'none'
                    }
                },
                'ivf': {
                    'indexing': {
                        'index_type': 'ivf',
                        'metric': 'cosine',
                        'nlist': 10,
                        'quantization': 'none'
                    }
                }
            }
            
            for index_name, config in index_configs.items():
                try:
                    logger.info(f"测试 {index_name} 索引...")
                    
                    builder = IndexBuilder(config)
                    
                    # 创建索引
                    builder.create_index(384)
                    
                    # 添加向量
                    builder.index.add(test_vectors)
                    
                    # 测试搜索
                    query_vector = np.random.rand(1, 384).astype('float32')
                    distances, indices = builder.index.search(query_vector, 5)
                    
                    test_result['details'][index_name] = {
                        'status': 'success',
                        'vector_count': test_vectors.shape[0],
                        'search_results': len(indices[0])
                    }
                    
                    logger.info(f"✅ {index_name} 索引测试成功")
                    
                except Exception as e:
                    test_result['details'][index_name] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    logger.error(f"❌ {index_name} 索引测试失败: {e}")
            
            test_result['status'] = 'completed'
            
        except Exception as e:
            test_result['status'] = 'failed'
            test_result['errors'].append(f"索引创建测试失败: {str(e)}")
            logger.error(f"索引创建测试失败: {e}")
            logger.error(traceback.format_exc())
        
        self.test_results['tests']['index_creation'] = test_result
        return test_result
    
    def test_search_functionality(self):
        """测试搜索功能"""
        logger.info("🔍 测试搜索功能...")
        
        test_result = {
            'name': '搜索功能测试',
            'status': 'running',
            'details': {},
            'errors': []
        }
        
        try:
            from src.indexer.search import VectorSearcher
            from src.vectorizer import TextEmbedding
            import numpy as np
            
            # 创建搜索器配置
            search_config = {
                'indexing': {
                    'index_type': 'faiss',
                    'metric': 'cosine',
                    'default_k': 5,
                    'min_similarity': 0.3
                }
            }
            
            searcher = VectorSearcher(search_config)
            
            # 创建测试索引和数据
            import faiss
            dimension = 384
            test_vectors = np.random.rand(50, dimension).astype('float32')
            
            index = faiss.IndexFlatIP(dimension)
            index.add(test_vectors)
            
            searcher.set_index(index)
            
            # 创建测试元数据
            metadata = {i: {'text': f'测试文档 {i}', 'id': i} for i in range(50)}
            searcher.set_metadata(metadata)
            
            # 执行搜索测试
            query_vector = np.random.rand(dimension).astype('float32')
            
            # 测试基本搜索
            results = searcher.search(query_vector, k=5)
            
            test_result['details']['basic_search'] = {
                'status': 'success' if results else 'failed',
                'results_count': len(results),
                'has_metadata': all(r.metadata is not None for r in results)
            }
            
            logger.info(f"基本搜索返回 {len(results)} 个结果")
            
            # 测试批量搜索
            query_vectors = np.random.rand(3, dimension).astype('float32')
            batch_results = searcher.batch_search(query_vectors, k=3)
            
            test_result['details']['batch_search'] = {
                'status': 'success' if batch_results else 'failed',
                'query_count': len(batch_results),
                'avg_results': sum(len(r) for r in batch_results) / len(batch_results) if batch_results else 0
            }
            
            logger.info(f"批量搜索处理 {len(batch_results)} 个查询")
            
            test_result['status'] = 'completed'
            logger.info("搜索功能测试完成")
            
        except Exception as e:
            test_result['status'] = 'failed'
            test_result['errors'].append(f"搜索功能测试失败: {str(e)}")
            logger.error(f"搜索功能测试失败: {e}")
            logger.error(traceback.format_exc())
        
        self.test_results['tests']['search_functionality'] = test_result
        return test_result
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始自动化测试...")
        
        # 发现测试文件
        self.discover_test_files()
        
        # 运行各项测试
        tests = [
            self.test_document_loading,
            self.test_vectorization,
            self.test_index_creation,
            self.test_search_functionality
        ]
        
        for test_func in tests:
            try:
                test_func()
                time.sleep(1)  # 测试间隔
            except Exception as e:
                logger.error(f"测试执行失败: {e}")
        
        # 生成测试报告
        self.generate_report()
        
        return self.test_results
    
    def generate_report(self):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        self.test_results['end_time'] = datetime.now().isoformat()
        
        # 计算总体统计
        total_tests = len(self.test_results['tests'])
        passed_tests = sum(1 for test in self.test_results['tests'].values() 
                          if test.get('status') == 'completed')
        failed_tests = total_tests - passed_tests
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'test_files_count': len(self.test_files)
        }
        
        # 保存报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试报告已保存: {report_file}")
        
        # 打印摘要
        print("\n" + "="*60)
        print("🎯 自动化测试报告摘要")
        print("="*60)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {failed_tests}")
        print(f"成功率: {self.test_results['summary']['success_rate']:.1%}")
        print(f"测试文件数: {len(self.test_files)}")
        print("="*60)

if __name__ == "__main__":
    tester = AutomationTester()
    results = tester.run_all_tests()
