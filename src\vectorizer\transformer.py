
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量转换器模块
负责向量的转换、压缩和处理
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
from sklearn.decomposition import PCA, TruncatedSVD
from sklearn.manifold import TSNE
from umap import UMAP
import torch
from pathlib import Path
import json
import h5py
import faiss
from tqdm import tqdm

class VectorTransformer:
    """向量转换器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化向量转换器

        Args:
            config: 配置字典，包含向量化相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.vectorization_config = config.get('vectorization', {})
        self.vector_dimension = self.vectorization_config.get('vector_dimension', 384)
        self.compression_method = self.vectorization_config.get('compression_method', 'pq')  # 'pq' or 'svd'
        self.reduced_dimension = self.vectorization_config.get('reduced_dimension', 128)
        self.visualization_method = self.vectorization_config.get('visualization_method', 'tsne')  # 'tsne', 'umap', or 'pca'

        # 初始化转换器
        self._initialize_transformers()

    def _initialize_transformers(self):
        """初始化各种转换器"""
        try:
            # 降维器
            self.pca = PCA(n_components=self.reduced_dimension)
            self.svd = TruncatedSVD(n_components=self.reduced_dimension)

            # 可视化转换器
            if self.visualization_method == 'tsne':
                self.vis_transformer = TSNE(n_components=2, random_state=42)
            elif self.visualization_method == 'umap':
                self.vis_transformer = UMAP(n_components=2, random_state=42)
            else:  # pca
                self.vis_transformer = PCA(n_components=2)

            # 向量压缩器
            if self.compression_method == 'pq':
                # 使用Product Quantization进行压缩
                self.compressor = faiss.IndexPQ(
                    self.vector_dimension,
                    8,  # 子空间数量
                    8   # 每个子空间的比特数
                )

        except Exception as e:
            self.logger.error(f"初始化转换器时出错: {e}")
            raise

    def normalize_vectors(self, vectors: np.ndarray) -> np.ndarray:
        """
        标准化向量

        Args:
            vectors: 输入向量矩阵

        Returns:
            np.ndarray: 标准化后的向量矩阵
        """
        try:
            # L2标准化
            norms = np.linalg.norm(vectors, axis=1, keepdims=True)
            norms[norms == 0] = 1  # 避免除零
            return vectors / norms
        except Exception as e:
            self.logger.error(f"标准化向量时出错: {e}")
            return vectors

    def reduce_dimensions(self, vectors: np.ndarray, method: str = 'pca') -> np.ndarray:
        """
        降维处理

        Args:
            vectors: 输入向量矩阵
            method: 降维方法 ('pca' or 'svd')

        Returns:
            np.ndarray: 降维后的向量矩阵
        """
        try:
            if method == 'pca':
                return self.pca.fit_transform(vectors)
            elif method == 'svd':
                return self.svd.fit_transform(vectors)
            else:
                self.logger.warning(f"未知的降维方法: {method}")
                return vectors
        except Exception as e:
            self.logger.error(f"降维处理时出错: {e}")
            return vectors

    def visualize_vectors(self, vectors: np.ndarray) -> np.ndarray:
        """
        将向量转换为2D可视化表示

        Args:
            vectors: 输入向量矩阵

        Returns:
            np.ndarray: 2D向量矩阵
        """
        try:
            return self.vis_transformer.fit_transform(vectors)
        except Exception as e:
            self.logger.error(f"向量可视化转换时出错: {e}")
            return np.zeros((vectors.shape[0], 2))

    def compress_vectors(self, vectors: np.ndarray) -> Tuple[np.ndarray, Any]:
        """
        压缩向量

        Args:
            vectors: 输入向量矩阵

        Returns:
            Tuple[np.ndarray, Any]: (压缩后的数据, 压缩器状态)
        """
        try:
            if self.compression_method == 'pq':
                # Product Quantization压缩
                self.compressor.train(vectors)
                codes = np.zeros((vectors.shape[0], self.compressor.code_size), dtype='uint8')
                self.compressor.compute_codes(vectors, codes)
                return codes, self.compressor

            elif self.compression_method == 'svd':
                # SVD压缩
                compressed = self.svd.fit_transform(vectors)
                return compressed, self.svd

            else:
                self.logger.warning(f"未知的压缩方法: {self.compression_method}")
                return vectors, None

        except Exception as e:
            self.logger.error(f"压缩向量时出错: {e}")
            return vectors, None

    def decompress_vectors(self, compressed_data: np.ndarray, compressor: Any) -> np.ndarray:
        """
        解压缩向量

        Args:
            compressed_data: 压缩后的数据
            compressor: 压缩器状态

        Returns:
            np.ndarray: 解压缩后的向量矩阵
        """
        try:
            if self.compression_method == 'pq' and isinstance(compressor, faiss.IndexPQ):
                # Product Quantization解压缩
                reconstructed = np.zeros((compressed_data.shape[0], self.vector_dimension), dtype='float32')
                compressor.reconstruct_n(0, compressed_data.shape[0], reconstructed)
                return reconstructed

            elif self.compression_method == 'svd' and isinstance(compressor, TruncatedSVD):
                # SVD解压缩
                return compressor.inverse_transform(compressed_data)

            else:
                self.logger.warning("无法解压缩向量")
                return compressed_data

        except Exception as e:
            self.logger.error(f"解压缩向量时出错: {e}")
            return compressed_data

    def save_vectors(self, vectors: np.ndarray, metadata: Dict[str, Any], file_path: Path):
        """
        保存向量到文件

        Args:
            vectors: 向量矩阵
            metadata: 元数据字典
            file_path: 保存路径
        """
        try:
            with h5py.File(file_path, 'w') as f:
                # 保存向量
                f.create_dataset('vectors', data=vectors)

                # 保存元数据
                metadata_group = f.create_group('metadata')
                for key, value in metadata.items():
                    if isinstance(value, (str, int, float, bool)):
                        metadata_group.attrs[key] = value
                    else:
                        # 将复杂对象转换为JSON字符串
                        metadata_group.attrs[key] = json.dumps(value)

            self.logger.info(f"向量已保存到: {file_path}")

        except Exception as e:
            self.logger.error(f"保存向量时出错: {e}")

    def load_vectors(self, file_path: Path) -> Tuple[Optional[np.ndarray], Optional[Dict[str, Any]]]:
        """
        从文件加载向量

        Args:
            file_path: 文件路径

        Returns:
            Tuple[Optional[np.ndarray], Optional[Dict[str, Any]]]: (向量矩阵, 元数据字典)
        """
        try:
            with h5py.File(file_path, 'r') as f:
                # 加载向量
                vectors = f['vectors'][:]

                # 加载元数据
                metadata = {}
                metadata_group = f['metadata']
                for key in metadata_group.attrs.keys():
                    value = metadata_group.attrs[key]
                    try:
                        # 尝试解析JSON字符串
                        metadata[key] = json.loads(value)
                    except:
                        metadata[key] = value

                return vectors, metadata

        except Exception as e:
            self.logger.error(f"加载向量时出错: {e}")
            return None, None

    def batch_process(self, vectors: np.ndarray, batch_size: int = 1000,
                     operations: List[str] = None) -> np.ndarray:
        """
        批量处理向量

        Args:
            vectors: 输入向量矩阵
            batch_size: 批处理大小
            operations: 要执行的操作列表 ['normalize', 'reduce', 'compress']

        Returns:
            np.ndarray: 处理后的向量矩阵
        """
        if operations is None:
            operations = ['normalize']

        try:
            result = vectors.copy()
            total_batches = (vectors.shape[0] + batch_size - 1) // batch_size

            for i in tqdm(range(total_batches), desc="Processing vectors"):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, vectors.shape[0])
                batch = result[start_idx:end_idx]

                for operation in operations:
                    if operation == 'normalize':
                        batch = self.normalize_vectors(batch)
                    elif operation == 'reduce':
                        batch = self.reduce_dimensions(batch)
                    elif operation == 'compress':
                        batch, _ = self.compress_vectors(batch)

                result[start_idx:end_idx] = batch

            return result

        except Exception as e:
            self.logger.error(f"批量处理向量时出错: {e}")
            return vectors

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'vectorization': {
            'vector_dimension': 384,
            'compression_method': 'pq',
            'reduced_dimension': 128,
            'visualization_method': 'tsne'
        }
    }

    transformer = VectorTransformer(test_config)

    # 生成测试向量
    test_vectors = np.random.rand(100, 384)

    # 测试标准化
    normalized = transformer.normalize_vectors(test_vectors)
    print(f"标准化后的向量维度: {normalized.shape}")

    # 测试降维
    reduced = transformer.reduce_dimensions(test_vectors)
    print(f"降维后的向量维度: {reduced.shape}")

    # 测试可视化
    visualized = transformer.visualize_vectors(test_vectors)
    print(f"可视化后的向量维度: {visualized.shape}")

    # 测试压缩和解压缩
    compressed, compressor = transformer.compress_vectors(test_vectors)
    decompressed = transformer.decompress_vectors(compressed, compressor)
    print(f"压缩后的向量维度: {compressed.shape}")
    print(f"解压缩后的向量维度: {decompressed.shape}")

    # 测试批量处理
    processed = transformer.batch_process(
        test_vectors,
        batch_size=32,
        operations=['normalize', 'reduce']
    )
    print(f"批量处理后的向量维度: {processed.shape}")