#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
本地模型管理器

用于检测、配置和管理本地部署的AI模型，包括Ollama、Hugging Face本地模型等
"""

import os
import json
import requests
import logging
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class LocalModel:
    """本地模型信息"""
    name: str
    type: str  # 'ollama', 'huggingface', 'openai_compatible'
    endpoint: str
    model_id: str
    description: str = ""
    parameters: Dict[str, Any] = None
    is_available: bool = False
    vector_dimension: int = 384
    supports_embedding: bool = True
    supports_chat: bool = True

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


class LocalModelManager:
    """本地模型管理器"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化本地模型管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or "config/local_models.json"
        self.models: Dict[str, LocalModel] = {}
        self.logger = logging.getLogger(__name__)
        
        # 确保配置目录存在
        Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        self.load_config()
        
        # 检测可用模型
        self.detect_models()

    def load_config(self):
        """加载本地模型配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 转换为LocalModel对象
                for model_data in config_data.get('models', []):
                    model = LocalModel(**model_data)
                    self.models[model.name] = model
                
                self.logger.info(f"加载了 {len(self.models)} 个本地模型配置")
            else:
                self.logger.info("本地模型配置文件不存在，将创建默认配置")
                self._create_default_config()
        except Exception as e:
            self.logger.error(f"加载本地模型配置失败: {e}")
            self._create_default_config()

    def save_config(self):
        """保存本地模型配置"""
        try:
            config_data = {
                'models': [asdict(model) for model in self.models.values()]
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"保存了 {len(self.models)} 个本地模型配置")
        except Exception as e:
            self.logger.error(f"保存本地模型配置失败: {e}")

    def _create_default_config(self):
        """创建默认配置"""
        # 默认Ollama配置
        ollama_models = [
            LocalModel(
                name="ollama_llama2",
                type="ollama",
                endpoint="http://localhost:11434",
                model_id="llama2",
                description="Llama 2 模型 (Ollama)",
                supports_embedding=True,
                supports_chat=True
            ),
            LocalModel(
                name="ollama_mistral",
                type="ollama", 
                endpoint="http://localhost:11434",
                model_id="mistral",
                description="Mistral 模型 (Ollama)",
                supports_embedding=True,
                supports_chat=True
            ),
            LocalModel(
                name="ollama_nomic_embed",
                type="ollama",
                endpoint="http://localhost:11434", 
                model_id="nomic-embed-text",
                description="Nomic Embed 向量化模型 (Ollama)",
                vector_dimension=768,
                supports_embedding=True,
                supports_chat=False
            )
        ]
        
        for model in ollama_models:
            self.models[model.name] = model
        
        self.save_config()

    def detect_models(self):
        """检测可用的本地模型"""
        self.logger.info("开始检测本地模型...")
        
        # 检测Ollama模型
        self._detect_ollama_models()
        
        # 检测其他类型的模型
        self._detect_huggingface_models()
        
        # 更新配置
        self.save_config()

    def _detect_ollama_models(self):
        """检测Ollama模型"""
        try:
            # 检查Ollama是否运行
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                available_models = [model['name'] for model in data.get('models', [])]
                
                self.logger.info(f"检测到 {len(available_models)} 个Ollama模型: {available_models}")
                
                # 更新现有模型状态
                for model in self.models.values():
                    if model.type == "ollama":
                        model.is_available = model.model_id in available_models
                
                # 添加新发现的模型
                for model_name in available_models:
                    model_key = f"ollama_{model_name.replace(':', '_')}"
                    if model_key not in self.models:
                        new_model = LocalModel(
                            name=model_key,
                            type="ollama",
                            endpoint="http://localhost:11434",
                            model_id=model_name,
                            description=f"自动检测的Ollama模型: {model_name}",
                            is_available=True
                        )
                        self.models[model_key] = new_model
                        self.logger.info(f"添加新检测到的Ollama模型: {model_name}")
            else:
                self.logger.warning("Ollama服务未响应")
                
        except requests.exceptions.RequestException:
            self.logger.warning("无法连接到Ollama服务 (http://localhost:11434)")
        except Exception as e:
            self.logger.error(f"检测Ollama模型时出错: {e}")

    def _detect_huggingface_models(self):
        """检测Hugging Face本地模型"""
        try:
            # 检查常见的Hugging Face缓存目录
            hf_cache_dirs = [
                os.path.expanduser("~/.cache/huggingface/transformers"),
                os.path.expanduser("~/.cache/huggingface/hub"),
                os.environ.get("HF_HOME", ""),
                os.environ.get("TRANSFORMERS_CACHE", "")
            ]
            
            for cache_dir in hf_cache_dirs:
                if cache_dir and os.path.exists(cache_dir):
                    self.logger.info(f"检测到Hugging Face缓存目录: {cache_dir}")
                    # 这里可以进一步扫描具体的模型
                    break
            else:
                self.logger.info("未检测到Hugging Face本地模型")
                
        except Exception as e:
            self.logger.error(f"检测Hugging Face模型时出错: {e}")

    def get_available_models(self, model_type: Optional[str] = None, 
                           supports_embedding: Optional[bool] = None,
                           supports_chat: Optional[bool] = None) -> List[LocalModel]:
        """
        获取可用的本地模型
        
        Args:
            model_type: 模型类型过滤
            supports_embedding: 是否支持向量化
            supports_chat: 是否支持对话
            
        Returns:
            List[LocalModel]: 符合条件的模型列表
        """
        models = []
        for model in self.models.values():
            if not model.is_available:
                continue
                
            if model_type and model.type != model_type:
                continue
                
            if supports_embedding is not None and model.supports_embedding != supports_embedding:
                continue
                
            if supports_chat is not None and model.supports_chat != supports_chat:
                continue
                
            models.append(model)
        
        return models

    def get_model(self, name: str) -> Optional[LocalModel]:
        """获取指定名称的模型"""
        return self.models.get(name)

    def test_model_connection(self, model: LocalModel) -> Tuple[bool, str]:
        """
        测试模型连接
        
        Args:
            model: 要测试的模型
            
        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        try:
            if model.type == "ollama":
                return self._test_ollama_model(model)
            elif model.type == "huggingface":
                return self._test_huggingface_model(model)
            elif model.type == "openai_compatible":
                return self._test_openai_compatible_model(model)
            else:
                return False, f"不支持的模型类型: {model.type}"
                
        except Exception as e:
            return False, f"测试连接时出错: {str(e)}"

    def _test_ollama_model(self, model: LocalModel) -> Tuple[bool, str]:
        """测试Ollama模型连接"""
        try:
            # 测试基本连接
            response = requests.get(f"{model.endpoint}/api/tags", timeout=10)
            if response.status_code != 200:
                return False, f"Ollama服务响应错误: {response.status_code}"
            
            # 检查模型是否存在
            data = response.json()
            available_models = [m['name'] for m in data.get('models', [])]
            if model.model_id not in available_models:
                return False, f"模型 {model.model_id} 不存在于Ollama中"
            
            # 测试向量化功能（如果支持）
            if model.supports_embedding:
                test_response = requests.post(
                    f"{model.endpoint}/api/embeddings",
                    json={
                        "model": model.model_id,
                        "prompt": "test"
                    },
                    timeout=30
                )
                if test_response.status_code != 200:
                    return False, f"向量化测试失败: {test_response.status_code}"
            
            return True, "连接成功"
            
        except requests.exceptions.Timeout:
            return False, "连接超时"
        except requests.exceptions.ConnectionError:
            return False, "无法连接到Ollama服务"
        except Exception as e:
            return False, f"测试失败: {str(e)}"

    def _test_huggingface_model(self, model: LocalModel) -> Tuple[bool, str]:
        """测试Hugging Face模型连接"""
        # 这里可以实现Hugging Face模型的测试逻辑
        return True, "Hugging Face模型测试暂未实现"

    def _test_openai_compatible_model(self, model: LocalModel) -> Tuple[bool, str]:
        """测试OpenAI兼容模型连接"""
        # 这里可以实现OpenAI兼容API的测试逻辑
        return True, "OpenAI兼容模型测试暂未实现"

    def add_model(self, model: LocalModel):
        """添加新模型"""
        self.models[model.name] = model
        self.save_config()
        self.logger.info(f"添加新模型: {model.name}")

    def remove_model(self, name: str):
        """移除模型"""
        if name in self.models:
            del self.models[name]
            self.save_config()
            self.logger.info(f"移除模型: {name}")

    def refresh_models(self):
        """刷新模型状态"""
        self.detect_models()


# 全局实例
_local_model_manager = None

def get_local_model_manager() -> LocalModelManager:
    """获取全局本地模型管理器实例"""
    global _local_model_manager
    if _local_model_manager is None:
        _local_model_manager = LocalModelManager()
    return _local_model_manager
