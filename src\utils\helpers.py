
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
辅助工具模块
提供各种通用工具类和函数
"""

import time
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable, Generator
import yaml
import json
import shutil
import hashlib
from datetime import datetime
import re
from tqdm import tqdm
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class Timer:
    """计时器类"""

    def __init__(self, name: str = ""):
        """
        初始化计时器

        Args:
            name: 计时器名称
        """
        self.name = name
        self.start_time = None
        self.end_time = None
        self.elapsed = None

    def __enter__(self) -> 'Timer':
        """开始计时"""
        self.start_time = time.time()
        return self

    def __exit__(self, *args):
        """结束计时"""
        self.end_time = time.time()
        self.elapsed = self.end_time - self.start_time

    def __str__(self) -> str:
        """返回计时结果字符串"""
        if self.elapsed is None:
            return f"Timer '{self.name}' hasn't completed"
        return f"Timer '{self.name}' took {self.elapsed:.3f} seconds"

class ProgressBar:
    """进度条类"""

    def __init__(self, total: int, desc: str = "", unit: str = "it"):
        """
        初始化进度条

        Args:
            total: 总数
            desc: 描述
            unit: 单位
        """
        self.pbar = tqdm(total=total, desc=desc, unit=unit)

    def update(self, n: int = 1) -> None:
        """更新进度"""
        self.pbar.update(n)

    def close(self) -> None:
        """关闭进度条"""
        self.pbar.close()

    def __enter__(self) -> tqdm:
        """进入上下文"""
        return self.pbar

    def __exit__(self, *args):
        """退出上下文"""
        self.close()

class FileUtils:
    """文件操作工具类"""

    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """
        确保目录存在

        Args:
            path: 目录路径

        Returns:
            Path: 目录路径对象
        """
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path

    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """
        计算文件哈希值

        Args:
            file_path: 文件路径
            algorithm: 哈希算法

        Returns:
            str: 哈希值
        """
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()

    @staticmethod
    def safe_file_write(file_path: Union[str, Path], content: str,
                       backup: bool = True) -> bool:
        """
        安全地写入文件

        Args:
            file_path: 文件路径
            content: 文件内容
            backup: 是否创建备份

        Returns:
            bool: 是否成功写入
        """
        file_path = Path(file_path)
        try:
            # 创建临时文件
            temp_path = file_path.with_suffix('.tmp')

            # 如果需要备份且文件存在
            if backup and file_path.exists():
                backup_path = file_path.with_suffix('.bak')
                shutil.copy2(file_path, backup_path)

            # 写入临时文件
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # 重命名临时文件
            temp_path.replace(file_path)
            return True

        except Exception as e:
            print(f"写入文件时出错: {e}")
            return False

    @staticmethod
    def find_files(directory: Union[str, Path], pattern: str = "*",
                   recursive: bool = True) -> Generator[Path, None, None]:
        """
        查找文件

        Args:
            directory: 目录路径
            pattern: 文件模式
            recursive: 是否递归查找

        Yields:
            Path: 文件路径
        """
        path = Path(directory)
        if recursive:
            yield from path.rglob(pattern)
        else:
            yield from path.glob(pattern)

    @staticmethod
    def batch_process_files(directory: Union[str, Path],
                          processor: Callable[[Path], Any],
                          pattern: str = "*",
                          max_workers: int = None) -> Dict[Path, Any]:
        """
        批量处理文件

        Args:
            directory: 目录路径
            processor: 处理函数
            pattern: 文件模式
            max_workers: 最大工作线程数

        Returns:
            Dict[Path, Any]: 处理结果字典
        """
        results = {}
        files = list(FileUtils.find_files(directory, pattern))

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(processor, f): f for f in files}

            with tqdm(total=len(files), desc="Processing files") as pbar:
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        results[file_path] = future.result()
                    except Exception as e:
                        results[file_path] = e
                    pbar.update(1)

        return results

class TextUtils:
    """文本处理工具类"""

    @staticmethod
    def normalize_text(text: str) -> str:
        """
        标准化文本

        Args:
            text: 输入文本

        Returns:
            str: 标准化后的文本
        """
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')

        # 删除重复的空行
        text = re.sub(r'\n{3,}', '\n\n', text)

        # 清理行首尾的空白字符
        text = '\n'.join(line.strip() for line in text.splitlines())

        return text.strip()

    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """
        提取URL

        Args:
            text: 输入文本

        Returns:
            List[str]: URL列表
        """
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(url_pattern, text)

    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """
        截断文本

        Args:
            text: 输入文本
            max_length: 最大长度
            suffix: 后缀

        Returns:
            str: 截断后的文本
        """
        if len(text) <= max_length:
            return text

        return text[:max_length - len(suffix)] + suffix

    @staticmethod
    def split_text(text: str, chunk_size: int, overlap: int = 0) -> List[str]:
        """
        分割文本

        Args:
            text: 输入文本
            chunk_size: 分块大小
            overlap: 重叠大小

        Returns:
            List[str]: 文本块列表
        """
        if chunk_size <= 0:
            return [text]

        # 如果文本长度小于chunk_size，直接返回
        if len(text) <= chunk_size:
            return [text]

        # 首先按段落分割文本
        paragraphs = []
        for para in text.split('\n\n'):
            if para.strip():
                paragraphs.append(para.strip())

        # 如果段落数量很少，直接返回段落
        if len(paragraphs) <= 3 and sum(len(p) for p in paragraphs) <= chunk_size * 3:
            return paragraphs

        # 对长段落进行进一步分割
        chunks = []
        current_chunk = ""
        current_length = 0

        # 限制最大块数，避免内存溢出
        max_chunks = 1000

        for para in paragraphs:
            # 如果段落本身超过chunk_size，需要进一步分割
            if len(para) > chunk_size:
                # 先处理当前累积的chunk
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""
                    current_length = 0

                # 分割句子
                sentences = []
                # 中英文句子边界
                for sep in ['. ', '? ', '! ', '。', '？', '！', '；', ';']:
                    if sep in para:
                        parts = para.split(sep)
                        for i, part in enumerate(parts[:-1]):
                            sentences.append(part + sep)
                        if parts[-1]:
                            sentences.append(parts[-1])
                        break
                else:
                    # 如果没有找到句子边界，按行分割
                    sentences = para.split('\n')
                    if len(sentences) == 1:
                        # 如果没有行边界，按空格分割
                        words = para.split(' ')
                        temp_sentence = ""
                        for word in words:
                            if len(temp_sentence) + len(word) + 1 > chunk_size:
                                sentences.append(temp_sentence)
                                temp_sentence = word
                            else:
                                if temp_sentence:
                                    temp_sentence += " " + word
                                else:
                                    temp_sentence = word
                        if temp_sentence:
                            sentences.append(temp_sentence)

                # 组合句子成块
                temp_chunk = ""
                for sentence in sentences:
                    if len(temp_chunk) + len(sentence) + 1 > chunk_size:
                        if temp_chunk:
                            chunks.append(temp_chunk)
                        temp_chunk = sentence
                    else:
                        if temp_chunk:
                            temp_chunk += " " + sentence
                        else:
                            temp_chunk = sentence

                if temp_chunk:
                    chunks.append(temp_chunk)
            else:
                # 如果添加当前段落会超过chunk_size，先保存当前chunk
                if current_length + len(para) + 2 > chunk_size:
                    chunks.append(current_chunk)
                    current_chunk = para
                    current_length = len(para)
                else:
                    # 否则，将段落添加到当前chunk
                    if current_chunk:
                        current_chunk += "\n\n" + para
                        current_length += len(para) + 2
                    else:
                        current_chunk = para
                        current_length = len(para)

            # 检查是否达到最大块数
            if len(chunks) >= max_chunks:
                break

        # 添加最后一个chunk
        if current_chunk and len(chunks) < max_chunks:
            chunks.append(current_chunk)

        # 确保所有chunk都不为空
        chunks = [chunk for chunk in chunks if chunk.strip()]

        # 如果需要重叠，创建重叠的块
        if overlap > 0 and len(chunks) > 1:
            overlapped_chunks = []
            for i in range(len(chunks)):
                current = chunks[i]

                # 添加前一个块的结尾
                if i > 0:
                    prev_end = chunks[i-1][-overlap:]
                    if prev_end:
                        current = prev_end + "... " + current

                # 添加后一个块的开头
                if i < len(chunks) - 1:
                    next_start = chunks[i+1][:overlap]
                    if next_start:
                        current = current + "... " + next_start

                overlapped_chunks.append(current)

            chunks = overlapped_chunks

        # 如果没有成功分割，至少返回一个块
        if not chunks:
            chunks = [text[:chunk_size].strip()]

        return chunks

class ConfigUtils:
    """配置管理工具类"""

    @staticmethod
    def load_config(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加载配置文件

        Args:
            file_path: 配置文件路径

        Returns:
            Dict[str, Any]: 配置字典
        """
        file_path = Path(file_path)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix == '.yaml' or file_path.suffix == '.yml':
                    return yaml.safe_load(f)
                elif file_path.suffix == '.json':
                    return json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {file_path.suffix}")

        except Exception as e:
            print(f"加载配置文件时出错: {e}")
            return {}

    @staticmethod
    def save_config(config: Dict[str, Any], file_path: Union[str, Path],
                   format: str = 'yaml') -> bool:
        """
        保存配置文件

        Args:
            config: 配置字典
            file_path: 文件路径
            format: 文件格式

        Returns:
            bool: 是否成功保存
        """
        file_path = Path(file_path)

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if format == 'yaml':
                    yaml.safe_dump(config, f, default_flow_style=False)
                elif format == 'json':
                    json.dump(config, f, indent=2)
                else:
                    raise ValueError(f"Unsupported format: {format}")
            return True

        except Exception as e:
            print(f"保存配置文件时出错: {e}")
            return False

    @staticmethod
    def merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并多个配置字典

        Args:
            *configs: 配置字典列表

        Returns:
            Dict[str, Any]: 合并后的配置字典
        """
        result = {}
        for config in configs:
            ConfigUtils._deep_update(result, config)
        return result

    @staticmethod
    def _deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """
        深度更新字典

        Args:
            base_dict: 基础字典
            update_dict: 更新字典
        """
        for key, value in update_dict.items():
            if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                ConfigUtils._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    @staticmethod
    def load_configs(*file_paths: Union[str, Path]) -> Dict[str, Any]:
        """
        加载多个配置文件并合并
        
        Args:
            file_paths: 配置文件路径列表
        
        Returns:
            Dict[str, Any]: 合并后的配置字典
        """
        merged_config = {}
        
        for file_path in file_paths:
            config = ConfigUtils.load_config(file_path)
            merged_config.update(config)
        
        return merged_config

if __name__ == "__main__":
    # 测试计时器
    with Timer("测试操作") as timer:
        time.sleep(1)
    print(timer)

    # 测试进度条
    with ProgressBar(total=100, desc="Processing") as pbar:
        for _ in range(100):
            time.sleep(0.01)
            pbar.update(1)

    # 测试文件工具
    test_dir = Path("test_files")
    FileUtils.ensure_dir(test_dir)

    # 写入测试文件
    test_file = test_dir / "test.txt"
    FileUtils.safe_file_write(test_file, "Hello, World!")

    # 计算文件哈希
    file_hash = FileUtils.get_file_hash(test_file)
    print(f"File hash: {file_hash}")

    # 测试文本工具
    text = """
    Hello,    World!


    This is a test.
    http://example.com
    """

    normalized = TextUtils.normalize_text(text)
    print(f"Normalized text:\n{normalized}")

    urls = TextUtils.extract_urls(text)
    print(f"URLs: {urls}")

    # 测试配置工具
    config1 = {'a': 1, 'b': {'c': 2}}
    config2 = {'b': {'d': 3}, 'e': 4}

    merged = ConfigUtils.merge_configs(config1, config2)
    print(f"Merged config: {merged}")

    # 清理测试文件
    shutil.rmtree(test_dir)
