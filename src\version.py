
"""
版本信息模块
"""

__version__ = '1.0.0'
__author__ = 'Your Name'
__author_email__ = '<EMAIL>'
__description__ = 'A powerful Markdown document vectorization and processing tool'
__url__ = 'https://github.com/yourusername/md_vector_processor'
__license__ = 'MIT'

# 版本信息详细说明
VERSION_INFO = {
    'major': 1,
    'minor': 0,
    'micro': 0,
    'releaselevel': 'final',
    'serial': 0
}

# 完整版本元组
VERSION_TUPLE = (
    VERSION_INFO['major'],
    VERSION_INFO['minor'],
    VERSION_INFO['micro'],
    VERSION_INFO['releaselevel'],
    VERSION_INFO['serial']
)

# 构建版本字符串
def get_version():
    """获取完整的版本字符串"""
    version = f"{VERSION_INFO['major']}.{VERSION_INFO['minor']}.{VERSION_INFO['micro']}"
    if VERSION_INFO['releaselevel'] != 'final':
        version += f"-{VERSION_INFO['releaselevel']}"
        if VERSION_INFO['serial'] > 0:
            version += f".{VERSION_INFO['serial']}"
    return version

# 版本兼容性信息
MINIMUM_PYTHON_VERSION = (3, 8, 0)
RECOMMENDED_PYTHON_VERSION = (3, 9, 0)

def check_python_version():
    """检查Python版本兼容性"""
    import sys
    if sys.version_info < MINIMUM_PYTHON_VERSION:
        raise RuntimeError(
            f"MD Vector Processor 需要 Python {'.'.join(map(str, MINIMUM_PYTHON_VERSION))} 或更高版本"
        )
    return True