
# MD Vector Processor

一个强大的Markdown文档向量化处理工具，支持批量处理、数据清理、向量化和索引构建。

## 功能特点

- **文档处理**
  - 支持批量处理Markdown文件
  - 智能文本清理和标准化
  - 增强的多语言支持（中文、英文、日语、韩语等多种语言）
  - 专门的代码块处理
  - 多语言混合文档智能识别

- **向量化处理**
  - 集成多种先进语言模型（BERT-MRC、RoBERTa、XLNet、ERNIE等）
  - 领域特化模型（医疗、法律、技术文档、代码）
  - 支持模型微调和自定义
  - 高效的批处理机制

- **索引管理**
  - 支持混合索引（HNSW+IVF）
  - 高级量化技术（PQ、OPQ、ScaNN）
  - 增量索引优化
  - 高效的向量检索
  - GPU加速支持

- **数据存储**
  - 高效的向量存储
  - 元数据管理
  - 自动备份机制

## 系统要求

- Python 3.8+
- CUDA支持（可选，用于GPU加速）
- 足够的磁盘空间用于存储向量数据
- 建议8GB以上RAM

## 安装指南

1. 克隆仓库：
```bash
git clone https://github.com/yourusername/md_vector_processor.git
cd md_vector_processor
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 安装额外的语言模型（可选）：
```bash
python -m spacy download xx_ent_wiki_sm  # 多语言支持
python -m nltk.downloader punkt wordnet stopwords  # NLTK资源
```

5. 安装小语种支持（可选）：
```bash
# 日语支持
pip install mecab-python3 unidic-lite

# 韩语支持
pip install konlpy

# 泰语支持
pip install pythainlp

# 阿拉伯语支持
pip install pyarabic

# 俄语支持
pip install pymorphy2 pymorphy2-dicts-ru
```

6. 安装GPU支持（可选，推荐）：
```bash
# 如果使用CUDA
pip install torch==2.0.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
pip install faiss-gpu

# 如果不使用CUDA
pip install torch
pip install faiss-cpu
```

## 配置说明

主要配置文件位于 `config/config.yaml`，包含以下主要部分：

- **文件处理配置**：设置输入输出目录、批处理大小等
- **预处理配置**：文本清理、语言处理等设置
- **向量化配置**：模型选择、设备设置等
- **索引配置**：索引类型、搜索参数等
- **存储配置**：数据存储、备份策略等
- **系统配置**：性能优化、资源限制等

## 使用示例

### 命令行使用

1. 基本使用：

```python
from md_vector_processor import MDVectorProcessor

# 初始化处理器
processor = MDVectorProcessor()

# 处理单个文件
processor.process_file("path/to/document.md")

# 批量处理目录
processor.process_directory("path/to/documents")

# 搜索相似文档
results = processor.search_similar("query text", k=5)
```

2. 自定义配置：

```python
from md_vector_processor import MDVectorProcessor
from md_vector_processor.utils import ConfigUtils

# 加载自定义配置
config = ConfigUtils.load_config("path/to/custom_config.yaml")

# 使用自定义配置初始化处理器
processor = MDVectorProcessor(config)
```

3. 高级功能：

```python
# 增量更新
processor.update_index("path/to/new_documents")

# 导出向量数据
processor.export_vectors("output_path")

# 性能分析
with processor.profiler():
    processor.process_directory("path/to/documents")
```

### GUI界面使用

运行GUI界面：

```bash
python gui_run.py
```

#### 主要功能区域

GUI界面分为以下几个主要功能区域：

1. **侧边栏**：提供主要功能导航
2. **内容区**：显示当前选中功能的详细内容和操作界面
3. **菜单栏**：提供文件操作、视图设置、语言切换等功能
4. **状态栏**：显示当前状态和操作信息

#### 侧边栏功能按钮

1. **DASHBOARD（仪表盘）**
   - 功能：提供系统概览和关键指标
   - 用法：点击查看系统状态、处理统计、资源使用情况等
   - 主要指标：已处理文档数、索引大小、向量总数、系统资源使用情况

2. **INDEX（索引）**
   - 功能：管理向量索引
   - 用法：创建、更新、优化和管理向量索引
   - 主要操作：
     - 创建新索引：设置索引类型、维度、度量方式等
     - 更新索引：添加新文档到现有索引
     - 优化索引：重建或优化现有索引
     - 导出/导入索引：保存或加载索引文件

3. **VECTORIZE（向量化）**
   - 功能：文档向量化处理
   - 用法：将文档转换为向量表示
   - 主要操作：
     - 选择输入文件/目录：支持单个文件或批量处理目录
     - 选择向量化模型：支持多种预训练模型和自定义模型
     - 设置处理参数：批处理大小、设备选择、并行度等
     - 执行向量化：将文档转换为向量并可选择性保存

4. **SEARCH（搜索）**
   - 功能：向量相似度搜索
   - 用法：在向量索引中搜索相似文档
   - 主要操作：
     - 输入查询文本：支持自然语言查询
     - 设置搜索参数：结果数量、相似度阈值等
     - 执行搜索：返回相似度排序的结果列表
     - 结果预览：查看搜索结果的详细内容

5. **VISUALIZE（可视化）**
   - 功能：向量数据可视化
   - 用法：通过图形方式展示向量数据分布和关系
   - 主要操作：
     - 选择可视化类型：散点图、热力图、聚类图等
     - 设置降维方法：PCA、t-SNE、UMAP等
     - 自定义可视化参数：颜色、标签、交互性等
     - 导出可视化结果：保存为图片或交互式HTML

#### 菜单栏功能

1. **文件菜单**
   - 打开文件：选择并打开文档文件
   - 保存文件：保存当前编辑的内容
   - 另存为：将当前内容保存到新文件
   - 退出：关闭应用程序

2. **编辑菜单**（待实现）
   - 复制、粘贴、剪切等基本编辑功能
   - 撤销、重做操作

3. **视图菜单**
   - 主题：选择界面主题（系统、亮色、暗色、蓝色、绿色、红色、紫色、橙色、青色、赛博朋克、矩阵）
   - 布局设置（待实现）

4. **工具菜单**（待实现）
   - 批处理工具
   - 数据导入/导出
   - 系统设置

5. **语言菜单**
   - 支持多种语言界面（中文、英文、日语、韩语、俄语、西班牙语、法语、德语）

6. **帮助菜单**
   - 关于：显示应用程序信息

## 性能优化建议

1. **内存使用**
   - 调整批处理大小
   - 启用内存效率模式
   - 使用向量压缩

2. **GPU加速**
   - 确保CUDA正确安装
   - 调整GPU内存使用比例
   - 优化批处理大小

3. **存储优化**
   - 启用数据压缩
   - 定期清理缓存
   - 使用SSD存储

## 常见问题解答

1. **内存不足**
   - 减小批处理大小
   - 启用内存效率模式
   - 增加系统交换空间

2. **处理速度慢**
   - 检查GPU是否正确使用
   - 优化批处理参数
   - 调整工作线程数

3. **索引性能问题**
   - 选择合适的索引类型
   - 调整索引参数
   - 定期重建索引

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
- 项目主页：[GitHub Repository URL]

## 致谢

感谢以下开源项目的支持：

- [sentence-transformers](https://github.com/UKPLab/sentence-transformers)
- [FAISS](https://github.com/facebookresearch/faiss)
- [HNSWlib](https://github.com/nmslib/hnswlib)
- [其他依赖项目]

## 更新日志

### v2.0.0 (2024-06)
- **向量化模型升级**
  - 添加多模型支持（BERT-MRC、RoBERTa、XLNet、ERNIE等）
  - 实现领域特化模型（医疗、法律、技术文档、代码）
  - 添加模型微调接口
- **索引性能提升**
  - 实现混合索引（HNSW+IVF）
  - 添加高级量化技术（PQ、OPQ、ScaNN）
  - 优化增量索引更新机制
- **多语言支持增强**
  - 扩展对小语种的支持
  - 添加代码块专门处理功能
  - 实现多语言混合文档智能识别
- **性能优化**
  - 添加性能优化脚本
  - 增强GPU加速支持
  - 优化内存使用

### v1.0.0 (2024-01)
- 初始版本发布
- 实现核心功能
- 添加基本文档

## 新功能文档

详细的新功能文档请参阅 [新功能文档](docs/new_features.md)，包括：
- 向量化模型升级详情
- 索引性能提升说明
- 多语言支持增强指南
- 新配置选项参考
- 性能优化和GPU加速指南
- 测试新功能的方法

## 本地化大模型调用

本程序支持调用本地部署的大模型，包括通过Ollama部署的模型。

### 配置本地大模型

1. **通过设置页面配置**
   - 导航到：点击侧边栏的"SETTINGS"按钮
   - 在"模型设置"选项卡中，选择"使用本地模型"
   - 配置本地模型参数：
     - 模型类型：选择模型类型（Ollama、本地Hugging Face等）
     - 模型名称：输入模型名称（如"llama2"、"mistral"等）
     - 模型路径/API地址：
       - Ollama模型：输入API地址（默认为"http://localhost:11434/api"）
       - 本地Hugging Face模型：输入模型文件夹路径
     - 模型参数：根据需要调整温度、上下文长度等参数

2. **通过配置文件配置**
   - 编辑`config/models.yaml`文件
   - 添加或修改本地模型配置：
   ```yaml
   local_models:
     ollama:
       enabled: true
       api_url: "http://localhost:11434/api"
       default_model: "llama2"
       models:
         - name: "llama2"
           parameters:
             temperature: 0.7
             max_tokens: 2048
         - name: "mistral"
           parameters:
             temperature: 0.5
             max_tokens: 4096
     huggingface:
       enabled: true
       models_dir: "models/huggingface"
       models:
         - name: "bert-base-uncased"
           parameters:
             max_length: 512
   ```

### 使用本地大模型

1. **向量化过程中使用**
   - 在向量化页面，选择"模型"下拉菜单
   - 选择已配置的本地模型
   - 调整参数（如有必要）
   - 点击"开始向量化"按钮

2. **搜索过程中使用**
   - 在搜索页面，点击"高级选项"
   - 在"使用模型"下拉菜单中选择本地模型
   - 调整搜索参数
   - 执行搜索

### Ollama模型支持

1. **安装Ollama**
   - 访问[Ollama官网](https://ollama.ai/)下载并安装
   - 或通过命令行安装：
   ```bash
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **拉取模型**
   ```bash
   ollama pull llama2
   ollama pull mistral
   # 或其他支持的模型
   ```

3. **启动Ollama服务**
   ```bash
   ollama serve
   ```

4. **在应用中配置Ollama**
   - 按照上述配置步骤设置Ollama API地址和模型名称

## 向量化文件存储

### 存储位置

向量化文件默认存储在以下位置：
- 向量数据：`data/vectors/`
- 索引文件：`data/indices/`
- 元数据：`data/metadata/`

### 存储设置

1. **存储大小限制**
   - 默认情况下，向量存储没有硬性大小限制
   - 可以在设置中配置存储限制：
     - 导航到设置页面
     - 在"存储设置"选项卡中设置最大存储大小
     - 设置自动清理策略（最早的先删除、最少使用的先删除等）

2. **长期保存**
   - 向量数据会长期保存在存储位置
   - 可以通过以下方式管理：
     - 手动备份：使用"导出"功能将向量数据导出到指定位置
     - 自动备份：在设置中启用自动备份功能，设置备份频率和保留策略
     - 数据迁移：使用"导入/导出"功能将数据迁移到新位置

3. **存储格式**
   - 向量数据：二进制格式（.bin）或NumPy格式（.npy）
   - 索引文件：根据索引类型不同（FAISS、HNSW等）有不同的格式
   - 元数据：JSON格式（.json）

4. **自定义存储位置**
   - 在设置页面的"存储设置"选项卡中修改存储路径
   - 或编辑`config/storage.yaml`文件：
   ```yaml
   storage:
     vectors_dir: "custom/path/to/vectors"
     indices_dir: "custom/path/to/indices"
     metadata_dir: "custom/path/to/metadata"
     max_size_gb: 50  # 设置最大存储大小（GB）
     cleanup_policy: "oldest_first"  # 清理策略
   ```