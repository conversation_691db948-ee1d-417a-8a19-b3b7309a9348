
global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/your-webhook-url'  # 替换为实际的Slack Webhook URL

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  
  # 子路由
  routes:
  - match:
      severity: critical
    receiver: 'slack-critical'
    continue: true
  
  - match:
      severity: warning
    receiver: 'slack-warnings'
    continue: true
  
  - match_re:
      service: ^.*$
    receiver: 'slack-notifications'

# 接收器配置
receivers:
- name: 'slack-notifications'
  slack_configs:
  - channel: '#monitoring'
    send_resolved: true
    icon_url: 'https://avatars3.githubusercontent.com/u/3380462'
    title: |-
      [{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .CommonLabels.alertname }}
    text: >-
      {{ range .Alerts -}}
      *Alert:* {{ .Annotations.summary }}
      *Description:* {{ .Annotations.description }}
      *Severity:* {{ .Labels.severity }}
      *Instance:* {{ .Labels.instance }}
      *Details:*
      {{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
      {{ end }}
      {{ end }}

- name: 'slack-critical'
  slack_configs:
  - channel: '#alerts-critical'
    send_resolved: true
    icon_emoji: ':red_circle:'
    title: |-
      [CRITICAL] {{ .CommonLabels.alertname }}
    text: >-
      {{ range .Alerts -}}
      *Alert:* {{ .Annotations.summary }}
      *Description:* {{ .Annotations.description }}
      *Instance:* {{ .Labels.instance }}
      *Details:*
      {{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
      {{ end }}
      {{ end }}

- name: 'slack-warnings'
  slack_configs:
  - channel: '#alerts-warnings'
    send_resolved: true
    icon_emoji: ':warning:'
    title: |-
      [WARNING] {{ .CommonLabels.alertname }}
    text: >-
      {{ range .Alerts -}}
      *Alert:* {{ .Annotations.summary }}
      *Description:* {{ .Annotations.description }}
      *Instance:* {{ .Labels.instance }}
      *Details:*
      {{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
      {{ end }}
      {{ end }}

# 抑制规则
inhibit_rules:
  # 当有critical级别告警时，抑制相关的warning级别告警
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']

# 模板配置
templates:
  - '/etc/alertmanager/template/*.tmpl'