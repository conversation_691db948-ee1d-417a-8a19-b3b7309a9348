#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
汽车电器领域训练数据自动化处理和索引训练流程
"""

import os
import sys
import json
import logging
import hashlib
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import numpy as np
from tqdm import tqdm

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutomotiveTrainingPipeline:
    """汽车电器领域训练数据处理和索引训练管道"""
    
    def __init__(self, config_path: str = "training_config.json"):
        """初始化训练管道"""
        self.config = self._load_config(config_path)
        self.setup_directories()
        
        # 训练统计
        self.stats = {
            'start_time': datetime.now(),
            'documents_processed': 0,
            'vectors_generated': 0,
            'indices_trained': 0,
            'errors': []
        }
        
        # 汽车电器领域术语库
        self.automotive_terms = self._load_automotive_terms()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "data_directories": {
                "raw_documents": "training_data/raw_documents",
                "processed_documents": "training_data/processed_documents", 
                "training_vectors": "training_data/training_vectors",
                "models": "training_data/models"
            },
            "document_categories": {
                "national_standards": {"priority": 1, "target_count": 300},
                "international_standards": {"priority": 1, "target_count": 400},
                "industry_standards": {"priority": 2, "target_count": 200},
                "enterprise_standards": {"priority": 2, "target_count": 150},
                "technical_docs": {"priority": 3, "target_count": 300}
            },
            "chunking": {
                "standard_document": {
                    "chunk_size": 800,
                    "overlap": 100,
                    "min_chunk_size": 200,
                    "max_chunk_size": 1200
                },
                "technical_specification": {
                    "chunk_size": 600,
                    "overlap": 80,
                    "min_chunk_size": 150,
                    "max_chunk_size": 1000
                }
            },
            "training": {
                "ivf": {
                    "min_vectors": 1000,
                    "recommended_vectors": 10000,
                    "nlist_ratio": 0.1,  # nlist = sqrt(n_vectors) * ratio
                    "nprobe": 32
                },
                "pq": {
                    "min_vectors": 10000,
                    "recommended_vectors": 100000,
                    "m": 8,
                    "bits": 8
                }
            },
            "vectorization": {
                "model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "batch_size": 32,
                "device": "cpu"
            }
        }
        
        if Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        else:
            # 保存默认配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            logger.info(f"创建默认配置文件: {config_path}")
        
        return default_config
    
    def _load_automotive_terms(self) -> Dict[str, List[str]]:
        """加载汽车电器领域术语库"""
        return {
            'electrical_components': [
                'ECU', '电控单元', '传感器', '执行器', '继电器', '保险丝',
                '线束', '连接器', '开关', '电机', '发电机', '蓄电池',
                '充电桩', '逆变器', '变压器', '电容器', '电阻器'
            ],
            'automotive_systems': [
                '发动机管理系统', '制动系统', '转向系统', '悬架系统',
                '空调系统', '照明系统', '信息娱乐系统', '导航系统',
                '安全气囊', '防抱死制动', '电子稳定程序'
            ],
            'standards_terms': [
                '技术要求', '试验方法', '检验规则', '标志包装',
                '安全要求', '性能指标', '环境条件', '可靠性',
                '功能安全', '电磁兼容', '环境适应性'
            ],
            'testing_terms': [
                '型式试验', '出厂试验', '抽样检验', '耐久性试验',
                '环境试验', '振动试验', '温度试验', '湿度试验',
                '盐雾试验', '冲击试验', '跌落试验'
            ]
        }
    
    def setup_directories(self):
        """创建必要的目录结构"""
        directories = [
            self.config["data_directories"]["raw_documents"],
            self.config["data_directories"]["processed_documents"],
            self.config["data_directories"]["training_vectors"],
            self.config["data_directories"]["models"],
            f"{self.config['data_directories']['raw_documents']}/national_standards",
            f"{self.config['data_directories']['raw_documents']}/international_standards",
            f"{self.config['data_directories']['raw_documents']}/industry_standards",
            f"{self.config['data_directories']['raw_documents']}/enterprise_standards",
            f"{self.config['data_directories']['raw_documents']}/technical_docs",
            f"{self.config['data_directories']['processed_documents']}/cleaned",
            f"{self.config['data_directories']['processed_documents']}/chunked",
            f"{self.config['data_directories']['processed_documents']}/metadata",
            f"{self.config['data_directories']['training_vectors']}/ivf_training",
            f"{self.config['data_directories']['training_vectors']}/pq_training",
            f"{self.config['data_directories']['training_vectors']}/validation",
            f"{self.config['data_directories']['models']}/ivf_indices",
            f"{self.config['data_directories']['models']}/pq_indices"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        
        logger.info("目录结构创建完成")
    
    def scan_documents(self) -> Dict[str, List[Path]]:
        """扫描并分类文档"""
        logger.info("🔍 扫描训练文档...")
        
        documents = {}
        raw_dir = Path(self.config["data_directories"]["raw_documents"])
        
        for category in self.config["document_categories"].keys():
            category_dir = raw_dir / category
            if category_dir.exists():
                # 支持的文件格式
                supported_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.html'}
                
                files = []
                for ext in supported_extensions:
                    files.extend(list(category_dir.rglob(f'*{ext}')))
                
                documents[category] = files
                logger.info(f"发现 {category}: {len(files)} 个文档")
            else:
                documents[category] = []
                logger.warning(f"目录不存在: {category_dir}")
        
        total_docs = sum(len(files) for files in documents.values())
        logger.info(f"总共发现 {total_docs} 个文档")
        
        return documents
    
    def process_documents(self, documents: Dict[str, List[Path]]) -> List[Dict]:
        """处理文档并生成训练数据"""
        logger.info("📄 处理文档...")
        
        all_chunks = []
        
        for category, files in documents.items():
            if not files:
                continue
                
            logger.info(f"处理 {category} 类别的 {len(files)} 个文档")
            
            for file_path in tqdm(files, desc=f"处理{category}"):
                try:
                    chunks = self._process_single_document(file_path, category)
                    all_chunks.extend(chunks)
                    self.stats['documents_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"处理文档失败 {file_path}: {str(e)}"
                    logger.error(error_msg)
                    self.stats['errors'].append(error_msg)
        
        logger.info(f"文档处理完成，生成 {len(all_chunks)} 个文本块")
        return all_chunks
    
    def _process_single_document(self, file_path: Path, category: str) -> List[Dict]:
        """处理单个文档"""
        try:
            # 加载文档
            from src.utils.document_loader import create_document_loader
            
            loader = create_document_loader()
            documents, doc_infos = loader.load_documents([str(file_path)])
            
            if str(file_path) not in documents:
                raise Exception("文档加载失败")
            
            content = documents[str(file_path)]
            doc_info = doc_infos[0] if doc_infos else None
            
            # 文档质量检查
            if not self._validate_document_quality(content, file_path):
                raise Exception("文档质量检查失败")
            
            # 智能分块
            chunks = self._intelligent_chunking(content, category)
            
            # 生成元数据
            processed_chunks = []
            for i, chunk in enumerate(chunks):
                chunk_data = {
                    'text': chunk,
                    'document_path': str(file_path),
                    'document_category': category,
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'file_size': doc_info.size if doc_info else 0,
                    'encoding': doc_info.encoding if doc_info else 'unknown',
                    'automotive_relevance': self._calculate_automotive_relevance(chunk),
                    'technical_terms': self._extract_technical_terms(chunk),
                    'chunk_id': self._generate_chunk_id(file_path, i),
                    'processing_time': datetime.now().isoformat()
                }
                processed_chunks.append(chunk_data)
            
            return processed_chunks
            
        except Exception as e:
            logger.error(f"处理文档 {file_path} 时出错: {e}")
            return []
    
    def _validate_document_quality(self, content: str, file_path: Path) -> bool:
        """验证文档质量"""
        # 基本长度检查
        if len(content) < 1000:
            logger.warning(f"文档过短: {file_path}")
            return False
        
        # 汽车电器相关性检查
        relevance_score = self._calculate_automotive_relevance(content)
        if relevance_score < 0.1:
            logger.warning(f"文档相关性需要进一步评估: {file_path}")
            return False
        
        # 编码质量检查
        if '�' in content or content.count('?') > len(content) * 0.05:
            logger.warning(f"文档可能存在编码问题: {file_path}")
            return False
        
        return True
    
    def _calculate_automotive_relevance(self, text: str) -> float:
        """计算文档与汽车电器的相关性"""
        text_lower = text.lower()
        total_terms = 0
        found_terms = 0
        
        for category, terms in self.automotive_terms.items():
            for term in terms:
                total_terms += 1
                if term.lower() in text_lower:
                    found_terms += 1
        
        return found_terms / total_terms if total_terms > 0 else 0.0
    
    def _extract_technical_terms(self, text: str) -> List[str]:
        """提取技术术语"""
        found_terms = []
        text_lower = text.lower()
        
        for category, terms in self.automotive_terms.items():
            for term in terms:
                if term.lower() in text_lower:
                    found_terms.append(term)
        
        return list(set(found_terms))
    
    def _intelligent_chunking(self, content: str, category: str) -> List[str]:
        """智能分块"""
        from src.utils.helpers import TextUtils
        
        # 根据文档类别选择分块策略
        if 'standard' in category:
            chunk_config = self.config["chunking"]["standard_document"]
        else:
            chunk_config = self.config["chunking"]["technical_specification"]
        
        chunks = TextUtils.split_text(
            content,
            chunk_size=chunk_config["chunk_size"],
            overlap=chunk_config["overlap"]
        )
        
        # 过滤过短或过长的块
        filtered_chunks = []
        for chunk in chunks:
            if (chunk_config["min_chunk_size"] <= len(chunk) <= 
                chunk_config["max_chunk_size"]):
                filtered_chunks.append(chunk)
        
        return filtered_chunks
    
    def _generate_chunk_id(self, file_path: Path, chunk_index: int) -> str:
        """生成块ID"""
        file_hash = hashlib.md5(str(file_path).encode()).hexdigest()[:8]
        return f"{file_hash}_{chunk_index:04d}"
    
    def vectorize_chunks(self, chunks: List[Dict]) -> Tuple[np.ndarray, np.ndarray, List[Dict]]:
        """向量化文本块"""
        logger.info("🔢 向量化文本块...")
        
        try:
            from src.vectorizer import TextEmbedding
            
            # 创建嵌入器
            config = {'vectorization': self.config["vectorization"]}
            embedder = TextEmbedding(config)
            
            # 批量向量化
            texts = [chunk['text'] for chunk in chunks]
            batch_size = self.config["vectorization"]["batch_size"]
            
            all_vectors = []
            all_ids = []
            
            for i in tqdm(range(0, len(texts), batch_size), desc="向量化"):
                batch_texts = texts[i:i + batch_size]
                batch_vectors = []
                
                for text in batch_texts:
                    vector = embedder.encode_text(text)
                    batch_vectors.append(vector)
                
                if batch_vectors:
                    batch_array = np.vstack(batch_vectors)
                    all_vectors.append(batch_array)
                    
                    # 生成ID
                    batch_ids = [int(hashlib.md5((text + str(i)).encode()).hexdigest(), 16) % (10 ** 10) 
                                for j, text in enumerate(batch_texts)]
                    all_ids.extend(batch_ids)
            
            if all_vectors:
                vectors_array = np.vstack(all_vectors)
                ids_array = np.array(all_ids)
                
                # 更新块数据
                for i, chunk in enumerate(chunks):
                    if i < len(all_ids):
                        chunk['vector_id'] = all_ids[i]
                
                self.stats['vectors_generated'] = len(vectors_array)
                logger.info(f"成功生成 {len(vectors_array)} 个向量，维度: {vectors_array.shape[1]}")
                
                return vectors_array, ids_array, chunks
            else:
                raise Exception("向量化失败")
                
        except Exception as e:
            logger.error(f"向量化过程出错: {e}")
            raise
    
    def train_indices(self, vectors: np.ndarray, vector_ids: np.ndarray) -> Dict[str, str]:
        """训练索引"""
        logger.info("🏗️ 训练索引...")
        
        trained_indices = {}
        n_vectors = len(vectors)
        
        try:
            import faiss
            
            # 训练IVF索引
            if n_vectors >= self.config["training"]["ivf"]["min_vectors"]:
                logger.info(f"训练IVF索引 (向量数: {n_vectors})")
                
                # 计算nlist
                nlist = max(1, int(np.sqrt(n_vectors) * self.config["training"]["ivf"]["nlist_ratio"]))
                nlist = min(nlist, n_vectors // 10)  # 确保每个聚类至少有10个向量
                
                # 创建IVF索引
                dimension = vectors.shape[1]
                quantizer = faiss.IndexFlatIP(dimension)
                ivf_index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
                
                # 训练索引
                logger.info(f"开始训练IVF索引 (nlist={nlist})")
                start_time = time.time()
                ivf_index.train(vectors)
                training_time = time.time() - start_time
                
                # 添加向量
                ivf_index.add(vectors)
                
                # 设置搜索参数
                ivf_index.nprobe = min(self.config["training"]["ivf"]["nprobe"], nlist)
                
                # 保存索引
                ivf_path = f"{self.config['data_directories']['models']}/ivf_indices/automotive_ivf_{datetime.now().strftime('%Y%m%d_%H%M%S')}.idx"
                faiss.write_index(ivf_index, ivf_path)
                
                trained_indices['ivf'] = ivf_path
                self.stats['indices_trained'] += 1
                
                logger.info(f"✅ IVF索引训练完成，耗时: {training_time:.2f}秒，保存至: {ivf_path}")
            
            # 训练PQ索引
            if n_vectors >= self.config["training"]["pq"]["min_vectors"]:
                logger.info(f"训练PQ索引 (向量数: {n_vectors})")
                
                # 创建PQ索引
                dimension = vectors.shape[1]
                m = self.config["training"]["pq"]["m"]
                bits = self.config["training"]["pq"]["bits"]
                
                pq_index = faiss.IndexPQ(dimension, m, bits)
                
                # 训练索引
                logger.info(f"开始训练PQ索引 (m={m}, bits={bits})")
                start_time = time.time()
                pq_index.train(vectors)
                training_time = time.time() - start_time
                
                # 添加向量
                pq_index.add(vectors)
                
                # 保存索引
                pq_path = f"{self.config['data_directories']['models']}/pq_indices/automotive_pq_{datetime.now().strftime('%Y%m%d_%H%M%S')}.idx"
                faiss.write_index(pq_index, pq_path)
                
                trained_indices['pq'] = pq_path
                self.stats['indices_trained'] += 1
                
                logger.info(f"✅ PQ索引训练完成，耗时: {training_time:.2f}秒，保存至: {pq_path}")
            
            return trained_indices
            
        except Exception as e:
            logger.error(f"索引训练失败: {e}")
            raise
    
    def save_training_data(self, chunks: List[Dict], vectors: np.ndarray, vector_ids: np.ndarray):
        """保存训练数据"""
        logger.info("💾 保存训练数据...")
        
        try:
            # 保存向量数据
            vectors_path = f"{self.config['data_directories']['training_vectors']}/automotive_vectors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.npz"
            np.savez_compressed(
                vectors_path,
                vectors=vectors,
                vector_ids=vector_ids
            )
            
            # 保存元数据
            metadata_path = f"{self.config['data_directories']['processed_documents']}/metadata/automotive_metadata_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(chunks, f, ensure_ascii=False, indent=2)
            
            logger.info(f"训练数据已保存: {vectors_path}, {metadata_path}")
            
        except Exception as e:
            logger.error(f"保存训练数据失败: {e}")
            raise
    
    def run_full_pipeline(self) -> Dict:
        """运行完整的训练管道"""
        logger.info("🚀 开始汽车电器领域训练数据自动化处理...")
        
        try:
            # 1. 扫描文档
            documents = self.scan_documents()
            
            # 2. 处理文档
            chunks = self.process_documents(documents)
            
            if not chunks:
                raise Exception("没有成功处理的文档")
            
            # 3. 向量化
            vectors, vector_ids, processed_chunks = self.vectorize_chunks(chunks)
            
            # 4. 训练索引
            trained_indices = self.train_indices(vectors, vector_ids)
            
            # 5. 保存数据
            self.save_training_data(processed_chunks, vectors, vector_ids)
            
            # 6. 生成报告
            self.stats['end_time'] = datetime.now()
            self.stats['total_time'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            self.stats['trained_indices'] = trained_indices
            
            self._generate_report()
            
            logger.info("🎉 训练管道执行完成!")
            return self.stats
            
        except Exception as e:
            logger.error(f"训练管道执行失败: {e}")
            self.stats['pipeline_error'] = str(e)
            return self.stats
    
    def _generate_report(self):
        """生成训练报告"""
        report_path = f"automotive_training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2, default=str)
        
        # 打印摘要
        print("\n" + "="*80)
        print("🚗 汽车电器领域训练管道执行报告")
        print("="*80)
        print(f"执行时间: {self.stats.get('total_time', 0):.2f} 秒")
        print(f"处理文档: {self.stats['documents_processed']} 个")
        print(f"生成向量: {self.stats['vectors_generated']} 个")
        print(f"训练索引: {self.stats['indices_trained']} 个")
        print(f"错误数量: {len(self.stats['errors'])}")
        
        if self.stats.get('trained_indices'):
            print("\n训练完成的索引:")
            for index_type, path in self.stats['trained_indices'].items():
                print(f"  {index_type.upper()}: {path}")
        
        print(f"\n详细报告: {report_path}")
        print("="*80)

if __name__ == "__main__":
    # 创建并运行训练管道
    pipeline = AutomotiveTrainingPipeline()
    results = pipeline.run_full_pipeline()
