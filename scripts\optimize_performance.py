#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能优化脚本
用于优化混合索引和量化技术的性能
"""

import sys
import os
import logging
import time
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import yaml
import argparse
import json
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.vectorizer.embeddings import TextEmbedding
from src.indexer.builder import IndexBuilder

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config(config_path='config/config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config, config_path='config/config.yaml'):
    """保存配置文件"""
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

def benchmark_index_types(config, dimension=384, n_vectors=10000, n_queries=100):
    """
    对不同索引类型进行基准测试
    
    Args:
        config: 配置字典
        dimension: 向量维度
        n_vectors: 测试向量数量
        n_queries: 查询次数
    
    Returns:
        dict: 基准测试结果
    """
    logger.info(f"开始索引类型基准测试 (维度: {dimension}, 向量数: {n_vectors}, 查询数: {n_queries})")
    
    # 测试不同的索引类型和量化方法
    index_types = ['flat', 'ivf', 'hnsw', 'hybrid']
    quantization_types = ['none', 'pq', 'opq']
    
    # 生成测试向量
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
    query_vectors = np.random.rand(n_queries, dimension).astype('float32')
    
    results = {}
    
    for idx_type in index_types:
        for quant_type in quantization_types:
            # 跳过不兼容的组合
            if (idx_type in ['pq', 'opq'] and quant_type != 'none'):
                continue
                
            # 更新配置
            config['indexing']['index_type'] = idx_type
            config['indexing']['quantization'] = quant_type
            
            try:
                logger.info(f"测试 {idx_type} 索引 + {quant_type} 量化:")
                
                # 创建索引构建器
                builder = IndexBuilder(config)
                
                # 创建索引
                start_time = time.time()
                builder.create_index(dimension)
                create_time = time.time() - start_time
                logger.info(f"  创建索引耗时: {create_time:.4f}秒")
                
                # 添加向量
                start_time = time.time()
                builder.add_vectors(test_vectors)
                add_time = time.time() - start_time
                logger.info(f"  添加向量耗时: {add_time:.4f}秒")
                
                # 优化索引
                start_time = time.time()
                builder.optimize_index()
                optimize_time = time.time() - start_time
                logger.info(f"  优化索引耗时: {optimize_time:.4f}秒")
                
                # 搜索测试
                search_times = []
                for query_vector in tqdm(query_vectors, desc="  执行搜索"):
                    start_time = time.time()
                    builder.search(query_vector, k=10)
                    search_times.append(time.time() - start_time)
                
                avg_search_time = np.mean(search_times)
                logger.info(f"  平均搜索耗时: {avg_search_time:.6f}秒")
                
                # 获取索引统计信息
                stats = builder.get_index_stats()
                mem_usage = stats.get('estimated_memory_usage_mb', 0)
                logger.info(f"  内存使用: {mem_usage:.2f} MB")
                
                # 记录结果
                results[f"{idx_type}_{quant_type}"] = {
                    'create_time': create_time,
                    'add_time': add_time,
                    'optimize_time': optimize_time,
                    'avg_search_time': avg_search_time,
                    'memory_usage': mem_usage
                }
                
            except Exception as e:
                logger.error(f"  测试 {idx_type} + {quant_type} 时出错: {e}")
    
    # 比较结果
    logger.info("\n性能比较:")
    logger.info(f"{'索引类型':20} {'创建时间(秒)':15} {'添加时间(秒)':15} {'优化时间(秒)':15} {'搜索时间(秒)':15} {'内存使用(MB)':15}")
    for idx_name, metrics in results.items():
        logger.info(f"{idx_name:20} {metrics['create_time']:15.4f} {metrics['add_time']:15.4f} {metrics['optimize_time']:15.4f} {metrics['avg_search_time']:15.6f} {metrics['memory_usage']:15.2f}")
    
    # 可视化结果
    visualize_benchmark_results(results, f"benchmark_results_{dimension}d_{n_vectors}v.png")
    
    return results

def optimize_hybrid_index_params(config, dimension=384, n_vectors=10000, n_queries=100):
    """
    优化混合索引参数
    
    Args:
        config: 配置字典
        dimension: 向量维度
        n_vectors: 测试向量数量
        n_queries: 查询次数
    
    Returns:
        dict: 最佳参数
    """
    logger.info(f"开始混合索引参数优化 (维度: {dimension}, 向量数: {n_vectors}, 查询数: {n_queries})")
    
    # 设置索引类型为混合索引
    config['indexing']['index_type'] = 'hybrid'
    config['indexing']['quantization'] = 'pq'  # 使用PQ量化
    
    # 参数网格
    hybrid_modes = ['hnsw_ivf', 'ivf_hnsw']
    n_lists_values = [50, 100, 200, 400]
    n_probes_values = [5, 10, 20, 40]
    ef_search_values = [50, 100, 200, 400]
    pq_m_values = [8, 16, 32, 64]
    
    # 生成测试向量
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
    query_vectors = np.random.rand(n_queries, dimension).astype('float32')
    
    best_params = {}
    best_search_time = float('inf')
    
    # 测试不同参数组合
    for hybrid_mode in hybrid_modes:
        for n_lists in n_lists_values:
            for n_probes in n_probes_values:
                for ef_search in ef_search_values:
                    for pq_m in pq_m_values:
                        # 更新配置
                        config['indexing']['hybrid_mode'] = hybrid_mode
                        config['indexing']['hybrid_n_lists'] = n_lists
                        config['indexing']['hybrid_n_probes'] = n_probes
                        config['indexing']['ef_search'] = ef_search
                        config['indexing']['pq_m'] = pq_m
                        
                        try:
                            logger.info(f"测试参数: mode={hybrid_mode}, n_lists={n_lists}, n_probes={n_probes}, ef_search={ef_search}, pq_m={pq_m}")
                            
                            # 创建索引构建器
                            builder = IndexBuilder(config)
                            
                            # 创建索引
                            builder.create_index(dimension)
                            
                            # 添加向量
                            builder.add_vectors(test_vectors)
                            
                            # 优化索引
                            builder.optimize_index()
                            
                            # 搜索测试
                            search_times = []
                            for query_vector in tqdm(query_vectors[:10], desc="  执行搜索"):  # 只测试部分查询以加快速度
                                start_time = time.time()
                                builder.search(query_vector, k=10)
                                search_times.append(time.time() - start_time)
                            
                            avg_search_time = np.mean(search_times)
                            logger.info(f"  平均搜索耗时: {avg_search_time:.6f}秒")
                            
                            # 获取索引统计信息
                            stats = builder.get_index_stats()
                            mem_usage = stats.get('estimated_memory_usage_mb', 0)
                            logger.info(f"  内存使用: {mem_usage:.2f} MB")
                            
                            # 更新最佳参数
                            if avg_search_time < best_search_time:
                                best_search_time = avg_search_time
                                best_params = {
                                    'hybrid_mode': hybrid_mode,
                                    'hybrid_n_lists': n_lists,
                                    'hybrid_n_probes': n_probes,
                                    'ef_search': ef_search,
                                    'pq_m': pq_m,
                                    'avg_search_time': avg_search_time,
                                    'memory_usage': mem_usage
                                }
                                logger.info(f"  找到新的最佳参数!")
                            
                        except Exception as e:
                            logger.error(f"  测试参数时出错: {e}")
    
    logger.info("\n最佳混合索引参数:")
    for param, value in best_params.items():
        logger.info(f"  {param}: {value}")
    
    return best_params

def visualize_benchmark_results(results, output_file):
    """
    可视化基准测试结果
    
    Args:
        results: 基准测试结果字典
        output_file: 输出文件路径
    """
    # 提取数据
    index_names = list(results.keys())
    search_times = [results[name]['avg_search_time'] * 1000 for name in index_names]  # 转换为毫秒
    memory_usages = [results[name]['memory_usage'] for name in index_names]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 搜索时间图表
    ax1.bar(index_names, search_times)
    ax1.set_title('平均搜索时间')
    ax1.set_xlabel('索引类型')
    ax1.set_ylabel('时间 (毫秒)')
    ax1.set_xticklabels(index_names, rotation=45, ha='right')
    
    # 内存使用图表
    ax2.bar(index_names, memory_usages)
    ax2.set_title('内存使用')
    ax2.set_xlabel('索引类型')
    ax2.set_ylabel('内存 (MB)')
    ax2.set_xticklabels(index_names, rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig(output_file)
    logger.info(f"结果图表已保存到: {output_file}")

def apply_optimized_config(config, best_params, config_path='config/config.yaml'):
    """
    应用优化后的配置
    
    Args:
        config: 原始配置字典
        best_params: 最佳参数字典
        config_path: 配置文件路径
    """
    # 更新配置
    config['indexing']['index_type'] = 'hybrid'
    config['indexing']['quantization'] = 'pq'
    
    for param, value in best_params.items():
        if param not in ['avg_search_time', 'memory_usage']:
            config['indexing'][param] = value
    
    # 保存配置
    save_config(config, config_path)
    logger.info(f"优化后的配置已保存到: {config_path}")

def main():
    parser = argparse.ArgumentParser(description='性能优化脚本')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--action', type=str, choices=['benchmark', 'optimize', 'all'], 
                        default='all', help='要执行的操作')
    parser.add_argument('--dimension', type=int, default=384, help='向量维度')
    parser.add_argument('--n_vectors', type=int, default=10000, help='测试向量数量')
    parser.add_argument('--n_queries', type=int, default=100, help='查询次数')
    parser.add_argument('--output', type=str, default='optimized_config.yaml', help='优化后的配置输出路径')
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 执行操作
    if args.action in ['benchmark', 'all']:
        benchmark_results = benchmark_index_types(config, args.dimension, args.n_vectors, args.n_queries)
        
        # 保存基准测试结果
        with open('benchmark_results.json', 'w') as f:
            json.dump(benchmark_results, f, indent=2)
    
    if args.action in ['optimize', 'all']:
        best_params = optimize_hybrid_index_params(config, args.dimension, args.n_vectors, args.n_queries)
        
        # 应用优化后的配置
        apply_optimized_config(config, best_params, args.output)
    
    logger.info("性能优化完成!")

if __name__ == "__main__":
    main()
