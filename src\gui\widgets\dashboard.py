#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仪表盘小部件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QSizePolicy, QGridLayout
)
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QColor, QPalette

from ..i18n import Translator

class StatCard(QFrame):
    """统计卡片小部件"""

    def __init__(self, title: str, value: str, icon: str = None):
        """
        初始化统计卡片

        Args:
            title: 卡片标题
            value: 卡片值
            icon: 图标名称（可选）
        """
        super().__init__()

        # 设置样式
        self.setObjectName("statCard")
        self.setProperty("class", "card")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setProperty("class", "card-title")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建值标签
        value_label = QLabel(value)
        value_label.setObjectName("cardValue")
        value_label.setProperty("class", "card-content")
        value_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        layout.addWidget(value_label)

        # 存储标签引用
        self.title_label = title_label
        self.value_label = value_label

    def update_data(self, title: str, value: str):
        """
        更新卡片数据

        Args:
            title: 新标题
            value: 新值
        """
        self.title_label.setText(title)

        # 创建动画效果
        self.animation = QPropertyAnimation(self.value_label, b"text")
        self.animation.setDuration(500)
        self.animation.setStartValue(self.value_label.text())
        self.animation.setEndValue(value)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.start()

        self.value_label.setText(value)

class ActionCard(QFrame):
    """操作卡片小部件"""

    def __init__(self, title: str, description: str, button_text: str, icon: str = None):
        """
        初始化操作卡片

        Args:
            title: 卡片标题
            description: 卡片描述
            button_text: 按钮文本
            icon: 图标名称（可选）
        """
        super().__init__()

        # 设置样式
        self.setObjectName("actionCard")
        self.setProperty("class", "card")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setProperty("class", "card-title")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建描述标签
        desc_label = QLabel(description)
        desc_label.setObjectName("cardDescription")
        desc_label.setProperty("class", "card-content")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 创建按钮
        button = QPushButton(button_text)
        button.setObjectName("cardButton")
        button.setProperty("class", "animated-button")
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        layout.addWidget(button)

        # 存储组件引用
        self.title_label = title_label
        self.desc_label = desc_label
        self.button = button

    def update_data(self, title: str, description: str, button_text: str):
        """
        更新卡片数据

        Args:
            title: 新标题
            description: 新描述
            button_text: 新按钮文本
        """
        self.title_label.setText(title)
        self.desc_label.setText(description)
        self.button.setText(button_text)

class DashboardWidget(QWidget):
    """仪表盘小部件"""

    def __init__(self, translator: Translator):
        """
        初始化仪表盘小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("dashboardWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建欢迎标签
        welcome_label = QLabel(self.translator.get_text("welcome_message", "欢迎使用MD向量处理器"))
        welcome_label.setObjectName("welcomeLabel")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        main_layout.addWidget(welcome_label)

        # 创建统计卡片区域
        stats_label = QLabel(self.translator.get_text("statistics", "统计信息"))
        stats_label.setObjectName("sectionLabel")
        stats_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        main_layout.addWidget(stats_label)

        # 统计卡片布局
        stats_layout = QHBoxLayout()

        # 创建统计卡片
        self.vectors_card = StatCard(
            self.translator.get_text("total_vectors", "向量总数"),
            "0"
        )
        self.indices_card = StatCard(
            self.translator.get_text("total_indices", "索引总数"),
            "0"
        )
        self.models_card = StatCard(
            self.translator.get_text("total_models", "模型总数"),
            "0"
        )
        self.queries_card = StatCard(
            self.translator.get_text("total_queries", "查询总数"),
            "0"
        )

        # 添加卡片到布局
        stats_layout.addWidget(self.vectors_card)
        stats_layout.addWidget(self.indices_card)
        stats_layout.addWidget(self.models_card)
        stats_layout.addWidget(self.queries_card)

        main_layout.addLayout(stats_layout)

        # 创建操作卡片区域
        actions_label = QLabel(self.translator.get_text("quick_actions", "快速操作"))
        actions_label.setObjectName("sectionLabel")
        actions_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        main_layout.addWidget(actions_label)

        # 操作卡片布局
        actions_layout = QGridLayout()

        # 创建操作卡片
        self.index_card = ActionCard(
            self.translator.get_text("create_index", "创建索引"),
            self.translator.get_text("create_index_desc", "创建新的向量索引，支持多种索引类型和量化方法"),
            self.translator.get_text("create", "创建")
        )
        self.vectorize_card = ActionCard(
            self.translator.get_text("vectorize_data", "向量化数据"),
            self.translator.get_text("vectorize_data_desc", "将文本、图像或其他数据转换为向量表示"),
            self.translator.get_text("vectorize", "向量化")
        )
        self.search_card = ActionCard(
            self.translator.get_text("search_vectors", "搜索向量"),
            self.translator.get_text("search_vectors_desc", "在向量索引中搜索相似内容"),
            self.translator.get_text("search", "搜索")
        )
        self.visualize_card = ActionCard(
            self.translator.get_text("visualize_vectors", "可视化向量"),
            self.translator.get_text("visualize_vectors_desc", "可视化向量空间，探索数据分布"),
            self.translator.get_text("visualize", "可视化")
        )

        # 添加卡片到布局
        actions_layout.addWidget(self.index_card, 0, 0)
        actions_layout.addWidget(self.vectorize_card, 0, 1)
        actions_layout.addWidget(self.search_card, 1, 0)
        actions_layout.addWidget(self.visualize_card, 1, 1)

        main_layout.addLayout(actions_layout)

        # 添加弹性空间
        main_layout.addStretch()

        # 连接信号
        self._connect_signals()

        # 模拟数据更新
        self._update_stats()

    def _connect_signals(self):
        """连接信号和槽"""
        # 连接操作卡片按钮
        self.index_card.button.clicked.connect(self._on_create_index)
        self.vectorize_card.button.clicked.connect(self._on_vectorize_data)
        self.search_card.button.clicked.connect(self._on_search_vectors)
        self.visualize_card.button.clicked.connect(self._on_visualize_vectors)

    def _update_stats(self):
        """更新统计数据"""
        # 这里应该从实际数据源获取数据
        # 现在只是模拟数据
        self.vectors_card.update_data(
            self.translator.get_text("total_vectors", "向量总数"),
            "1,234,567"
        )
        self.indices_card.update_data(
            self.translator.get_text("total_indices", "索引总数"),
            "42"
        )
        self.models_card.update_data(
            self.translator.get_text("total_models", "模型总数"),
            "8"
        )
        self.queries_card.update_data(
            self.translator.get_text("total_queries", "查询总数"),
            "98,765"
        )

    def _on_create_index(self):
        """创建索引按钮点击处理"""
        # 切换到索引页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'index_name_input'):  # 索引页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_vectorize_data(self):
        """向量化数据按钮点击处理"""
        # 切换到向量化页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'vectorize_button'):  # 向量化页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_search_vectors(self):
        """搜索向量按钮点击处理"""
        # 切换到搜索页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'search_button'):  # 搜索页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_visualize_vectors(self):
        """可视化向量按钮点击处理"""
        # 切换到可视化页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'plot_button'):  # 可视化页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def on_language_changed(self):
        """语言变更回调"""
        # 更新欢迎标签
        self.findChild(QLabel, "welcomeLabel").setText(
            self.translator.get_text("welcome_message", "欢迎使用MD向量处理器")
        )

        # 更新区域标签
        section_labels = self.findChildren(QLabel, "sectionLabel")
        if len(section_labels) >= 2:
            section_labels[0].setText(self.translator.get_text("statistics", "统计信息"))
            section_labels[1].setText(self.translator.get_text("quick_actions", "快速操作"))

        # 更新统计卡片
        self._update_stats()

        # 更新操作卡片
        self.index_card.update_data(
            self.translator.get_text("create_index", "创建索引"),
            self.translator.get_text("create_index_desc", "创建新的向量索引，支持多种索引类型和量化方法"),
            self.translator.get_text("create", "创建")
        )
        self.vectorize_card.update_data(
            self.translator.get_text("vectorize_data", "向量化数据"),
            self.translator.get_text("vectorize_data_desc", "将文本、图像或其他数据转换为向量表示"),
            self.translator.get_text("vectorize", "向量化")
        )
        self.search_card.update_data(
            self.translator.get_text("search_vectors", "搜索向量"),
            self.translator.get_text("search_vectors_desc", "在向量索引中搜索相似内容"),
            self.translator.get_text("search", "搜索")
        )
        self.visualize_card.update_data(
            self.translator.get_text("visualize_vectors", "可视化向量"),
            self.translator.get_text("visualize_vectors_desc", "可视化向量空间，探索数据分布"),
            self.translator.get_text("visualize", "可视化")
        )
