
# 贡献指南

感谢你考虑为 MD Vector Processor 项目做出贡献！这个文档提供了参与项目开发的指南和最佳实践。

## 目录

1. [行为准则](#行为准则)
2. [开始贡献](#开始贡献)
3. [开发流程](#开发流程)
4. [提交规范](#提交规范)
5. [代码风格](#代码风格)
6. [测试指南](#测试指南)
7. [文档编写](#文档编写)
8. [发布流程](#发布流程)

## 行为准则

本项目采用[贡献者公约](CODE_OF_CONDUCT.md)。参与本项目即表示你同意遵守其条款。

## 开始贡献

1. Fork 项目仓库
2. 克隆你的 Fork：
   ```bash
   git clone https://github.com/your-username/md_vector_processor.git
   cd md_vector_processor
   ```
3. 创建虚拟环境：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   venv\Scripts\activate     # Windows
   ```
4. 安装开发依赖：
   ```bash
   pip install -e ".[dev]"
   ```

## 开发流程

1. 创建新分支：
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. 进行开发和测试

3. 提交更改：
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

4. 推送到你的 Fork：
   ```bash
   git push origin feature/your-feature-name
   ```

5. 创建 Pull Request

## 提交规范

我们使用[约定式提交](https://www.conventionalcommits.org/)规范。每个提交消息应该包含：

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

类型包括：
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码风格修改
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(vectorizer): 添加新的向量化模型支持

- 集成BERT模型
- 添加模型配置选项
- 优化批处理性能

BREAKING CHANGE: 更改了向量化接口的参数结构
```

## 代码风格

我们使用以下工具确保代码质量：

1. **Black** 用于代码格式化：
   ```bash
   black src tests
   ```

2. **isort** 用于导入排序：
   ```bash
   isort src tests
   ```

3. **flake8** 用于代码检查：
   ```bash
   flake8 src tests
   ```

4. **mypy** 用于类型检查：
   ```bash
   mypy src
   ```

## 测试指南

1. 编写测试：
   - 单元测试放在 `tests/` 目录下
   - 使用 pytest 框架
   - 确保测试覆盖新功能和修复

2. 运行测试：
   ```bash
   pytest tests/
   ```

3. 检查覆盖率：
   ```bash
   pytest --cov=src tests/
   ```

## 文档编写

1. 代码文档：
   - 使用 Google 风格的文档字符串
   - 为所有公共 API 提供文档
   - 包含参数类型注解

2. 项目文档：
   - 更新 README.md
   - 更新 CHANGELOG.md
   - 添加新功能的使用示例

## 发布流程

1. 更新版本号：
   - 修改 `src/version.py`
   - 遵循语义化版本规范

2. 更新 CHANGELOG.md：
   - 添加新版本的更改记录
   - 包含所有重要变更

3. 创建发布标签：
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   git push origin v1.0.0
   ```

## 问题报告

创建问题报告时，请包含：

1. 问题描述
2. 复现步骤
3. 期望行为
4. 实际行为
5. 环境信息：
   - Python 版本
   - 操作系统
   - 相关依赖版本

## 功能请求

提出新功能建议时，请包含：

1. 用例描述
2. 预期收益
3. 可能的实现方案
4. 相关参考资料

## 代码审查

在审查 PR 时，我们关注：

1. 代码质量
2. 测试覆盖
3. 文档完整性
4. 性能影响
5. 向后兼容性

## 许可证

通过贡献代码，你同意你的贡献将采用项目的 MIT 许可证。

## 联系方式

如有任何问题，请通过以下方式联系：

- 问题追踪器：[GitHub Issues](https://github.com/yourusername/md_vector_processor/issues)
- 邮件：<EMAIL>

感谢你的贡献！