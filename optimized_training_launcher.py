#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化的训练启动器
包含更好的监控、错误处理和进度跟踪
"""

import sys
import time
import json
import psutil
import threading
from pathlib import Path
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'training_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self):
        self.start_time = None
        self.monitoring = False
        self.stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'processing_speed': []
        }
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.monitoring = True
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        logger.info("🔍 系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("🔍 系统监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # 磁盘使用率
                disk = psutil.disk_usage('.')
                disk_percent = (disk.used / disk.total) * 100
                
                # 记录统计
                self.stats['cpu_usage'].append(cpu_percent)
                self.stats['memory_usage'].append(memory_percent)
                self.stats['disk_usage'].append(disk_percent)
                
                # 检查资源警告
                if memory_percent > 85:
                    logger.warning(f"⚠️ 内存使用率过高: {memory_percent:.1f}%")
                
                if cpu_percent > 90:
                    logger.warning(f"⚠️ CPU使用率过高: {cpu_percent:.1f}%")
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"监控出错: {e}")
                time.sleep(60)
    
    def get_summary(self):
        """获取监控摘要"""
        if not self.stats['cpu_usage']:
            return "无监控数据"
        
        runtime = time.time() - self.start_time if self.start_time else 0
        
        summary = {
            'runtime_minutes': runtime / 60,
            'avg_cpu_usage': sum(self.stats['cpu_usage']) / len(self.stats['cpu_usage']),
            'max_memory_usage': max(self.stats['memory_usage']),
            'avg_memory_usage': sum(self.stats['memory_usage']) / len(self.stats['memory_usage']),
            'current_disk_usage': self.stats['disk_usage'][-1] if self.stats['disk_usage'] else 0
        }
        
        return summary

class OptimizedTrainingLauncher:
    """优化的训练启动器"""
    
    def __init__(self):
        self.monitor = TrainingMonitor()
        self.training_pipeline = None
    
    def check_prerequisites(self):
        """检查前置条件"""
        logger.info("🔍 检查训练前置条件...")
        
        issues = []
        
        # 1. 检查数据目录
        data_dir = Path('training_data/raw_documents')
        if not data_dir.exists():
            issues.append("数据目录不存在")
        else:
            pdf_count = len(list(data_dir.rglob('*.pdf')))
            if pdf_count == 0:
                issues.append("没有找到PDF文档")
            else:
                logger.info(f"✅ 找到 {pdf_count} 个PDF文档")
        
        # 2. 检查磁盘空间
        disk = psutil.disk_usage('.')
        free_gb = disk.free / (1024**3)
        if free_gb < 5:
            issues.append(f"磁盘空间不足 (剩余: {free_gb:.1f}GB)")
        else:
            logger.info(f"✅ 磁盘空间充足 (剩余: {free_gb:.1f}GB)")
        
        # 3. 检查内存
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        if available_gb < 4:
            issues.append(f"可用内存不足 (可用: {available_gb:.1f}GB)")
        else:
            logger.info(f"✅ 内存充足 (可用: {available_gb:.1f}GB)")
        
        # 4. 检查依赖
        try:
            import faiss
            logger.info("✅ FAISS库可用")
        except ImportError:
            issues.append("FAISS库未安装")
        
        try:
            import sentence_transformers
            logger.info("✅ SentenceTransformers库可用")
        except ImportError:
            issues.append("SentenceTransformers库未安装")
        
        if issues:
            logger.error("❌ 前置条件检查失败:")
            for issue in issues:
                logger.error(f"  - {issue}")
            return False
        
        logger.info("✅ 所有前置条件检查通过")
        return True
    
    def estimate_training_time(self):
        """估算训练时间"""
        logger.info("⏱️ 估算训练时间...")
        
        try:
            data_dir = Path('training_data/raw_documents')
            pdf_files = list(data_dir.rglob('*.pdf'))
            
            # 基于文件数量和大小估算
            total_size_mb = sum(f.stat().st_size for f in pdf_files) / (1024**2)
            
            # 估算公式 (基于测试结果)
            estimated_minutes = len(pdf_files) * 0.5 + total_size_mb * 0.1
            
            logger.info(f"📊 估算结果:")
            logger.info(f"  文档数量: {len(pdf_files)}")
            logger.info(f"  总大小: {total_size_mb:.1f}MB")
            logger.info(f"  预计时间: {estimated_minutes:.1f}分钟 ({estimated_minutes/60:.1f}小时)")
            
            return estimated_minutes
            
        except Exception as e:
            logger.warning(f"时间估算失败: {e}")
            return 60  # 默认1小时
    
    def run_training_with_monitoring(self):
        """运行带监控的训练"""
        logger.info("🚀 开始优化训练流程...")
        
        try:
            # 导入训练模块
            from training_automation import AutomotiveTrainingPipeline
            
            # 创建训练实例
            self.training_pipeline = AutomotiveTrainingPipeline()
            
            # 启动监控
            self.monitor.start_monitoring()
            
            # 运行训练
            logger.info("📊 开始文档扫描...")
            documents = self.training_pipeline.scan_documents()
            
            total_docs = sum(len(files) for files in documents.values())
            logger.info(f"📄 发现 {total_docs} 个文档")
            
            if total_docs == 0:
                raise Exception("没有找到可处理的文档")
            
            # 分批处理以提高稳定性
            BATCH_SIZE = 100
            all_chunks = []
            
            for category, files in documents.items():
                if not files:
                    continue
                
                logger.info(f"🔄 处理类别: {category} ({len(files)} 个文档)")
                
                # 分批处理
                for i in range(0, len(files), BATCH_SIZE):
                    batch_files = files[i:i+BATCH_SIZE]
                    batch_num = i // BATCH_SIZE + 1
                    total_batches = (len(files) - 1) // BATCH_SIZE + 1
                    
                    logger.info(f"📦 处理批次 {batch_num}/{total_batches} ({len(batch_files)} 个文档)")
                    
                    # 处理批次
                    batch_documents = {category: batch_files}
                    batch_chunks = self.training_pipeline.process_documents(batch_documents)
                    all_chunks.extend(batch_chunks)
                    
                    # 显示进度
                    processed = i + len(batch_files)
                    progress = (processed / len(files)) * 100
                    logger.info(f"📈 类别 {category} 进度: {progress:.1f}% ({processed}/{len(files)})")
            
            if not all_chunks:
                raise Exception("没有成功处理的文档块")
            
            logger.info(f"📝 总共生成 {len(all_chunks)} 个文本块")
            
            # 向量化
            logger.info("🔢 开始向量化...")
            vectors, vector_ids, processed_chunks = self.training_pipeline.vectorize_chunks(all_chunks)
            
            # 训练索引
            logger.info("🏗️ 开始索引训练...")
            trained_indices = self.training_pipeline.train_indices(vectors, vector_ids)
            
            # 保存数据
            logger.info("💾 保存训练数据...")
            self.training_pipeline.save_training_data(processed_chunks, vectors, vector_ids)
            
            # 停止监控
            self.monitor.stop_monitoring()
            
            # 生成报告
            self._generate_final_report(len(all_chunks), len(vectors), trained_indices)
            
            logger.info("🎉 训练完成!")
            return True
            
        except Exception as e:
            logger.error(f"❌ 训练失败: {e}")
            self.monitor.stop_monitoring()
            return False
    
    def _generate_final_report(self, chunks_count, vectors_count, trained_indices):
        """生成最终报告"""
        logger.info("📊 生成训练报告...")
        
        # 获取监控摘要
        monitor_summary = self.monitor.get_summary()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'training_results': {
                'chunks_generated': chunks_count,
                'vectors_generated': vectors_count,
                'indices_trained': len(trained_indices),
                'trained_indices': trained_indices
            },
            'system_performance': monitor_summary,
            'status': 'completed'
        }
        
        # 保存报告
        report_file = f"training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n" + "="*80)
        print("🎯 训练完成报告")
        print("="*80)
        print(f"📝 生成文本块: {chunks_count:,}")
        print(f"🔢 生成向量: {vectors_count:,}")
        print(f"🏗️ 训练索引: {len(trained_indices)}")
        
        if isinstance(monitor_summary, dict):
            print(f"⏱️ 运行时间: {monitor_summary.get('runtime_minutes', 0):.1f} 分钟")
            print(f"💾 最大内存使用: {monitor_summary.get('max_memory_usage', 0):.1f}%")
            print(f"🖥️ 平均CPU使用: {monitor_summary.get('avg_cpu_usage', 0):.1f}%")
        
        print(f"📄 详细报告: {report_file}")
        print("="*80)

def main():
    """主函数"""
    print("🚀 优化训练启动器")
    print("="*60)
    
    launcher = OptimizedTrainingLauncher()
    
    # 1. 检查前置条件
    if not launcher.check_prerequisites():
        print("❌ 前置条件检查失败，请解决问题后重试")
        return False
    
    # 2. 估算训练时间
    estimated_time = launcher.estimate_training_time()
    
    # 3. 用户确认
    print(f"\n⏱️ 预计训练时间: {estimated_time:.1f} 分钟")
    print("🔍 将启动系统监控")
    print("📊 支持分批处理以提高稳定性")
    
    confirm = input("\n是否开始训练? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消训练")
        return False
    
    # 4. 运行训练
    success = launcher.run_training_with_monitoring()
    
    if success:
        print("🎉 训练成功完成!")
        return True
    else:
        print("❌ 训练失败，请检查日志")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
