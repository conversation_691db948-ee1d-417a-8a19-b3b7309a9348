
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: md_vector_processor:dev
    container_name: md_vector_processor_dev
    volumes:
      - .:/app
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
    ports:
      - "8000:8000"  # API
      - "8888:8888"  # Jupyter
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - DEBUG=1
      - LOG_LEVEL=DEBUG
    command: python run.py --mode api --debug --reload
    depends_on:
      - redis
      - postgres
    networks:
      - dev_network

  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: md_vector_processor:jupyter
    container_name: md_vector_processor_jupyter
    volumes:
      - .:/app
      - ./notebooks:/app/notebooks
    ports:
      - "8888:8888"
    environment:
      - PYTHONPATH=/app
      - JUPYTER_TOKEN=mdvector
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root
    networks:
      - dev_network

  postgres:
    image: postgres:13-alpine
    container_name: md_vector_processor_db
    environment:
      - POSTGRES_USER=mdvector
      - POSTGRES_PASSWORD=development
      - POSTGRES_DB=mdvector_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - dev_network

  redis:
    image: redis:6-alpine
    container_name: md_vector_processor_cache
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - dev_network

  adminer:
    image: adminer
    container_name: md_vector_processor_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - dev_network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: md_vector_processor_redis_ui
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - dev_network

volumes:
  postgres_data:
  redis_data:

networks:
  dev_network:
    driver: bridge