
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Markdown加载器模块
负责读取和解析Markdown文件内容
"""

import logging
import chardet
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
import re
import frontmatter  # 用于解析YAML front matter
from .file_scanner import FileMetadata

@dataclass
class MDContent:
    """Markdown内容数据类"""
    metadata: FileMetadata
    content: str
    front_matter: Dict[str, Any]
    sections: List[Dict[str, str]]  # 每个部分包含标题和内容
    code_blocks: List[Dict[str, str]]  # 代码块列表
    links: List[str]  # 链接列表
    images: List[str]  # 图片链接列表
    
    @property
    def text_content(self) -> str:
        """获取纯文本内容（不包含代码块和front matter）"""
        return '\n\n'.join(section['content'] for section in self.sections)

class MDLoader:
    """Markdown文件加载器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Markdown加载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.encoding = config['file_processing'].get('encoding', 'utf-8')
        
        # 编译正则表达式
        self.code_block_pattern = re.compile(r'```[\s\S]*?```')
        self.header_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.link_pattern = re.compile(r'\[([^\]]+)\]\(([^)]+)\)')
        self.image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
    
    def detect_encoding(self, file_path: Path) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 检测到的编码
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
                
                if detected_encoding is None:
                    self.logger.warning(f"无法检测文件编码 {file_path}，使用默认编码 {self.encoding}")
                    return self.encoding
                    
                return detected_encoding
        except Exception as e:
            self.logger.error(f"检测文件 {file_path} 编码时出错: {e}")
            return self.encoding
    
    def extract_code_blocks(self, content: str) -> Tuple[str, List[Dict[str, str]]]:
        """
        提取并移除代码块
        
        Args:
            content: Markdown内容
            
        Returns:
            Tuple[str, List[Dict[str, str]]]: 处理后的内容和代码块列表
        """
        code_blocks = []
        positions = []
        
        for match in self.code_block_pattern.finditer(content):
            block = match.group()
            language = block.split('\n')[0].replace('```', '').strip()
            code = '\n'.join(block.split('\n')[1:-1])
            code_blocks.append({
                'language': language,
                'code': code
            })
            positions.append(match.span())
        
        # 移除代码块
        if positions:
            new_content = ''
            last_end = 0
            for start, end in positions:
                new_content += content[last_end:start]
                last_end = end
            new_content += content[last_end:]
            return new_content, code_blocks
        
        return content, code_blocks
    
    def parse_sections(self, content: str) -> List[Dict[str, str]]:
        """
        解析文档章节
        
        Args:
            content: Markdown内容
            
        Returns:
            List[Dict[str, str]]: 章节列表
        """
        sections = []
        lines = content.split('\n')
        current_section = {'title': '', 'content': []}
        
        for line in lines:
            header_match = self.header_pattern.match(line)
            if header_match:
                # 保存当前章节
                if current_section['content']:
                    current_section['content'] = '\n'.join(current_section['content']).strip()
                    sections.append(current_section)
                
                # 创建新章节
                level = len(header_match.group(1))  # 标题级别
                title = header_match.group(2)
                current_section = {'title': title, 'level': level, 'content': []}
            else:
                current_section['content'].append(line)
        
        # 添加最后一个章节
        if current_section['content']:
            current_section['content'] = '\n'.join(current_section['content']).strip()
            sections.append(current_section)
        
        return sections
    
    def extract_links_and_images(self, content: str) -> Tuple[List[str], List[str]]:
        """
        提取链接和图片
        
        Args:
            content: Markdown内容
            
        Returns:
            Tuple[List[str], List[str]]: 链接列表和图片列表
        """
        links = [match.group(2) for match in self.link_pattern.finditer(content)]
        images = [match.group(2) for match in self.image_pattern.finditer(content)]
        return links, images
    
    def load_file(self, metadata: FileMetadata) -> Optional[MDContent]:
        """
        加载并解析Markdown文件
        
        Args:
            metadata: 文件元数据
            
        Returns:
            Optional[MDContent]: 解析后的Markdown内容
        """
        try:
            # 检测文件编码
            encoding = self.detect_encoding(metadata.path)
            
            # 读取文件内容
            with open(metadata.path, 'r', encoding=encoding) as f:
                raw_content = f.read()
            
            # 解析front matter
            post = frontmatter.loads(raw_content)
            front_matter_data = post.metadata or {}
            content = post.content
            
            # 提取代码块
            content, code_blocks = self.extract_code_blocks(content)
            
            # 解析章节
            sections = self.parse_sections(content)
            
            # 提取链接和图片
            links, images = self.extract_links_and_images(content)
            
            return MDContent(
                metadata=metadata,
                content=content,
                front_matter=front_matter_data,
                sections=sections,
                code_blocks=code_blocks,
                links=links,
                images=images
            )
            
        except Exception as e:
            self.logger.error(f"加载文件 {metadata.path} 时出错: {e}")
            return None
    
    def load_batch(self, metadata_batch: List[FileMetadata]) -> List[MDContent]:
        """
        批量加载Markdown文件
        
        Args:
            metadata_batch: 文件元数据列表
            
        Returns:
            List[MDContent]: 加载的内容列表
        """
        results = []
        for metadata in metadata_batch:
            try:
                content = self.load_file(metadata)
                if content is not None:
                    results.append(content)
            except Exception as e:
                self.logger.error(f"处理文件 {metadata.path} 时出错: {e}")
                continue
        return results

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'file_processing': {
            'encoding': 'utf-8'
        }
    }
    
    loader = MDLoader(test_config)
    
    # 创建测试文件元数据
    test_metadata = FileMetadata(
        path=Path("test.md"),
        filename="test.md",
        extension=".md",
        size=0,
        created_time=None,
        modified_time=None,
        relative_path="test.md"
    )
    
    # 测试加载文件
    content = loader.load_file(test_metadata)
    if content:
        print(f"Sections: {len(content.sections)}")
        print(f"Code blocks: {len(content.code_blocks)}")
        print(f"Links: {len(content.links)}")
        print(f"Images: {len(content.images)}")