
# MD Vector Processor 配置文件

# 文件处理配置
file_processing:
  input_dir: "data/raw"
  output_dir: "data/processed"
  vector_dir: "data/vectors"
  supported_extensions: [".md"]
  batch_size: 32
  encoding: "utf-8"

# 预处理配置
preprocessing:
  # 文本清理
  remove_html_tags: true
  remove_special_chars: true
  normalize_whitespace: true
  remove_urls: true
  normalize_chars: true

  # 语言处理
  language: "multilingual"  # 'chinese', 'english', 'multilingual', 'auto'
  lowercase: true
  min_length: 10
  max_length: 512

  # 多语言支持
  languages:
    enabled: ["chinese", "english", "japanese", "korean", "russian", "spanish", "french", "german", "arabic"]
    auto_detect: true
    fallback: "english"

  # 代码处理
  code_blocks:
    enabled: true
    languages: ["python", "java", "javascript", "c", "cpp", "csharp", "go", "rust", "php", "ruby"]
    special_model: "code"  # 使用代码特化模型处理代码块

  # 分词设置
  use_lemmatization: true
  use_stemming: false
  remove_stopwords: true

  # 数字处理
  number_handling: "keep"  # 'keep', 'remove', 'replace'

  # 标点符号处理
  punctuation_handling: "normalize"  # 'keep', 'remove', 'normalize'

  # 小语种支持
  low_resource_languages:
    enabled: true
    languages: ["thai", "vietnamese", "hindi", "turkish", "indonesian", "malay"]
    use_specialized_tokenizers: true

# 向量化配置
vectorization:
  # 模型设置
  model_name: "multilingual-minilm"  # 预定义模型名称或HuggingFace模型ID
  domain: null  # 可选: 'medical', 'legal', 'technical', 'code'
  vector_dimension: 384
  batch_size: 32
  device: "cuda"  # 'cuda' or 'cpu'

  # 高级模型设置
  model_cache_dir: "models"
  custom_model_path: null  # 自定义模型路径
  pooling_method: "mean_pooling"  # 'mean_pooling', 'cls_pooling', 'max_pooling', 'last_pooling'

  # 向量处理
  normalize_vectors: true
  compression_method: "pq"  # 'pq', 'opq', 'scann', 'svd'
  reduced_dimension: 128

  # 缓存设置
  cache_dir: "cache"
  cache_enabled: true

  # 可视化设置
  visualization_method: "tsne"  # 'tsne', 'umap', or 'pca'

  # 微调设置
  fine_tuning:
    enabled: false
    data_path: null  # 训练数据路径
    save_path: "models/fine_tuned"
    epochs: 3
    learning_rate: 2e-5
    batch_size: 16

# 索引配置
indexing:
  # 索引类型
  index_type: "faiss"  # 'faiss', 'hnsw', 'hybrid'
  metric: "cosine"     # 'cosine', 'l2', 'ip'

  # FAISS参数
  n_lists: 100        # IVF聚类中心数量
  n_probes: 10        # 搜索时探测的聚类数量

  # HNSW参数
  ef_construction: 200
  ef_search: 100
  M: 16

  # 混合索引参数 (HNSW+IVF)
  hybrid_mode: "hnsw_ivf"  # 'hnsw_ivf', 'ivf_hnsw'
  hybrid_n_lists: 50
  hybrid_n_probes: 5

  # 量化参数
  quantization: "pq"   # 'pq', 'opq', 'sq', 'none'
  pq_m: 8              # PQ子空间数量
  pq_bits: 8           # 每个子空间的位数
  opq_m: 8             # OPQ子空间数量
  opq_bits: 8          # 每个子空间的位数

  # 增量索引设置
  incremental_index: true
  rebuild_threshold: 1000  # 增量更新多少次后重建索引

  # 搜索参数
  default_k: 10
  min_similarity: 0.5
  max_candidates: 100

  # 性能优化
  use_gpu_index: false  # 是否使用GPU加速索引
  cache_index: true     # 是否缓存索引

# 存储配置
storage:
  # 基本设置
  base_dir: "data/vectors"
  vector_format: "numpy"
  metadata_format: "json"

  # 压缩设置
  compression: true

  # 缓存设置
  cache_enabled: true

  # 备份设置
  backup_enabled: true
  backup_frequency: "daily"  # 'daily', 'weekly', 'monthly'

# 系统配置
system:
  num_workers: 4
  memory_limit: "8G"
  temp_dir: "temp"

# 日志配置
logging:
  log_dir: "logs"
  log_file: "processor.log"
  log_level: "INFO"
  max_file_size: "10MB"
  backup_count: 5
  console_log: true

# 性能优化
performance:
  # 并发设置
  use_multiprocessing: true
  max_workers: 4
  chunk_size: 300

  # 相似度设置
  similarity_threshold: 0.3  # 最小相似度阈值
  similarity_boost: 1.2      # 相似度提升因子

  # 内存管理
  batch_processing: true
  memory_efficient: true
  max_text_size: 5000000  # 最大处理文本大小限制(5MB)

  # 缓存设置
  use_disk_cache: true
  cache_size_limit: "1GB"

  # GPU设置
  use_gpu: true
  gpu_memory_fraction: 0.8

# 调试配置
debug:
  enabled: false
  verbose: false
  profile: false
  save_intermediate: false