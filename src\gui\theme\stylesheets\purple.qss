/* 紫色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #F3E5F5;
}

QLabel {
    color: #4A148C;
}

QPushButton {
    background-color: #8E24AA;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #7B1FA2;
}

QPushButton:pressed {
    background-color: #6A1B9A;
}

QPushButton:disabled {
    background-color: #E1BEE7;
    color: #CE93D8;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #4A148C;
    border: 1px solid #CE93D8;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #8E24AA;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #F3E5F5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #CE93D8;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #F3E5F5;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #CE93D8;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #8E24AA;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #7B1FA2;
}

QMenu {
    background-color: #FFFFFF;
    color: #4A148C;
    border: 1px solid #CE93D8;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #F3E5F5;
}

QTabWidget::pane {
    border: 1px solid #CE93D8;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #E1BEE7;
    color: #4A148C;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #8E24AA;
}

QStatusBar {
    background-color: #8E24AA;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #8E24AA;
}

#sidebarWidget {
    background-color: #8E24AA;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #F3E5F5;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #CE93D8;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #8E24AA;
    margin-bottom: 8px;
}

.card-content {
    color: #4A148C;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #8E24AA;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #8E24AA;
    box-shadow: 0 0 10px #8E24AA;
}
