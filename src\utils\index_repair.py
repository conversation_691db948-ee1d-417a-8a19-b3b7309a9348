"""
索引修复工具

用于修复索引和元数据之间的映射问题
"""

import os
import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional

from ..storage import MetadataManager, VectorStore
from ..indexer import IndexBuilder


class IndexRepairTool:
    """索引修复工具"""
    
    def __init__(self, config: Dict):
        """
        初始化索引修复工具
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.metadata_manager = MetadataManager(config)
        self.vector_store = VectorStore(config)
        
    def diagnose_index_issues(self, index_path: str) -> Dict:
        """
        诊断索引问题
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            Dict: 诊断结果
        """
        self.logger.info(f"开始诊断索引: {index_path}")
        
        diagnosis = {
            'index_exists': False,
            'index_size': 0,
            'metadata_count': 0,
            'vector_count': 0,
            'mapping_issues': [],
            'recommendations': []
        }
        
        try:
            # 检查索引文件是否存在
            if os.path.exists(index_path):
                diagnosis['index_exists'] = True
                diagnosis['index_size'] = os.path.getsize(index_path)
                self.logger.info(f"索引文件存在，大小: {diagnosis['index_size']} 字节")
            else:
                diagnosis['recommendations'].append("索引文件不存在，需要重新创建")
                return diagnosis
            
            # 加载索引
            builder = IndexBuilder(self.config)
            if not builder.load_index(index_path):
                diagnosis['recommendations'].append("无法加载索引文件，可能已损坏")
                return diagnosis
            
            diagnosis['index_size'] = builder.total_vectors
            self.logger.info(f"索引中的向量数量: {builder.total_vectors}")
            
            # 检查元数据
            all_metadata = self.metadata_manager.get_all_metadata()
            diagnosis['metadata_count'] = len(all_metadata)
            self.logger.info(f"元数据数量: {diagnosis['metadata_count']}")
            
            # 检查向量存储
            vector_stats = self.vector_store.get_stats()
            diagnosis['vector_count'] = vector_stats.get('total_vectors', 0)
            self.logger.info(f"向量存储中的向量数量: {diagnosis['vector_count']}")
            
            # 检查映射问题
            if diagnosis['index_size'] != diagnosis['metadata_count']:
                diagnosis['mapping_issues'].append(
                    f"索引向量数量({diagnosis['index_size']})与元数据数量({diagnosis['metadata_count']})不匹配"
                )
            
            if diagnosis['index_size'] != diagnosis['vector_count']:
                diagnosis['mapping_issues'].append(
                    f"索引向量数量({diagnosis['index_size']})与向量存储数量({diagnosis['vector_count']})不匹配"
                )
            
            # 生成建议
            if diagnosis['mapping_issues']:
                diagnosis['recommendations'].append("建议重建索引以修复映射问题")
            else:
                diagnosis['recommendations'].append("索引状态正常")
                
        except Exception as e:
            self.logger.error(f"诊断索引时出错: {e}")
            diagnosis['mapping_issues'].append(f"诊断过程中出错: {str(e)}")
            diagnosis['recommendations'].append("建议重新创建索引")
        
        return diagnosis
    
    def rebuild_index(self, index_path: str, force: bool = False) -> bool:
        """
        重建索引
        
        Args:
            index_path: 索引文件路径
            force: 是否强制重建
            
        Returns:
            bool: 是否成功重建
        """
        self.logger.info(f"开始重建索引: {index_path}")
        
        try:
            # 获取所有元数据
            all_metadata = self.metadata_manager.get_all_metadata()
            if not all_metadata:
                self.logger.error("没有找到元数据，无法重建索引")
                return False
            
            self.logger.info(f"找到 {len(all_metadata)} 个元数据记录")
            
            # 收集所有向量和ID
            vectors = []
            doc_ids = []
            
            for doc_id, metadata in all_metadata.items():
                # 尝试从向量存储中获取向量
                vector = self.vector_store.get_vector(doc_id)
                if vector is not None:
                    vectors.append(vector)
                    doc_ids.append(doc_id)
                else:
                    self.logger.warning(f"无法找到文档 {doc_id} 的向量")
            
            if not vectors:
                self.logger.error("没有找到任何向量，无法重建索引")
                return False
            
            self.logger.info(f"收集到 {len(vectors)} 个向量")
            
            # 转换为numpy数组
            vectors_array = np.vstack(vectors)
            doc_ids_array = np.array(doc_ids)
            
            # 创建新的索引构建器
            indexing_config = self.config.get('indexing', {})
            indexing_config.update({
                'index_type': 'flat',  # 使用简单的flat索引确保兼容性
                'metric': 'ip',        # 使用内积度量
                'dimension': vectors_array.shape[1]
            })
            
            builder = IndexBuilder({'indexing': indexing_config})
            
            # 创建索引
            if not builder.create_index(vectors_array.shape[1]):
                self.logger.error("创建索引失败")
                return False
            
            # 添加向量到索引
            if not builder.add_vectors(vectors_array, doc_ids_array, self.metadata_manager):
                self.logger.error("添加向量到索引失败")
                return False
            
            # 保存索引
            if not builder.save_index(index_path):
                self.logger.error("保存索引失败")
                return False
            
            self.logger.info(f"成功重建索引: {index_path}")
            self.logger.info(f"索引包含 {len(vectors)} 个向量")
            
            return True
            
        except Exception as e:
            self.logger.error(f"重建索引时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def optimize_similarity_calculation(self) -> Dict:
        """
        优化相似度计算
        
        Returns:
            Dict: 优化结果
        """
        self.logger.info("开始优化相似度计算")
        
        result = {
            'optimizations_applied': [],
            'recommendations': []
        }
        
        try:
            # 检查向量是否已归一化
            all_metadata = self.metadata_manager.get_all_metadata()
            sample_size = min(100, len(all_metadata))
            
            if sample_size > 0:
                sample_ids = list(all_metadata.keys())[:sample_size]
                vectors = []
                
                for doc_id in sample_ids:
                    vector = self.vector_store.get_vector(doc_id)
                    if vector is not None:
                        vectors.append(vector)
                
                if vectors:
                    vectors_array = np.vstack(vectors)
                    norms = np.linalg.norm(vectors_array, axis=1)
                    
                    # 检查是否已归一化
                    normalized_count = np.sum(np.abs(norms - 1.0) < 1e-6)
                    normalization_ratio = normalized_count / len(norms)
                    
                    self.logger.info(f"归一化比例: {normalization_ratio:.2%}")
                    
                    if normalization_ratio < 0.9:
                        result['recommendations'].append(
                            "建议对向量进行归一化以提高相似度计算精度"
                        )
                    else:
                        result['optimizations_applied'].append("向量已正确归一化")
            
            # 检查文本切片策略
            chunk_sizes = []
            for metadata in list(all_metadata.values())[:100]:
                if 'text' in metadata:
                    chunk_sizes.append(len(metadata['text']))
            
            if chunk_sizes:
                avg_chunk_size = np.mean(chunk_sizes)
                std_chunk_size = np.std(chunk_sizes)
                
                self.logger.info(f"平均块大小: {avg_chunk_size:.1f}, 标准差: {std_chunk_size:.1f}")
                
                if avg_chunk_size < 200:
                    result['recommendations'].append(
                        "文本块过小，建议增加块大小以保留更多语义信息"
                    )
                elif avg_chunk_size > 500:
                    result['recommendations'].append(
                        "文本块过大，建议减小块大小以提高匹配精度"
                    )
                else:
                    result['optimizations_applied'].append("文本块大小适中")
                
                if std_chunk_size > avg_chunk_size * 0.5:
                    result['recommendations'].append(
                        "文本块大小差异较大，建议使用更一致的切片策略"
                    )
        
        except Exception as e:
            self.logger.error(f"优化相似度计算时出错: {e}")
            result['recommendations'].append(f"优化过程中出错: {str(e)}")
        
        return result
