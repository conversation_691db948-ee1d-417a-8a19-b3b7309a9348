
{{ define "slack.default.title" }}
[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .CommonLabels.alertname }}
{{ end }}

{{ define "slack.default.text" }}
{{ range .Alerts -}}
*Alert:* {{ .Annotations.summary }}
*Description:* {{ .Annotations.description }}
*Severity:* {{ .Labels.severity }}
*Duration:* {{ duration .StartsAt .EndsAt }}
*Instance:* {{ .Labels.instance }}
*Details:*
{{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
{{ end }}
{{ if .Annotations.runbook }}*Runbook:* {{ .Annotations.runbook }}{{ end }}
{{ end }}
{{ end }}

{{ define "slack.critical.title" }}
[CRITICAL] {{ .CommonLabels.alertname }}
{{ end }}

{{ define "slack.critical.text" }}
{{ range .Alerts -}}
:red_circle: *Critical Alert:* {{ .Annotations.summary }}
*Description:* {{ .Annotations.description }}
*Duration:* {{ duration .StartsAt .EndsAt }}
*Instance:* {{ .Labels.instance }}
*Details:*
{{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
{{ end }}
{{ if .Annotations.runbook }}*Runbook:* {{ .Annotations.runbook }}{{ end }}
{{ end }}
{{ end }}

{{ define "slack.warning.title" }}
[WARNING] {{ .CommonLabels.alertname }}
{{ end }}

{{ define "slack.warning.text" }}
{{ range .Alerts -}}
:warning: *Warning Alert:* {{ .Annotations.summary }}
*Description:* {{ .Annotations.description }}
*Duration:* {{ duration .StartsAt .EndsAt }}
*Instance:* {{ .Labels.instance }}
*Details:*
{{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
{{ end }}
{{ if .Annotations.runbook }}*Runbook:* {{ .Annotations.runbook }}{{ end }}
{{ end }}
{{ end }}