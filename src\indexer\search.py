
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量搜索器模块
负责向量索引的搜索功能
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import faiss
import hnswlib
from pathlib import Path
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
from tqdm import tqdm

@dataclass
class SearchResult:
    """搜索结果数据类"""
    id: int
    score: float
    vector: Optional[np.ndarray] = None
    metadata: Optional[Dict[str, Any]] = None

class VectorSearcher:
    """向量搜索器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化向量搜索器
        
        Args:
            config: 配置字典，包含索引相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 获取配置参数
        self.indexing_config = config.get('indexing', {})
        self.index_type = self.indexing_config.get('index_type', 'faiss')
        self.metric = self.indexing_config.get('metric', 'cosine')
        
        # 搜索参数
        self.default_k = self.indexing_config.get('default_k', 10)
        self.min_similarity = self.indexing_config.get('min_similarity', 0.5)
        self.max_candidates = self.indexing_config.get('max_candidates', 100)
        
        # 索引和元数据引用
        self.index = None
        self.metadata_dict = {}
        
        # 线程锁
        self.search_lock = threading.Lock()
    
    def set_index(self, index: Union[faiss.Index, hnswlib.Index]) -> None:
        """
        设置搜索索引
        
        Args:
            index: FAISS或HNSW索引对象
        """
        with self.search_lock:
            self.index = index
    
    def set_metadata(self, metadata: Dict[int, Dict[str, Any]]) -> None:
        """
        设置向量元数据
        
        Args:
            metadata: 向量ID到元数据的映射字典
        """
        with self.search_lock:
            self.metadata_dict = metadata
    
    def _normalize_vector(self, vector: np.ndarray) -> np.ndarray:
        """
        标准化向量
        
        Args:
            vector: 输入向量
            
        Returns:
            np.ndarray: 标准化后的向量
        """
        if self.metric == 'cosine':
            norm = np.linalg.norm(vector)
            if norm > 0:
                return vector / norm
        return vector
    
    def _convert_distance_to_similarity(self, distance: float) -> float:
        """
        将距离转换为相似度分数
        
        Args:
            distance: 距离值
            
        Returns:
            float: 相似度分数
        """
        if self.metric == 'cosine':
            return (1 - distance) / 2  # 将余弦距离转换为相似度
        elif self.metric == 'l2':
            return 1 / (1 + distance)  # 将欧氏距离转换为相似度
        else:  # inner product
            return (distance + 1) / 2  # 将内积转换为相似度
    
    def _prepare_query_vector(self, vector: np.ndarray) -> np.ndarray:
        """
        准备查询向量
        
        Args:
            vector: 输入向量
            
        Returns:
            np.ndarray: 处理后的查询向量
        """
        # 确保向量是二维的
        if vector.ndim == 1:
            vector = vector.reshape(1, -1)
        
        # 确保数据类型正确
        vector = vector.astype(np.float32)
        
        # 标准化向量（如果需要）
        if self.metric == 'cosine':
            vector = self._normalize_vector(vector)
        
        return vector
    
    def _search_faiss(self, query_vector: np.ndarray, k: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用FAISS索引搜索
        
        Args:
            query_vector: 查询向量
            k: 返回的最近邻数量
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (距离数组, ID数组)
        """
        return self.index.search(query_vector, k)
    
    def _search_hnsw(self, query_vector: np.ndarray, k: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        使用HNSW索引搜索
        
        Args:
            query_vector: 查询向量
            k: 返回的最近邻数量
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (距离数组, ID数组)
        """
        labels, distances = self.index.knn_query(query_vector, k)
        return distances, labels
    
    def search(self, query_vector: np.ndarray, k: Optional[int] = None,
              min_similarity: Optional[float] = None,
              return_vectors: bool = False) -> List[SearchResult]:
        """
        搜索最相似的向量
        
        Args:
            query_vector: 查询向量
            k: 返回的结果数量
            min_similarity: 最小相似度阈值
            return_vectors: 是否返回向量数据
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            if self.index is None:
                raise ValueError("未设置索引")
            
            # 使用默认值
            k = k or self.default_k
            min_similarity = min_similarity or self.min_similarity
            
            # 准备查询向量
            query_vector = self._prepare_query_vector(query_vector)
            
            with self.search_lock:
                # 执行搜索
                if self.index_type == 'faiss':
                    distances, indices = self._search_faiss(query_vector, k)
                else:  # hnsw
                    distances, indices = self._search_hnsw(query_vector, k)
                
                # 处理结果
                results = []
                for distance, idx in zip(distances[0], indices[0]):
                    # 计算相似度分数
                    similarity = self._convert_distance_to_similarity(distance)
                    
                    # 应用相似度阈值
                    if similarity < min_similarity:
                        continue
                    
                    # 创建结果对象
                    result = SearchResult(
                        id=int(idx),
                        score=float(similarity),
                        metadata=self.metadata_dict.get(int(idx))
                    )
                    
                    # 如果需要，添加向量数据
                    if return_vectors:
                        if self.index_type == 'faiss':
                            vector = self.index.reconstruct(int(idx))
                        else:  # hnsw
                            vector = self.index.get_items([int(idx)])[0]
                        result.vector = vector
                    
                    results.append(result)
                
                # 按相似度排序
                results.sort(key=lambda x: x.score, reverse=True)
                
                return results
                
        except Exception as e:
            self.logger.error(f"搜索向量时出错: {e}")
            return []
    
    def batch_search(self, query_vectors: np.ndarray, k: Optional[int] = None,
                    min_similarity: Optional[float] = None,
                    return_vectors: bool = False,
                    batch_size: int = 32) -> List[List[SearchResult]]:
        """
        批量搜索向量
        
        Args:
            query_vectors: 查询向量矩阵
            k: 每个查询返回的结果数量
            min_similarity: 最小相似度阈值
            return_vectors: 是否返回向量数据
            batch_size: 批处理大小
            
        Returns:
            List[List[SearchResult]]: 每个查询的搜索结果列表
        """
        try:
            all_results = []
            total_queries = len(query_vectors)
            
            for i in tqdm(range(0, total_queries, batch_size), desc="Searching vectors"):
                batch_vectors = query_vectors[i:i + batch_size]
                
                # 处理每个查询向量
                batch_results = []
                for vector in batch_vectors:
                    results = self.search(vector, k, min_similarity, return_vectors)
                    batch_results.append(results)
                
                all_results.extend(batch_results)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"批量搜索向量时出错: {e}")
            return []
    
    def range_search(self, query_vector: np.ndarray,
                    min_similarity: float,
                    max_results: Optional[int] = None) -> List[SearchResult]:
        """
        范围搜索：返回相似度大于阈值的所有向量
        
        Args:
            query_vector: 查询向量
            min_similarity: 最小相似度阈值
            max_results: 最大返回结果数量
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            # 将相似度阈值转换为距离阈值
            if self.metric == 'cosine':
                distance_threshold = 1 - 2 * min_similarity
            elif self.metric == 'l2':
                distance_threshold = (1 - min_similarity) / min_similarity
            else:  # inner product
                distance_threshold = 2 * min_similarity - 1
            
            # 准备查询向量
            query_vector = self._prepare_query_vector(query_vector)
            
            with self.search_lock:
                if self.index_type == 'faiss' and hasattr(self.index, 'range_search'):
                    # FAISS范围搜索
                    distances, indices = self.index.range_search(query_vector, distance_threshold)
                else:
                    # 如果不支持范围搜索，使用k-NN搜索代替
                    k = max_results or self.max_candidates
                    if self.index_type == 'faiss':
                        distances, indices = self._search_faiss(query_vector, k)
                    else:  # hnsw
                        distances, indices = self._search_hnsw(query_vector, k)
                    
                    # 过滤结果
                    mask = distances[0] <= distance_threshold
                    distances = distances[0][mask]
                    indices = indices[0][mask]
                
                # 处理结果
                results = []
                for distance, idx in zip(distances, indices):
                    similarity = self._convert_distance_to_similarity(distance)
                    result = SearchResult(
                        id=int(idx),
                        score=float(similarity),
                        metadata=self.metadata_dict.get(int(idx))
                    )
                    results.append(result)
                
                # 排序并限制结果数量
                results.sort(key=lambda x: x.score, reverse=True)
                if max_results:
                    results = results[:max_results]
                
                return results
                
        except Exception as e:
            self.logger.error(f"范围搜索时出错: {e}")
            return []

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'indexing': {
            'index_type': 'faiss',
            'metric': 'cosine',
            'default_k': 5,
            'min_similarity': 0.5,
            'max_candidates': 100
        }
    }
    
    searcher = VectorSearcher(test_config)
    
    # 创建测试索引
    dimension = 384
    n_vectors = 1000
    
    if test_config['indexing']['index_type'] == 'faiss':
        index = faiss.IndexFlatIP(dimension)
        test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
        index.add(test_vectors)
    else:
        index = hnswlib.Index(space='cosine', dim=dimension)
        index.init_index(max_elements=n_vectors, ef_construction=100, M=16)
        test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
        index.add_items(test_vectors)
    
    # 设置索引
    searcher.set_index(index)
    
    # 创建测试元数据
    metadata = {i: {'text': f'Vector {i}'} for i in range(n_vectors)}
    searcher.set_metadata(metadata)
    
    # 测试搜索
    query_vector = np.random.rand(dimension).astype('float32')
    results = searcher.search(query_vector, k=5)
    
    print("\n搜索结果:")
    for result in results:
        print(f"ID: {result.id}, 相似度: {result.score:.4f}, 元数据: {result.metadata}")
    
    # 测试批量搜索
    query_vectors = np.random.rand(10, dimension).astype('float32')
    batch_results = searcher.batch_search(query_vectors, k=3)
    
    print("\n批量搜索结果:")
    for i, results in enumerate(batch_results):
        print(f"\n查询 {i}:")
        for result in results:
            print(f"ID: {result.id}, 相似度: {result.score:.4f}")
    
    # 测试范围搜索
    range_results = searcher.range_search(query_vector, min_similarity=0.7, max_results=5)
    
    print("\n范围搜索结果:")
    for result in range_results:
        print(f"ID: {result.id}, 相似度: {result.score:.4f}")