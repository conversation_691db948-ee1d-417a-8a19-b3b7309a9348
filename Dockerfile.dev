
# 使用官方Python基础镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    POETRY_VERSION=1.4.2 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false

# 安装系统依赖
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        libpq-dev \
        procps \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN curl -sSL https://install.python-poetry.org | python -

# 将Poetry添加到PATH
ENV PATH="${POETRY_HOME}/bin:$PATH"

# 复制项目文件
COPY pyproject.toml poetry.lock* ./

# 安装项目依赖
RUN poetry install --no-root --no-interaction --no-ansi

# 安装开发工具
RUN pip install --no-cache-dir \
    jupyter \
    jupyterlab \
    ipython \
    ipdb \
    black \
    flake8 \
    mypy \
    pytest \
    pytest-cov \
    pytest-watch

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/cache /app/notebooks

# 设置Jupyter配置
RUN jupyter notebook --generate-config \
    && echo "c.NotebookApp.token = 'mdvector'" >> ~/.jupyter/jupyter_notebook_config.py \
    && echo "c.NotebookApp.password = ''" >> ~/.jupyter/jupyter_notebook_config.py

# 设置开发用户
RUN useradd -m -s /bin/bash developer \
    && chown -R developer:developer /app
USER developer

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置默认命令
CMD ["python", "run.py", "--mode", "api", "--debug", "--reload"]