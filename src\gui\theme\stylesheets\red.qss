/* 红色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #FFEBEE;
}

QLabel {
    color: #B71C1C;
}

QPushButton {
    background-color: #E53935;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #D32F2F;
}

QPushButton:pressed {
    background-color: #C62828;
}

QPushButton:disabled {
    background-color: #FFCDD2;
    color: #EF9A9A;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #B71C1C;
    border: 1px solid #EF9A9A;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #E53935;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #FFEBEE;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #EF9A9A;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #FFEBEE;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #EF9A9A;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #E53935;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #D32F2F;
}

QMenu {
    background-color: #FFFFFF;
    color: #B71C1C;
    border: 1px solid #EF9A9A;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #FFEBEE;
}

QTabWidget::pane {
    border: 1px solid #EF9A9A;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #FFCDD2;
    color: #B71C1C;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #E53935;
}

QStatusBar {
    background-color: #E53935;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #E53935;
}

#sidebarWidget {
    background-color: #E53935;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #FFEBEE;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #EF9A9A;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #E53935;
    margin-bottom: 8px;
}

.card-content {
    color: #B71C1C;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #E53935;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #E53935;
    box-shadow: 0 0 10px #E53935;
}
