
[tox]
envlist = py38, py39, py310, lint, type, docs
isolated_build = True
skip_missing_interpreters = True

[testenv]
deps =
    -r{toxinidir}/requirements.txt
    -r{toxinidir}/requirements-dev.txt
commands =
    pytest {posargs:tests}

[testenv:lint]
deps =
    flake8
    black
    isort
    pylint
commands =
    flake8 src tests
    black --check src tests
    isort --check-only src tests
    pylint src tests

[testenv:type]
deps =
    mypy
    types-PyYAML
    types-setuptools
commands =
    mypy src

[testenv:docs]
deps =
    sphinx
    sphinx-rtd-theme
    sphinx-autodoc-typehints
commands =
    sphinx-build -W -b html docs/source docs/build/html

[testenv:security]
deps =
    bandit
    safety
commands =
    bandit -r src
    safety check

[testenv:coverage]
deps =
    pytest
    pytest-cov
commands =
    pytest --cov=src --cov-report=xml --cov-report=term-missing tests/

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = .tox,*.egg,build,data
select = E,W,F

[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --cov=src --cov-report=term-missing
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    gpu: marks tests that require GPU

[coverage:run]
source = src
omit = tests/*,setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError

[isort]
profile = black
multi_line_output = 3
include_trailing_comma = True
force_grid_wrap = 0
use_parentheses = True
ensure_newline_before_comments = True
line_length = 88

[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

[bandit]
exclude = tests,examples
skips = B101,B404,B603
