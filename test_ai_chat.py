#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI对话功能测试脚本
"""

import sys
import logging
from pathlib import Path
import json
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIChatTester:
    """AI对话测试类"""

    def __init__(self):
        self.test_results = {
            'start_time': datetime.now().isoformat(),
            'chat_tests': {},
            'retrieval_tests': {},
            'integration_tests': {}
        }

    def test_retrieval_integration(self):
        """测试检索集成功能"""
        logger.info("🔍 测试检索集成功能...")

        test_queries = [
            "如何搭建企业级知识库？",
            "向量化模型需要训练吗？",
            "FAISS索引的优势是什么？",
            "文档处理支持哪些格式？"
        ]

        results = {}

        try:
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager
            import numpy as np

            # 创建嵌入器
            config = {
                'vectorization': {
                    'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                    'vector_dimension': 384,
                    'device': 'cpu'
                }
            }

            embedder = TextEmbedding(config)
            metadata_manager = MetadataManager({'storage': {'base_dir': 'data/vectors'}})

            for query in test_queries:
                try:
                    # 向量化查询
                    query_vector = embedder.encode_text(query)

                    # 模拟检索（由于没有实际的索引数据，这里只测试向量化）
                    results[query] = {
                        'status': 'success',
                        'query_vector_shape': query_vector.shape,
                        'vector_norm': float(np.linalg.norm(query_vector))
                    }

                    logger.info(f"✅ 查询向量化成功: {query[:20]}...")

                except Exception as e:
                    results[query] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    logger.error(f"❌ 查询处理失败: {query[:20]}... - {e}")

            self.test_results['retrieval_tests'] = {
                'status': 'completed',
                'results': results,
                'success_rate': sum(1 for r in results.values() if r['status'] == 'success') / len(results)
            }

        except Exception as e:
            self.test_results['retrieval_tests'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"检索集成测试失败: {e}")

    def test_mock_ai_responses(self):
        """测试模拟AI响应"""
        logger.info("🤖 测试模拟AI响应...")

        test_scenarios = [
            {
                'query': '如何搭建企业级知识库？',
                'context': '企业级知识库需要考虑架构设计、技术选型、部署流程等方面...',
                'expected_topics': ['架构', '技术选型', '部署']
            },
            {
                'query': '向量化模型需要训练吗？',
                'context': '量化索引模型需要训练，HNSW索引不需要传统训练...',
                'expected_topics': ['训练', '模型', '索引']
            }
        ]

        results = {}

        for scenario in test_scenarios:
            try:
                # 模拟AI响应生成
                mock_response = self._generate_mock_response(
                    scenario['query'],
                    scenario['context']
                )

                # 评估响应质量
                quality_score = self._evaluate_response_quality(
                    mock_response,
                    scenario['expected_topics']
                )

                results[scenario['query']] = {
                    'status': 'success',
                    'response': mock_response,
                    'quality_score': quality_score,
                    'response_length': len(mock_response)
                }

                logger.info(f"✅ AI响应生成成功: {scenario['query'][:20]}...")

            except Exception as e:
                results[scenario['query']] = {
                    'status': 'failed',
                    'error': str(e)
                }
                logger.error(f"❌ AI响应生成失败: {scenario['query'][:20]}... - {e}")

        self.test_results['chat_tests'] = {
            'status': 'completed',
            'results': results,
            'avg_quality_score': sum(r.get('quality_score', 0) for r in results.values()) / len(results)
        }

    def _generate_mock_response(self, query, context):
        """生成模拟AI响应"""
        # 这里实现一个简单的模板响应生成器
        templates = {
            '如何': '根据提供的信息，{topic}的步骤如下：\n1. 首先需要{step1}\n2. 然后进行{step2}\n3. 最后完成{step3}',
            '什么': '根据文档内容，{topic}是指{definition}。主要特点包括：{features}',
            '需要': '是的，{topic}确实需要{requirement}。具体来说：{details}'
        }

        # 简单的关键词匹配
        for keyword, template in templates.items():
            if keyword in query:
                if '搭建' in query:
                    return template.format(
                        topic='企业级知识库搭建',
                        step1='进行需求分析和架构设计',
                        step2='选择合适的技术栈',
                        step3='部署和优化系统'
                    )
                elif '训练' in query:
                    return template.format(
                        topic='向量化模型',
                        requirement='根据具体类型进行训练',
                        details='量化索引需要训练，而HNSW索引主要是构建过程'
                    )

        # 默认响应
        return f"根据相关文档，关于「{query}」的问题，需要结合具体的上下文信息来提供准确的答案。建议查看相关技术文档获取更详细的信息。"

    def _evaluate_response_quality(self, response, expected_topics):
        """评估响应质量"""
        score = 0.0

        # 检查是否包含期望的主题
        for topic in expected_topics:
            if topic in response:
                score += 1.0

        # 标准化分数
        if expected_topics:
            score = score / len(expected_topics)

        # 检查响应长度（太短或太长都不好）
        length_score = 1.0
        if len(response) < 50:
            length_score = 0.5
        elif len(response) > 1000:
            length_score = 0.8

        return (score + length_score) / 2

    def test_integration_workflow(self):
        """测试集成工作流"""
        logger.info("🔄 测试集成工作流...")

        workflow_steps = [
            '文档加载',
            '文本分块',
            '向量化',
            '索引存储',
            '查询处理',
            'AI响应生成'
        ]

        results = {}

        try:
            # 模拟完整的工作流
            for step in workflow_steps:
                try:
                    # 模拟每个步骤的执行
                    step_result = self._simulate_workflow_step(step)
                    results[step] = {
                        'status': 'success',
                        'execution_time': step_result.get('time', 0.1),
                        'output_size': step_result.get('size', 100)
                    }
                    logger.info(f"✅ 工作流步骤完成: {step}")

                except Exception as e:
                    results[step] = {
                        'status': 'failed',
                        'error': str(e)
                    }
                    logger.error(f"❌ 工作流步骤失败: {step} - {e}")

            # 计算整体成功率
            success_rate = sum(1 for r in results.values() if r['status'] == 'success') / len(results)

            self.test_results['integration_tests'] = {
                'status': 'completed',
                'workflow_results': results,
                'success_rate': success_rate,
                'total_steps': len(workflow_steps)
            }

        except Exception as e:
            self.test_results['integration_tests'] = {
                'status': 'failed',
                'error': str(e)
            }
            logger.error(f"集成工作流测试失败: {e}")

    def _simulate_workflow_step(self, step):
        """模拟工作流步骤"""
        import time
        import random

        # 模拟执行时间
        execution_time = random.uniform(0.05, 0.3)
        time.sleep(execution_time)

        # 模拟输出大小
        output_sizes = {
            '文档加载': 5000,
            '文本分块': 10,
            '向量化': 384,
            '索引存储': 1,
            '查询处理': 5,
            'AI响应生成': 200
        }

        return {
            'time': execution_time,
            'size': output_sizes.get(step, 100)
        }

    def run_all_tests(self):
        """运行所有AI对话测试"""
        logger.info("🚀 开始AI对话功能测试...")

        # 运行各项测试
        self.test_retrieval_integration()
        self.test_mock_ai_responses()
        self.test_integration_workflow()

        # 生成报告
        self.generate_report()

        return self.test_results

    def generate_report(self):
        """生成测试报告"""
        logger.info("📊 生成AI对话测试报告...")

        self.test_results['end_time'] = datetime.now().isoformat()

        # 保存报告
        report_file = f"ai_chat_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)

        logger.info(f"AI对话测试报告已保存: {report_file}")

        # 打印摘要
        print("\n" + "="*60)
        print("🤖 AI对话功能测试报告摘要")
        print("="*60)

        # 检索测试结果
        retrieval_status = self.test_results.get('retrieval_tests', {}).get('status', 'unknown')
        retrieval_rate = self.test_results.get('retrieval_tests', {}).get('success_rate', 0)
        print(f"检索集成测试: {retrieval_status} (成功率: {retrieval_rate:.1%})")

        # AI响应测试结果
        chat_status = self.test_results.get('chat_tests', {}).get('status', 'unknown')
        chat_quality = self.test_results.get('chat_tests', {}).get('avg_quality_score', 0)
        print(f"AI响应测试: {chat_status} (平均质量分: {chat_quality:.2f})")

        # 集成测试结果
        integration_status = self.test_results.get('integration_tests', {}).get('status', 'unknown')
        integration_rate = self.test_results.get('integration_tests', {}).get('success_rate', 0)
        print(f"集成工作流测试: {integration_status} (成功率: {integration_rate:.1%})")

        print("="*60)

if __name__ == "__main__":
    tester = AIChatTester()
    results = tester.run_all_tests()
