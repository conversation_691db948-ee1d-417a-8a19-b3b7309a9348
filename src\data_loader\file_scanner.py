
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件扫描器模块
负责扫描目录、过滤文件和收集文件元数据
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Generator, Any, Optional
from dataclasses import dataclass

@dataclass
class FileMetadata:
    """文件元数据类"""
    path: Path
    filename: str
    extension: str
    size: int
    created_time: datetime
    modified_time: datetime
    relative_path: str
    
    @classmethod
    def from_path(cls, file_path: Path, base_dir: Path) -> 'FileMetadata':
        """从文件路径创建元数据对象"""
        stat = file_path.stat()
        return cls(
            path=file_path,
            filename=file_path.name,
            extension=file_path.suffix.lower(),
            size=stat.st_size,
            created_time=datetime.fromtimestamp(stat.st_ctime),
            modified_time=datetime.fromtimestamp(stat.st_mtime),
            relative_path=str(file_path.relative_to(base_dir))
        )

class FileScanner:
    """文件扫描器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化文件扫描器
        
        Args:
            config: 配置字典，包含文件处理相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.input_dir = Path(config['file_processing']['input_dir'])
        self.supported_extensions = set(config['file_processing']['supported_extensions'])
        self.batch_size = config['file_processing']['batch_size']
        
    def validate_directory(self) -> bool:
        """
        验证输入目录是否存在且可访问
        
        Returns:
            bool: 目录是否有效
        """
        try:
            if not self.input_dir.exists():
                self.logger.error(f"输入目录不存在: {self.input_dir}")
                return False
            if not self.input_dir.is_dir():
                self.logger.error(f"指定路径不是目录: {self.input_dir}")
                return False
            # 测试目录权限
            test_file = self.input_dir / '.test_access'
            try:
                test_file.touch()
                test_file.unlink()
            except Exception as e:
                self.logger.error(f"目录权限测试失败: {e}")
                return False
            return True
        except Exception as e:
            self.logger.error(f"验证目录时出错: {e}")
            return False
    
    def is_valid_file(self, file_path: Path) -> bool:
        """
        检查文件是否符合处理要求
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否有效
        """
        try:
            # 检查文件扩展名
            if file_path.suffix.lower() not in self.supported_extensions:
                return False
            
            # 检查文件大小
            if file_path.stat().st_size == 0:
                return False
            
            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                return False
            
            return True
        except Exception as e:
            self.logger.warning(f"检查文件 {file_path} 时出错: {e}")
            return False
    
    def scan_files(self) -> Generator[List[FileMetadata], None, None]:
        """
        扫描目录并以批次形式生成文件元数据
        
        Yields:
            List[FileMetadata]: 文件元数据列表，每批最多包含 batch_size 个文件
        """
        if not self.validate_directory():
            self.logger.error("目录验证失败，停止扫描")
            return
        
        current_batch: List[FileMetadata] = []
        
        try:
            for file_path in self.input_dir.rglob('*'):
                if file_path.is_file() and self.is_valid_file(file_path):
                    try:
                        metadata = FileMetadata.from_path(file_path, self.input_dir)
                        current_batch.append(metadata)
                        
                        if len(current_batch) >= self.batch_size:
                            yield current_batch
                            current_batch = []
                    except Exception as e:
                        self.logger.warning(f"处理文件 {file_path} 时出错: {e}")
                        continue
            
            # 返回最后一批文件
            if current_batch:
                yield current_batch
                
        except Exception as e:
            self.logger.error(f"扫描文件时出错: {e}")
            if current_batch:
                yield current_batch
    
    def get_file_count(self) -> int:
        """
        获取符合条件的文件总数
        
        Returns:
            int: 文件总数
        """
        try:
            count = sum(1 for file_path in self.input_dir.rglob('*')
                       if file_path.is_file() and self.is_valid_file(file_path))
            return count
        except Exception as e:
            self.logger.error(f"计算文件数量时出错: {e}")
            return 0
    
    def get_file_metadata(self, file_path: Path) -> Optional[FileMetadata]:
        """
        获取单个文件的元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[FileMetadata]: 文件元数据，如果出错则返回None
        """
        try:
            if not self.is_valid_file(file_path):
                return None
            return FileMetadata.from_path(file_path, self.input_dir)
        except Exception as e:
            self.logger.error(f"获取文件 {file_path} 的元数据时出错: {e}")
            return None

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'file_processing': {
            'input_dir': '.',
            'supported_extensions': ['.md'],
            'batch_size': 10
        }
    }
    
    scanner = FileScanner(test_config)
    
    print(f"Total files: {scanner.get_file_count()}")
    
    for batch in scanner.scan_files():
        for metadata in batch:
            print(f"File: {metadata.relative_path}, "
                  f"Size: {metadata.size}, "
                  f"Modified: {metadata.modified_time}")