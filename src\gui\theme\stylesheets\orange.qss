/* 橙色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #FFF3E0;
}

QLabel {
    color: #E65100;
}

QPushButton {
    background-color: #F57C00;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #EF6C00;
}

QPushButton:pressed {
    background-color: #E65100;
}

QPushButton:disabled {
    background-color: #FFE0B2;
    color: #FFCC80;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #E65100;
    border: 1px solid #FFCC80;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #F57C00;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #FFF3E0;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #FFCC80;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #FFF3E0;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #FFCC80;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #F57C00;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #EF6C00;
}

QMenu {
    background-color: #FFFFFF;
    color: #E65100;
    border: 1px solid #FFCC80;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #FFF3E0;
}

QTabWidget::pane {
    border: 1px solid #FFCC80;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #FFE0B2;
    color: #E65100;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    color: #F57C00;
}

QStatusBar {
    background-color: #F57C00;
    color: #FFFFFF;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #F57C00;
}

#sidebarWidget {
    background-color: #F57C00;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #FFFFFF;
}

#dashboardWidget {
    background-color: #FFF3E0;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    border: 1px solid #FFCC80;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #F57C00;
    margin-bottom: 8px;
}

.card-content {
    color: #E65100;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #F57C00;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #F57C00;
    box-shadow: 0 0 10px #F57C00;
}
