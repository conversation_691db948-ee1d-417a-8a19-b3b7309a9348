#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试语言切换功能
"""

import sys
import enum
import traceback
from typing import Dict, Optional

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget,
        QComboBox, QPushButton, QMenuBar, QMenu, QStatusBar
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QAction
    qt_version = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget,
        QComboBox, QPushButton, QMenuBar, QMenu, QStatusBar
    )
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QAction
    qt_version = 5

class Language(enum.Enum):
    """支持的语言枚举"""
    CHINESE = "zh_CN"
    ENGLISH = "en_US"
    
    @property
    def display_name(self) -> str:
        """获取语言的显示名称"""
        names = {
            Language.CHINESE: "简体中文",
            Language.ENGLISH: "English",
        }
        return names.get(self, "Unknown")

class Translator:
    """简单的翻译器类"""
    
    def __init__(self, default_language: Language = Language.ENGLISH):
        """初始化翻译器"""
        self.current_language = default_language
        self.translations: Dict[Language, Dict[str, str]] = {
            Language.ENGLISH: {
                "app_title": "Language Switch Test",
                "language": "Language",
                "language_menu": "Language",
                "file_menu": "File",
                "exit": "Exit",
                "status_ready": "Ready",
                "switch_language": "Switch Language",
                "current_language": "Current Language",
            },
            Language.CHINESE: {
                "app_title": "语言切换测试",
                "language": "语言",
                "language_menu": "语言",
                "file_menu": "文件",
                "exit": "退出",
                "status_ready": "就绪",
                "switch_language": "切换语言",
                "current_language": "当前语言",
            }
        }
        self.observers = []
    
    def set_language(self, language: Language):
        """设置当前语言"""
        if language != self.current_language:
            self.current_language = language
            self._notify_language_changed()
    
    def get_text(self, key: str, default: Optional[str] = None) -> str:
        """获取指定键的翻译文本"""
        translation = self.translations.get(self.current_language, {}).get(key)
        if translation is not None:
            return translation
        
        if self.current_language != Language.ENGLISH:
            translation = self.translations.get(Language.ENGLISH, {}).get(key)
            if translation is not None:
                return translation
        
        return default if default is not None else key
    
    def add_observer(self, observer):
        """添加语言变更观察者"""
        if observer not in self.observers:
            self.observers.append(observer)
    
    def remove_observer(self, observer):
        """移除语言变更观察者"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def _notify_language_changed(self):
        """通知所有观察者语言已更改"""
        print(f"通知观察者语言已更改为: {self.current_language.value}")
        for observer in self.observers:
            try:
                if hasattr(observer, 'on_language_changed'):
                    print(f"通知观察者: {observer.__class__.__name__}")
                    observer.on_language_changed()
                else:
                    print(f"观察者 {observer.__class__.__name__} 没有 on_language_changed 方法")
            except Exception as e:
                print(f"通知观察者 {observer.__class__.__name__} 时出错: {e}")
                traceback.print_exc()

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 创建翻译器
        self.translator = Translator()
        self.translator.add_observer(self)
        
        # 设置窗口属性
        self.setWindowTitle(self.translator.get_text("app_title"))
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建标签
        self.language_label = QLabel(f"{self.translator.get_text('current_language')}: {self.translator.current_language.display_name}")
        layout.addWidget(self.language_label)
        
        # 创建语言下拉框
        self.language_combo = QComboBox()
        for lang in Language:
            self.language_combo.addItem(lang.display_name, lang)
        self.language_combo.setCurrentIndex(list(Language).index(self.translator.current_language))
        self.language_combo.currentIndexChanged.connect(self._on_combo_language_changed)
        layout.addWidget(self.language_combo)
        
        # 创建切换语言按钮
        self.switch_button = QPushButton(self.translator.get_text("switch_language"))
        self.switch_button.clicked.connect(self._on_switch_button_clicked)
        layout.addWidget(self.switch_button)
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()
        menu_bar.clear()  # 清除现有菜单
        
        # 文件菜单
        file_menu = menu_bar.addMenu(self.translator.get_text("file_menu"))
        
        exit_action = QAction(self.translator.get_text("exit"), self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 语言菜单
        language_menu = menu_bar.addMenu(self.translator.get_text("language_menu"))
        
        # 添加支持的语言
        for lang in Language:
            lang_action = QAction(lang.display_name, self)
            lang_action.setCheckable(True)
            lang_action.setChecked(self.translator.current_language == lang)
            lang_action.triggered.connect(lambda checked, l=lang: self._on_language_changed(l))
            language_menu.addAction(lang_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 添加状态标签
        self.status_label = QLabel(self.translator.get_text("status_ready"))
        status_bar.addWidget(self.status_label)
        
        # 添加语言标签
        self.status_language_label = QLabel(f"{self.translator.get_text('language')}: {self.translator.current_language.display_name}")
        status_bar.addPermanentWidget(self.status_language_label)
    
    def _on_combo_language_changed(self, index):
        """语言下拉框变更处理"""
        lang = list(Language)[index]
        self._on_language_changed(lang)
    
    def _on_switch_button_clicked(self):
        """切换语言按钮点击处理"""
        current_index = self.language_combo.currentIndex()
        next_index = (current_index + 1) % len(Language)
        self.language_combo.setCurrentIndex(next_index)
    
    def _on_language_changed(self, language: Language):
        """语言变更处理"""
        try:
            print(f"语言变更处理: {language.value}")
            
            # 直接设置当前语言，不通过观察者模式
            self.translator.current_language = language
            
            # 手动调用语言变更回调
            self.on_language_changed()
            
            print("语言变更处理完成")
        except Exception as e:
            print(f"语言变更处理出错: {e}")
            traceback.print_exc()
    
    def on_language_changed(self):
        """语言变更回调"""
        try:
            print("语言变更回调开始执行")
            
            # 更新窗口标题
            self.setWindowTitle(self.translator.get_text("app_title"))
            
            # 更新标签
            self.language_label.setText(f"{self.translator.get_text('current_language')}: {self.translator.current_language.display_name}")
            
            # 更新按钮
            self.switch_button.setText(self.translator.get_text("switch_language"))
            
            # 更新菜单
            self._create_menu_bar()
            
            # 更新状态栏
            self.status_label.setText(self.translator.get_text("status_ready"))
            self.status_language_label.setText(f"{self.translator.get_text('language')}: {self.translator.current_language.display_name}")
            
            print("语言变更回调执行完成")
        except Exception as e:
            print(f"语言变更回调执行出错: {e}")
            traceback.print_exc()

def run_test():
    """运行测试"""
    try:
        app = QApplication(sys.argv)
        window = MainWindow()
        window.show()
        sys.exit(app.exec() if qt_version == 6 else app.exec_())
    except Exception as e:
        print(f"运行测试时出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    run_test()
