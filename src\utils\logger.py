
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志管理模块
负责配置和管理日志系统
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
import sys
import json
from datetime import datetime
import os
import gzip
import shutil
from concurrent_log_handler import ConcurrentRotatingFileHandler

class LogFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self, *args, **kwargs):
        """初始化格式化器"""
        super().__init__(*args, **kwargs)
        self.default_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self.debug_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        
        # 不同级别使用不同的颜色
        self.COLORS = {
            'DEBUG': '\033[36m',     # 青色
            'INFO': '\033[32m',      # 绿色
            'WARNING': '\033[33m',   # 黄色
            'ERROR': '\033[31m',     # 红色
            'CRITICAL': '\033[35m'   # 紫色
        }
        self.RESET = '\033[0m'
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            str: 格式化后的日志字符串
        """
        # 选择格式
        if record.levelno <= logging.DEBUG:
            self._style._fmt = self.debug_format
        else:
            self._style._fmt = self.default_format
        
        # 格式化消息
        formatted = super().format(record)
        
        # 如果是控制台输出且支持颜色，添加颜色
        if sys.stdout.isatty():
            color = self.COLORS.get(record.levelname, '')
            if color:
                formatted = f"{color}{formatted}{self.RESET}"
        
        return formatted

class CompressedRotatingFileHandler(ConcurrentRotatingFileHandler):
    """支持压缩的日志文件处理器"""
    
    def doRollover(self):
        """执行日志轮转并压缩旧日志"""
        # 执行基础的轮转
        super().doRollover()
        
        # 压缩旧的日志文件
        if self.backupCount > 0:
            for i in range(1, self.backupCount + 1):
                log_file = f"{self.baseFilename}.{i}"
                gz_file = f"{log_file}.gz"
                
                if os.path.exists(log_file):
                    try:
                        with open(log_file, 'rb') as f_in:
                            with gzip.open(gz_file, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        os.remove(log_file)
                    except Exception as e:
                        sys.stderr.write(f"压缩日志文件时出错: {e}\n")

def setup_logger(config: Dict[str, Any], module_name: Optional[str] = None) -> logging.Logger:
    """
    配置并返回日志记录器
    
    Args:
        config: 配置字典，包含日志相关的配置
        module_name: 模块名称（可选）
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    try:
        # 获取日志配置
        log_config = config.get('logging', {})
        log_level = getattr(logging, log_config.get('log_level', 'INFO'))
        log_dir = Path(log_config.get('log_dir', 'logs'))
        log_file = log_config.get('log_file', 'processor.log')
        max_file_size = log_config.get('max_file_size', '10MB')
        backup_count = log_config.get('backup_count', 5)
        console_log = log_config.get('console_log', True)
        
        # 创建日志目录
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志记录器
        logger_name = module_name if module_name else 'md_vector_processor'
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = LogFormatter()
        
        # 添加文件处理器
        file_path = log_dir / log_file
        # 将文件大小字符串转换为字节数
        size_map = {'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024}
        size_str = max_file_size.upper()
        multiplier = size_map.get(size_str[-2:], 1)
        max_bytes = int(size_str[:-2]) * multiplier
        
        file_handler = CompressedRotatingFileHandler(
            filename=str(file_path),
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 添加控制台处理器
        if console_log:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 记录初始化信息
        logger.info(f"日志系统初始化完成 - 级别: {logging.getLevelName(log_level)}")
        logger.debug(f"日志配置: {json.dumps(log_config, indent=2)}")
        
        return logger
        
    except Exception as e:
        # 如果配置失败，设置基本的控制台日志
        basic_logger = logging.getLogger(module_name if module_name else 'md_vector_processor')
        basic_logger.setLevel(logging.INFO)
        
        if not basic_logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            handler.setFormatter(LogFormatter())
            basic_logger.addHandler(handler)
        
        basic_logger.error(f"配置日志系统时出错: {e}")
        return basic_logger

class LogManager:
    """日志管理器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化日志管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.loggers: Dict[str, logging.Logger] = {}
        
        # 设置根日志记录器
        self.root_logger = setup_logger(config)
        self.loggers['root'] = self.root_logger
    
    def get_logger(self, module_name: str) -> logging.Logger:
        """
        获取或创建模块的日志记录器
        
        Args:
            module_name: 模块名称
            
        Returns:
            logging.Logger: 日志记录器
        """
        if module_name not in self.loggers:
            self.loggers[module_name] = setup_logger(self.config, module_name)
        return self.loggers[module_name]
    
    def set_level(self, level: str, module_name: Optional[str] = None) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别
            module_name: 模块名称（可选，如果未指定则设置所有日志记录器）
        """
        log_level = getattr(logging, level.upper())
        
        if module_name:
            if module_name in self.loggers:
                self.loggers[module_name].setLevel(log_level)
        else:
            for logger in self.loggers.values():
                logger.setLevel(log_level)
    
    def cleanup_logs(self, days: int = 30) -> None:
        """
        清理旧的日志文件
        
        Args:
            days: 保留的天数
        """
        try:
            log_dir = Path(self.config.get('logging', {}).get('log_dir', 'logs'))
            if not log_dir.exists():
                return
            
            current_time = datetime.now().timestamp()
            max_age = days * 24 * 60 * 60  # 转换为秒
            
            for file_path in log_dir.glob('*.gz'):
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age:
                    file_path.unlink()
                    self.root_logger.info(f"删除了旧日志文件: {file_path}")
                    
        except Exception as e:
            self.root_logger.error(f"清理日志文件时出错: {e}")

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'logging': {
            'log_level': 'DEBUG',
            'log_dir': 'logs',
            'log_file': 'test.log',
            'max_file_size': '1MB',
            'backup_count': 3,
            'console_log': True
        }
    }
    
    # 测试日志管理器
    log_manager = LogManager(test_config)
    
    # 获取不同模块的日志记录器
    main_logger = log_manager.get_logger('main')
    data_logger = log_manager.get_logger('data_processor')
    
    # 测试不同级别的日志
    main_logger.debug("这是一条调试信息")
    main_logger.info("这是一条信息")
    main_logger.warning("这是一条警告")
    main_logger.error("这是一条错误")
    
    data_logger.info("数据处理模块的日志")
    
    # 测试更改日志级别
    log_manager.set_level('INFO', 'main')
    main_logger.debug("这条调试信息不会显示")
    
    # 清理旧日志
    log_manager.cleanup_logs(days=7)