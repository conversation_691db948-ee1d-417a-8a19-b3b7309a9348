#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证第一阶段增强效果
"""

import json
from pathlib import Path
from collections import Counter
import statistics

def verify_phase1_enhancement():
    """验证第一阶段增强效果"""
    
    print("🔍 验证第一阶段增强效果")
    print("=" * 80)
    
    metadata_dir = Path("training_data/raw_documents/enterprise_standards/metadata")
    metadata_files = list(metadata_dir.glob("*_metadata.json"))
    
    # 统计数据
    stats = {
        'total_files': len(metadata_files),
        'has_standard_number': 0,
        'has_keywords': 0,
        'has_positive_relevance': 0,
        'has_specific_domains': 0,
        'high_technical_depth': 0,
        'medium_technical_depth': 0,
        'low_technical_depth': 0
    }
    
    # 详细分析
    standard_numbers = []
    keyword_counts = []
    relevance_scores = []
    technical_domains = []
    
    # 样本展示
    sample_improvements = []
    
    for i, file_path in enumerate(metadata_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 统计改进情况
            if metadata.get('standard_number'):
                stats['has_standard_number'] += 1
                standard_numbers.append(metadata['standard_number'])
            
            keywords = metadata.get('keywords', [])
            if keywords:
                stats['has_keywords'] += 1
                keyword_counts.append(len(keywords))
            
            relevance = metadata.get('automotive_relevance', 0)
            if relevance > 0:
                stats['has_positive_relevance'] += 1
                relevance_scores.append(relevance)
            
            domains = metadata.get('technical_domains', [])
            if domains and domains != ['general']:
                stats['has_specific_domains'] += 1
            technical_domains.extend(domains)
            
            depth = metadata.get('technical_depth', 'low')
            if depth == 'high':
                stats['high_technical_depth'] += 1
            elif depth == 'medium':
                stats['medium_technical_depth'] += 1
            else:
                stats['low_technical_depth'] += 1
            
            # 收集前10个改进样本
            if i < 10:
                sample_improvements.append({
                    'filename': file_path.name,
                    'title': metadata.get('title', 'N/A'),
                    'standard_number': metadata.get('standard_number', 'NULL'),
                    'keywords': keywords,
                    'automotive_relevance': relevance,
                    'technical_domains': domains,
                    'technical_depth': depth
                })
                
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path.name}: {e}")
    
    # 显示统计结果
    print(f"📈 增强效果统计:")
    print("-" * 60)
    total = stats['total_files']
    
    print(f"标准号完整率:     {stats['has_standard_number']:4d} / {total} ({stats['has_standard_number']/total*100:.1f}%)")
    print(f"关键词完整率:     {stats['has_keywords']:4d} / {total} ({stats['has_keywords']/total*100:.1f}%)")
    print(f"相关性有效率:     {stats['has_positive_relevance']:4d} / {total} ({stats['has_positive_relevance']/total*100:.1f}%)")
    print(f"技术领域特化率:   {stats['has_specific_domains']:4d} / {total} ({stats['has_specific_domains']/total*100:.1f}%)")
    
    print(f"\n技术深度分布:")
    print(f"  高深度:         {stats['high_technical_depth']:4d} / {total} ({stats['high_technical_depth']/total*100:.1f}%)")
    print(f"  中等深度:       {stats['medium_technical_depth']:4d} / {total} ({stats['medium_technical_depth']/total*100:.1f}%)")
    print(f"  低深度:         {stats['low_technical_depth']:4d} / {total} ({stats['low_technical_depth']/total*100:.1f}%)")
    
    # 关键词统计
    if keyword_counts:
        print(f"\n🏷️ 关键词统计:")
        print(f"  平均关键词数:   {statistics.mean(keyword_counts):.1f}")
        print(f"  最多关键词数:   {max(keyword_counts)}")
        print(f"  最少关键词数:   {min(keyword_counts)}")
    
    # 相关性统计
    if relevance_scores:
        print(f"\n📊 汽车相关性统计:")
        print(f"  平均相关性:     {statistics.mean(relevance_scores):.3f}")
        print(f"  最高相关性:     {max(relevance_scores):.3f}")
        print(f"  最低相关性:     {min(relevance_scores):.3f}")
        
        # 相关性分布
        high_relevance = sum(1 for r in relevance_scores if r >= 0.6)
        medium_relevance = sum(1 for r in relevance_scores if 0.3 <= r < 0.6)
        low_relevance = sum(1 for r in relevance_scores if 0 < r < 0.3)
        
        print(f"  高相关性(≥0.6): {high_relevance} ({high_relevance/len(relevance_scores)*100:.1f}%)")
        print(f"  中相关性(0.3-0.6): {medium_relevance} ({medium_relevance/len(relevance_scores)*100:.1f}%)")
        print(f"  低相关性(0-0.3): {low_relevance} ({low_relevance/len(relevance_scores)*100:.1f}%)")
    
    # 技术领域分布
    domain_counter = Counter(technical_domains)
    print(f"\n🔬 技术领域分布 (前10):")
    for domain, count in domain_counter.most_common(10):
        print(f"  {domain}: {count}")
    
    # 标准号类型分布
    if standard_numbers:
        standard_prefixes = [std.split('-')[0] for std in standard_numbers if '-' in std]
        prefix_counter = Counter(standard_prefixes)
        print(f"\n📋 标准号类型分布:")
        for prefix, count in prefix_counter.most_common(10):
            print(f"  {prefix}: {count}")
    
    # 显示改进样本
    print(f"\n📋 改进样本展示 (前10个):")
    print("-" * 80)
    
    for i, sample in enumerate(sample_improvements, 1):
        print(f"\n{i}. {sample['filename'][:60]}...")
        print(f"   标题: {sample['title'][:50]}...")
        print(f"   标准号: {sample['standard_number']}")
        print(f"   关键词: {sample['keywords'][:5]}{'...' if len(sample['keywords']) > 5 else ''}")
        print(f"   汽车相关性: {sample['automotive_relevance']:.3f}")
        print(f"   技术领域: {sample['technical_domains']}")
        print(f"   技术深度: {sample['technical_depth']}")
    
    # 改进对比
    print(f"\n📈 改进对比 (与原始分析对比):")
    print("-" * 60)
    print("指标                  原始状态    增强后状态    改进幅度")
    print("-" * 60)
    
    # 原始数据 (来自之前的分析)
    original_stats = {
        'standard_number_rate': 37.5,  # 100% - 62.5%
        'keywords_rate': 25.7,         # 100% - 74.3%
        'relevance_rate': 25.7,        # 100% - 74.3%
        'specific_domains_rate': 5.6   # 100% - 94.4%
    }
    
    current_stats = {
        'standard_number_rate': stats['has_standard_number']/total*100,
        'keywords_rate': stats['has_keywords']/total*100,
        'relevance_rate': stats['has_positive_relevance']/total*100,
        'specific_domains_rate': stats['has_specific_domains']/total*100
    }
    
    for metric in ['standard_number_rate', 'keywords_rate', 'relevance_rate', 'specific_domains_rate']:
        original = original_stats[metric]
        current = current_stats[metric]
        improvement = current - original
        
        metric_name = {
            'standard_number_rate': '标准号完整率',
            'keywords_rate': '关键词完整率', 
            'relevance_rate': '相关性有效率',
            'specific_domains_rate': '技术领域特化率'
        }[metric]
        
        print(f"{metric_name:<15} {original:6.1f}%     {current:6.1f}%     +{improvement:5.1f}%")
    
    # 质量评估
    print(f"\n🎯 质量评估:")
    print("-" * 60)
    
    quality_score = (
        current_stats['standard_number_rate'] * 0.25 +
        current_stats['keywords_rate'] * 0.30 +
        current_stats['relevance_rate'] * 0.30 +
        current_stats['specific_domains_rate'] * 0.15
    ) / 100
    
    print(f"综合质量评分: {quality_score:.3f} / 1.000")
    
    if quality_score >= 0.8:
        print("🎉 优秀! 元数据质量大幅提升，可以进行高质量训练")
    elif quality_score >= 0.6:
        print("✅ 良好! 元数据质量显著改善，训练效果会明显提升")
    elif quality_score >= 0.4:
        print("⚠️ 一般! 有所改善，但仍有优化空间")
    else:
        print("❌ 需要进一步改进")
    
    print(f"\n💡 建议:")
    print("-" * 60)
    if current_stats['standard_number_rate'] < 90:
        print("- 标准号提取仍需优化，考虑添加更多模式匹配规则")
    if current_stats['keywords_rate'] < 85:
        print("- 关键词提取可以进一步增强，考虑添加更多专业词汇")
    if current_stats['relevance_rate'] < 80:
        print("- 汽车相关性算法需要调优，可能需要调整权重")
    if current_stats['specific_domains_rate'] < 70:
        print("- 技术领域分类需要更细致的规则")
    
    print("- 可以考虑进入第二阶段：添加PDF内容解析")
    print("- 建议运行训练流程验证实际效果")

if __name__ == "__main__":
    verify_phase1_enhancement()
