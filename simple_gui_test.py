#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的 GUI 测试
"""

import sys
import traceback

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel,
        QPushButton, QComboBox, QMenuBar, QMenu, QStatusBar
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QAction, QActionGroup
    qt_version = 6
except ImportError:
    try:
        from PyQt5.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel,
            QPushButton, QComboBox, QMenuBar, QMenu, QStatusBar, QActionGroup
        )
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QAction
        qt_version = 5
    except ImportError:
        print("错误: 无法导入 PyQt5 或 PyQt6")
        sys.exit(1)

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("简单的 GUI 测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建标签
        self.label = QLabel("这是一个简单的 GUI 测试")
        layout.addWidget(self.label)
        
        # 创建下拉框
        self.combo = QComboBox()
        self.combo.addItems(["选项 1", "选项 2", "选项 3"])
        self.combo.currentIndexChanged.connect(self._on_combo_changed)
        layout.addWidget(self.combo)
        
        # 创建按钮
        self.button = QPushButton("点击我")
        self.button.clicked.connect(self._on_button_clicked)
        layout.addWidget(self.button)
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()
        
        # 文件菜单
        file_menu = menu_bar.addMenu("文件")
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 主题菜单
        theme_menu = menu_bar.addMenu("主题")
        
        # 创建互斥的主题动作组
        theme_action_group = QActionGroup(self)
        theme_action_group.setExclusive(True)  # 设置为互斥
        
        # 添加主题选项
        themes = ["亮色", "暗色", "蓝色", "绿色"]
        for theme in themes:
            theme_action = QAction(theme, self)
            theme_action.setCheckable(True)
            theme_action.setChecked(theme == "亮色")  # 默认选中亮色主题
            theme_action.triggered.connect(lambda checked, t=theme: self._on_theme_changed(t))
            theme_menu.addAction(theme_action)
            theme_action_group.addAction(theme_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 添加状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
    
    def _on_combo_changed(self, index):
        """下拉框变更处理"""
        self.status_label.setText(f"选择了: {self.combo.currentText()}")
    
    def _on_button_clicked(self):
        """按钮点击处理"""
        self.status_label.setText("按钮被点击")
        self.label.setText(f"按钮被点击，当前选择: {self.combo.currentText()}")
    
    def _on_theme_changed(self, theme):
        """主题变更处理"""
        self.status_label.setText(f"主题已更改为: {theme}")

def run_test():
    """运行测试"""
    try:
        app = QApplication(sys.argv)
        window = MainWindow()
        window.show()
        sys.exit(app.exec() if qt_version == 6 else app.exec_())
    except Exception as e:
        print(f"运行测试时出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    run_test()
