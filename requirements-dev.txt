
# 测试工具
pytest>=6.2.5
pytest-cov>=2.12.1
pytest-xdist>=2.4.0
pytest-sugar>=0.9.4
pytest-timeout>=2.0.1
pytest-mock>=3.6.1

# 代码质量工具
black>=21.7b0
flake8>=3.9.0
pylint>=2.9.6
mypy>=0.910
isort>=5.9.0
bandit>=1.7.0
safety>=1.10.3

# 类型检查支持
types-PyYAML>=6.0.0
types-setuptools>=57.4.0
types-requests>=2.25.0
types-urllib3>=1.26.0

# 文档工具
sphinx>=4.2.0
sphinx-rtd-theme>=1.0.0
sphinx-autodoc-typehints>=1.12.0
sphinx-markdown-builder>=0.5.5
pdoc3>=0.9.2

# 开发工具
ipython>=7.27.0
jupyter>=1.0.0
notebook>=6.4.4
pre-commit>=2.15.0
commitizen>=2.20.0
bump2version>=1.0.1

# 性能分析
memory_profiler>=0.58.0
line_profiler>=3.3.0
pyinstrument>=4.0.3

# 调试工具
ipdb>=0.13.9
pudb>=2021.1

# 代码复杂度分析
radon>=5.1.0
xenon>=0.7.3

# 依赖管理
pipdeptree>=2.2.0
pip-tools>=6.2.0

# 打包工具
build>=0.7.0
twine>=3.4.2
wheel>=0.37.0

# Docker开发
docker-compose>=1.29.2

# 文档生成
mkdocs>=1.2.3
mkdocs-material>=7.3.0
mkdocstrings>=0.15.2