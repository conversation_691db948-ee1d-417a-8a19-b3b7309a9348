
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本清理器模块
负责清理和规范化文本内容
"""

import re
import logging
from typing import Dict, Any, List, Optional
from html import unescape
import unicodedata

class TextCleaner:
    """文本清理器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化文本清理器
        
        Args:
            config: 配置字典，包含预处理相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 编译正则表达式
        self.html_tag_pattern = re.compile(r'<[^>]+>')
        self.markdown_link_pattern = re.compile(r'\[([^\]]+)\]\([^)]+\)')
        self.markdown_image_pattern = re.compile(r'!\[([^\]]*)\]\([^)]+\)')
        self.markdown_bold_pattern = re.compile(r'\*\*([^\*]+)\*\*|__([^_]+)__')
        self.markdown_italic_pattern = re.compile(r'\*([^\*]+)\*|_([^_]+)_')
        self.markdown_code_pattern = re.compile(r'`[^`]+`')
        self.markdown_header_pattern = re.compile(r'^#+\s+', re.MULTILINE)
        self.multiple_spaces_pattern = re.compile(r'\s+')
        self.multiple_newlines_pattern = re.compile(r'\n{3,}')
        self.url_pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
        
        # 特殊字符映射
        self.special_chars_map = {
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '…': '...',
            '–': '-',
            '—': '-',
            '•': '*',
        }
    
    def remove_html_tags(self, text: str) -> str:
        """
        移除HTML标签
        
        Args:
            text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        # 首先解码HTML实体
        text = unescape(text)
        # 然后移除HTML标签
        return self.html_tag_pattern.sub('', text)
    
    def clean_markdown(self, text: str) -> str:
        """
        清理Markdown语法标记
        
        Args:
            text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        # 提取链接文本
        text = self.markdown_link_pattern.sub(r'\1', text)
        
        # 移除图片标记
        text = self.markdown_image_pattern.sub('', text)
        
        # 提取加粗文本
        text = self.markdown_bold_pattern.sub(lambda m: m.group(1) or m.group(2), text)
        
        # 提取斜体文本
        text = self.markdown_italic_pattern.sub(lambda m: m.group(1) or m.group(2), text)
        
        # 提取行内代码文本
        text = self.markdown_code_pattern.sub(lambda m: m.group().strip('`'), text)
        
        # 移除标题标记
        text = self.markdown_header_pattern.sub('', text)
        
        return text
    
    def normalize_whitespace(self, text: str) -> str:
        """
        规范化空白字符
        
        Args:
            text: 输入文本
            
        Returns:
            str: 规范化后的文本
        """
        # 将所有空白字符替换为单个空格
        text = self.multiple_spaces_pattern.sub(' ', text)
        
        # 将多个换行符替换为两个换行符
        text = self.multiple_newlines_pattern.sub('\n\n', text)
        
        # 清理行首尾的空白字符
        text = '\n'.join(line.strip() for line in text.splitlines())
        
        return text.strip()
    
    def replace_special_chars(self, text: str) -> str:
        """
        替换特殊字符
        
        Args:
            text: 输入文本
            
        Returns:
            str: 替换后的文本
        """
        # 替换预定义的特殊字符
        for old, new in self.special_chars_map.items():
            text = text.replace(old, new)
        
        # 规范化Unicode字符
        return unicodedata.normalize('NFKC', text)
    
    def remove_urls(self, text: str) -> str:
        """
        移除URL
        
        Args:
            text: 输入文本
            
        Returns:
            str: 清理后的文本
        """
        return self.url_pattern.sub('', text)
    
    def clean_text(self, text: str, **options) -> str:
        """
        清理文本
        
        Args:
            text: 输入文本
            **options: 清理选项，可以覆盖配置文件中的设置
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        try:
            # 获取清理选项
            remove_html = options.get('remove_html_tags', 
                                    self.config['preprocessing'].get('remove_html_tags', True))
            remove_urls = options.get('remove_urls',
                                    self.config['preprocessing'].get('remove_urls', True))
            normalize_chars = options.get('normalize_chars',
                                        self.config['preprocessing'].get('normalize_chars', True))
            
            # 应用清理步骤
            if remove_html:
                text = self.remove_html_tags(text)
            
            # 清理Markdown语法
            text = self.clean_markdown(text)
            
            if remove_urls:
                text = self.remove_urls(text)
            
            if normalize_chars:
                text = self.replace_special_chars(text)
            
            # 规范化空白字符
            text = self.normalize_whitespace(text)
            
            return text
            
        except Exception as e:
            self.logger.error(f"清理文本时出错: {e}")
            return text
    
    def clean_batch(self, texts: List[str], **options) -> List[str]:
        """
        批量清理文本
        
        Args:
            texts: 文本列表
            **options: 清理选项
            
        Returns:
            List[str]: 清理后的文本列表
        """
        return [self.clean_text(text, **options) for text in texts]

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'preprocessing': {
            'remove_html_tags': True,
            'remove_urls': True,
            'normalize_chars': True
        }
    }
    
    cleaner = TextCleaner(test_config)
    
    test_text = """
    # 标题
    
    这是一个**粗体**和*斜体*的测试。
    
    这是一个[链接](http://example.com)和一个![图片](image.jpg)。
    
    <div>HTML标签</div>
    
    代码示例：`print("Hello")`
    
    http://example.com
    
    特殊字符："测试"…
    """
    
    cleaned_text = cleaner.clean_text(test_text)
    print("清理后的文本:")
    print(cleaned_text)