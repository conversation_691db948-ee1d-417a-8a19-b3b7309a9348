/* 暗色主题样式表 */

/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #121212;
}

QLabel {
    color: #FFFFFF;
}

QPushButton {
    background-color: #2196F3;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1E88E5;
}

QPushButton:pressed {
    background-color: #1976D2;
}

QPushButton:disabled {
    background-color: #424242;
    color: #757575;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid #2196F3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QScrollBar:vertical {
    border: none;
    background-color: #1E1E1E;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #424242;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #1E1E1E;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #424242;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: #1E1E1E;
    color: #FFFFFF;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #333333;
}

QMenu {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border: 1px solid #333333;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #333333;
}

QTabWidget::pane {
    border: 1px solid #333333;
    background-color: #1E1E1E;
}

QTabBar::tab {
    background-color: #121212;
    color: #AAAAAA;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #1E1E1E;
    color: #2196F3;
}

QStatusBar {
    background-color: #1E1E1E;
    color: #AAAAAA;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #2196F3;
}

#sidebarWidget {
    background-color: #1E1E1E;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: #121212;
}

#dashboardWidget {
    background-color: #121212;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: #2196F3;
    margin-bottom: 8px;
}

.card-content {
    color: #FFFFFF;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid #2196F3;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid #2196F3;
    box-shadow: 0 0 10px #2196F3;
}
