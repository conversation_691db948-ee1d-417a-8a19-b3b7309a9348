#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ollama集成模块
提供与Ollama API的集成，用于向量化操作
"""

import requests
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

class OllamaEmbedding:
    """Ollama嵌入类，用于调用Ollama API进行向量化"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Ollama嵌入类
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)
        
        # 获取配置
        ollama_config = config.get('local_models', {}).get('ollama', {})
        self.api_url = ollama_config.get('api_url', 'http://localhost:11434/api')
        self.model_name = ollama_config.get('default_model', 'llama2')
        
        # 从模型列表中查找指定模型的配置
        models = ollama_config.get('models', [])
        for model in models:
            if model.get('name') == self.model_name:
                self.parameters = model.get('parameters', {})
                break
        else:
            self.parameters = {}
        
        # 向量维度 - 根据模型不同可能需要调整
        self.vector_dimension = config.get('vectorization', {}).get('vector_dimension', 384)
        
        self.logger.info(f"初始化Ollama嵌入类，使用模型: {self.model_name}，API地址: {self.api_url}")
    
    def encode_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化
        
        Args:
            text: 输入文本
            
        Returns:
            np.ndarray: 文本向量
        """
        if not text:
            return np.zeros(self.vector_dimension)
        
        try:
            # 构建请求
            endpoint = f"{self.api_url}/embeddings"
            payload = {
                "model": self.model_name,
                "prompt": text,
                **self.parameters
            }
            
            # 发送请求
            response = requests.post(endpoint, json=payload)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            if 'embedding' in result:
                embedding = np.array(result['embedding'])
                
                # 如果向量维度与配置不符，进行调整
                if len(embedding) != self.vector_dimension:
                    self.logger.warning(f"Ollama返回的向量维度 ({len(embedding)}) 与配置的维度 ({self.vector_dimension}) 不符")
                    if len(embedding) > self.vector_dimension:
                        # 截断
                        embedding = embedding[:self.vector_dimension]
                    else:
                        # 填充
                        padding = np.zeros(self.vector_dimension - len(embedding))
                        embedding = np.concatenate([embedding, padding])
                
                return embedding
            else:
                self.logger.error(f"Ollama API返回的结果中没有embedding字段: {result}")
                return np.zeros(self.vector_dimension)
                
        except Exception as e:
            self.logger.error(f"调用Ollama API进行向量化时出错: {e}")
            return np.zeros(self.vector_dimension)
    
    def encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        批量对文本进行向量化
        
        Args:
            texts: 文本列表
            
        Returns:
            np.ndarray: 文本向量矩阵
        """
        if not texts:
            return np.array([])
        
        # 逐个处理文本
        embeddings = []
        for text in texts:
            embedding = self.encode_text(text)
            embeddings.append(embedding)
        
        return np.array(embeddings)
