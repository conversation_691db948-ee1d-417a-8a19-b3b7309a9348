#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试枚举类的脚本
"""

import sys
import os
import logging
from enum import Enum
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 定义测试枚举类
class TestTheme(Enum):
    """测试主题枚举"""
    SYSTEM = "system"
    LIGHT = "light"
    DARK = "dark"
    
    @property
    def display_name(self) -> str:
        """获取主题的显示名称"""
        names = {
            TestTheme.SYSTEM: "System Theme",
            TestTheme.LIGHT: "Light",
            TestTheme.DARK: "Dark",
        }
        return names.get(self, "Unknown")

class TestLanguage(Enum):
    """测试语言枚举"""
    CHINESE = "zh_CN"
    ENGLISH = "en_US"
    
    @classmethod
    def from_code(cls, code: str) -> 'TestLanguage':
        """从语言代码获取语言枚举"""
        for lang in cls:
            if lang.value == code:
                return lang
        return cls.ENGLISH  # 默认返回英语
    
    @property
    def display_name(self) -> str:
        """获取语言的显示名称"""
        names = {
            TestLanguage.CHINESE: "简体中文",
            TestLanguage.ENGLISH: "English",
        }
        return names.get(self, "Unknown")

def test_enum_constructor():
    """测试枚举构造函数"""
    try:
        print("测试 TestTheme 枚举构造函数...")
        # 正常方式使用枚举
        theme = TestTheme.LIGHT
        print(f"主题: {theme}, 显示名称: {theme.display_name}")
        
        # 使用构造函数
        try:
            theme_value = "light"
            theme = TestTheme(theme_value)
            print(f"通过构造函数创建主题: {theme}, 显示名称: {theme.display_name}")
        except Exception as e:
            print(f"通过构造函数创建主题时出错: {e}")
            traceback.print_exc()
        
        # 使用安全的方式获取枚举值
        try:
            theme_value = "light"
            theme = None
            for t in TestTheme:
                if t.value == theme_value:
                    theme = t
                    break
            if theme:
                print(f"通过安全方式获取主题: {theme}, 显示名称: {theme.display_name}")
            else:
                print(f"未找到值为 {theme_value} 的主题")
        except Exception as e:
            print(f"通过安全方式获取主题时出错: {e}")
            traceback.print_exc()
        
        print("\n测试 TestLanguage 枚举构造函数...")
        # 正常方式使用枚举
        lang = TestLanguage.CHINESE
        print(f"语言: {lang}, 显示名称: {lang.display_name}")
        
        # 使用构造函数
        try:
            lang_value = "zh_CN"
            lang = TestLanguage(lang_value)
            print(f"通过构造函数创建语言: {lang}, 显示名称: {lang.display_name}")
        except Exception as e:
            print(f"通过构造函数创建语言时出错: {e}")
            traceback.print_exc()
        
        # 使用类方法
        try:
            lang_code = "zh_CN"
            lang = TestLanguage.from_code(lang_code)
            print(f"通过类方法获取语言: {lang}, 显示名称: {lang.display_name}")
        except Exception as e:
            print(f"通过类方法获取语言时出错: {e}")
            traceback.print_exc()
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_enum_constructor()
