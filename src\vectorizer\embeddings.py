
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本嵌入模块
负责将文本转换为向量表示
支持多种模型类型和领域特化模型
"""

import logging
import os
from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import numpy as np
from pathlib import Path
import torch
from sentence_transformers import SentenceTransformer, models
from transformers import AutoTokenizer, AutoModel, AutoConfig, PreTrainedModel, PreTrainedTokenizer
import faiss
import pickle
from tqdm import tqdm
import json
from dataclasses import dataclass
from enum import Enum
import shutil
import tempfile

class ModelType(Enum):
    """模型类型枚举"""
    SENTENCE_TRANSFORMER = "sentence-transformer"
    TRANSFORMERS = "transformers"
    BERT_MRC = "bert-mrc"
    ROBERTA = "roberta"
    XLNET = "xlnet"
    ERNIE = "ernie"
    DOMAIN_SPECIFIC = "domain-specific"
    CUSTOM = "custom"

@dataclass
class DomainModelConfig:
    """领域模型配置"""
    name: str
    base_model: str
    domain: str
    description: str
    vector_dimension: int
    model_path: Optional[str] = None
    tokenizer_path: Optional[str] = None
    config_path: Optional[str] = None

class ModelRegistry:
    """模型注册表类"""

    # 预定义的领域模型
    DOMAIN_MODELS = {
        # 医疗领域模型
        "medical": DomainModelConfig(
            name="medical-bert",
            base_model="bert-base-chinese",
            domain="medical",
            description="针对医疗领域优化的BERT模型",
            vector_dimension=768,
        ),
        # 法律领域模型
        "legal": DomainModelConfig(
            name="legal-roberta",
            base_model="roberta-base",
            domain="legal",
            description="针对法律文档优化的RoBERTa模型",
            vector_dimension=768,
        ),
        # 技术文档模型
        "technical": DomainModelConfig(
            name="technical-xlnet",
            base_model="xlnet-base-cased",
            domain="technical",
            description="针对技术文档优化的XLNet模型",
            vector_dimension=768,
        ),
        # 代码模型
        "code": DomainModelConfig(
            name="code-bert",
            base_model="microsoft/codebert-base",
            domain="code",
            description="针对代码文本优化的CodeBERT模型",
            vector_dimension=768,
        ),
    }

    # 通用多语言模型
    GENERAL_MODELS = {
        "multilingual-minilm": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "multilingual-mpnet": "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
        "bert-base-multilingual": "bert-base-multilingual-cased",
        "xlm-roberta": "xlm-roberta-base",
    }

    # 高级模型
    ADVANCED_MODELS = {
        "bert-mrc": "deepset/bert-base-cased-squad2",
        "roberta-large": "roberta-large",
        "xlnet-large": "xlnet-large-cased",
        "ernie": "ernie-3.0-base-zh",
    }

    @classmethod
    def get_model_config(cls, model_name: str) -> Optional[Union[str, DomainModelConfig]]:
        """获取模型配置"""
        # 检查是否是领域模型
        if model_name in cls.DOMAIN_MODELS:
            return cls.DOMAIN_MODELS[model_name]

        # 检查是否是通用模型
        if model_name in cls.GENERAL_MODELS:
            return cls.GENERAL_MODELS[model_name]

        # 检查是否是高级模型
        if model_name in cls.ADVANCED_MODELS:
            return cls.ADVANCED_MODELS[model_name]

        # 直接返回模型名称（可能是自定义模型或HuggingFace模型ID）
        return model_name

    @classmethod
    def get_model_type(cls, model_name: str) -> ModelType:
        """获取模型类型"""
        if model_name in cls.DOMAIN_MODELS:
            return ModelType.DOMAIN_SPECIFIC

        if "sentence-transformers" in model_name or model_name in cls.GENERAL_MODELS and "sentence-transformers" in cls.GENERAL_MODELS[model_name]:
            return ModelType.SENTENCE_TRANSFORMER

        if "bert-mrc" in model_name or model_name == "bert-mrc":
            return ModelType.BERT_MRC

        if "roberta" in model_name:
            return ModelType.ROBERTA

        if "xlnet" in model_name:
            return ModelType.XLNET

        if "ernie" in model_name:
            return ModelType.ERNIE

        # 默认为transformers类型
        return ModelType.TRANSFORMERS

class TextEmbedding:
    """文本嵌入类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化文本嵌入器

        Args:
            config: 配置字典，包含向量化相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.vectorization_config = config.get('vectorization', {})
        self.model_name = self.vectorization_config.get('model_name',
            'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
        self.domain = self.vectorization_config.get('domain', None)
        self.vector_dimension = self.vectorization_config.get('vector_dimension', 384)
        self.batch_size = self.vectorization_config.get('batch_size', 32)
        # 强制使用CPU，避免CUDA错误
        self.device = self.vectorization_config.get('device', 'cpu')
        self.cache_dir = Path(self.vectorization_config.get('cache_dir', 'cache'))
        self.normalize_vectors = self.vectorization_config.get('normalize_vectors', True)
        self.model_cache_dir = Path(self.vectorization_config.get('model_cache_dir', 'models'))
        self.custom_model_path = self.vectorization_config.get('custom_model_path', None)

        # 检查是否使用本地模型
        self.use_local_model = False
        self.local_model = None
        if self.model_name.startswith('local:'):
            self.use_local_model = True
            local_model_name = self.model_name[6:]  # 移除 'local:' 前缀

            # 获取本地模型管理器
            try:
                from ..utils.local_model_manager import get_local_model_manager
                model_manager = get_local_model_manager()
                self.local_model = model_manager.get_model(local_model_name)

                if self.local_model:
                    self.logger.info(f"使用本地模型: {self.local_model.name} ({self.local_model.type})")
                    # 更新向量维度
                    self.vector_dimension = self.local_model.vector_dimension
                else:
                    self.logger.error(f"未找到本地模型: {local_model_name}")
                    raise ValueError(f"未找到本地模型: {local_model_name}")
            except ImportError as e:
                self.logger.error(f"无法导入本地模型管理器: {e}")
                raise

        # 检查是否使用Ollama
        local_models_config = config.get('local_models', {})
        ollama_config = local_models_config.get('ollama', {})
        self.use_ollama = ollama_config.get('enabled', False)

        if self.use_ollama:
            # 导入Ollama集成模块
            from .ollama import OllamaEmbedding
            self.ollama_embedder = OllamaEmbedding(config)
            self.logger.info("使用Ollama进行向量化")

        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.model_cache_dir.mkdir(parents=True, exist_ok=True)

        # 解析模型配置
        self._parse_model_config()

        # 初始化模型
        self._initialize_model()

        # 初始化缓存
        self._initialize_cache()

    def _parse_model_config(self):
        """解析模型配置"""
        # 如果指定了领域，优先使用领域模型
        if self.domain and self.domain in ModelRegistry.DOMAIN_MODELS:
            self.model_config = ModelRegistry.DOMAIN_MODELS[self.domain]
            self.actual_model_name = self.model_config.base_model
            self.model_type = ModelType.DOMAIN_SPECIFIC
            self.vector_dimension = self.model_config.vector_dimension
            return

        # 否则使用指定的模型名称
        model_config = ModelRegistry.get_model_config(self.model_name)
        if isinstance(model_config, DomainModelConfig):
            self.model_config = model_config
            self.actual_model_name = model_config.base_model
            self.model_type = ModelType.DOMAIN_SPECIFIC
            self.vector_dimension = model_config.vector_dimension
        else:
            self.model_config = None
            self.actual_model_name = model_config if isinstance(model_config, str) else self.model_name
            self.model_type = ModelRegistry.get_model_type(self.actual_model_name)

    def _initialize_model(self):
        """初始化嵌入模型"""
        try:
            # 检查是否使用本地模型
            if self.use_local_model and self.local_model:
                self._load_local_model()
                return

            # 检查是否使用自定义模型
            if self.custom_model_path:
                self._load_custom_model()
                return

            # 根据模型类型加载不同的模型
            if self.model_type == ModelType.SENTENCE_TRANSFORMER:
                # 使用Sentence-BERT模型
                self.model = SentenceTransformer(self.actual_model_name, device=self.device, cache_folder=str(self.model_cache_dir))
                self.tokenizer = None  # Sentence-Transformer内部已包含tokenizer

            elif self.model_type == ModelType.DOMAIN_SPECIFIC:
                # 加载领域特化模型
                self._load_domain_model()

            elif self.model_type == ModelType.BERT_MRC:
                # 加载BERT-MRC模型
                self.tokenizer = AutoTokenizer.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir))
                self.model = AutoModel.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir)).to(self.device)
                # 设置特殊的池化方法
                self.pooling_method = "cls_pooling"

            elif self.model_type in [ModelType.ROBERTA, ModelType.XLNET, ModelType.ERNIE]:
                # 加载其他特定模型
                self.tokenizer = AutoTokenizer.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir))
                self.model = AutoModel.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir)).to(self.device)
                # 根据模型类型设置特定的池化方法
                if self.model_type == ModelType.XLNET:
                    self.pooling_method = "last_pooling"
                else:
                    self.pooling_method = "mean_pooling"

            else:  # ModelType.TRANSFORMERS
                # 使用HuggingFace Transformers模型
                self.tokenizer = AutoTokenizer.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir))
                self.model = AutoModel.from_pretrained(self.actual_model_name, cache_dir=str(self.model_cache_dir)).to(self.device)
                self.pooling_method = "mean_pooling"

            self.logger.info(f"成功加载模型: {self.actual_model_name} (类型: {self.model_type.value})")

        except Exception as e:
            self.logger.error(f"初始化模型时出错: {e}")
            raise

    def _load_domain_model(self):
        """加载领域特化模型"""
        try:
            if not self.model_config:
                raise ValueError("未找到领域模型配置")

            # 检查是否有预训练的领域模型
            domain_model_path = self.model_cache_dir / f"domain_{self.model_config.domain}"
            if domain_model_path.exists():
                # 加载已有的领域模型
                self.logger.info(f"加载本地领域模型: {domain_model_path}")

                if (domain_model_path / "sentence_transformer").exists():
                    # 作为Sentence-Transformer加载
                    self.model = SentenceTransformer(str(domain_model_path / "sentence_transformer"), device=self.device)
                    self.tokenizer = None
                else:
                    # 作为普通Transformer加载
                    self.tokenizer = AutoTokenizer.from_pretrained(str(domain_model_path))
                    self.model = AutoModel.from_pretrained(str(domain_model_path)).to(self.device)
                    self.pooling_method = "mean_pooling"
            else:
                # 加载基础模型
                self.logger.info(f"加载基础模型作为领域模型: {self.model_config.base_model}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_config.base_model, cache_dir=str(self.model_cache_dir))
                self.model = AutoModel.from_pretrained(self.model_config.base_model, cache_dir=str(self.model_cache_dir)).to(self.device)
                self.pooling_method = "mean_pooling"

        except Exception as e:
            self.logger.error(f"加载领域模型时出错: {e}")
            raise

    def _load_custom_model(self):
        """加载自定义模型"""
        try:
            custom_path = Path(self.custom_model_path)
            if not custom_path.exists():
                raise ValueError(f"自定义模型路径不存在: {custom_path}")

            # 检查模型类型
            model_type_file = custom_path / "model_type.txt"
            if model_type_file.exists():
                with open(model_type_file, "r", encoding="utf-8") as f:
                    model_type_str = f.read().strip()
                    self.model_type = ModelType(model_type_str)

            # 加载模型
            if (custom_path / "sentence_transformer").exists():
                # 作为Sentence-Transformer加载
                self.model = SentenceTransformer(str(custom_path / "sentence_transformer"), device=self.device)
                self.tokenizer = None
            else:
                # 作为普通Transformer加载
                self.tokenizer = AutoTokenizer.from_pretrained(str(custom_path))
                self.model = AutoModel.from_pretrained(str(custom_path)).to(self.device)
                self.pooling_method = "mean_pooling"

            # 加载向量维度信息
            config_file = custom_path / "config.json"
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    model_config = json.load(f)
                    if "hidden_size" in model_config:
                        self.vector_dimension = model_config["hidden_size"]

            self.logger.info(f"成功加载自定义模型: {custom_path}")

        except Exception as e:
            self.logger.error(f"加载自定义模型时出错: {e}")
            raise

    def _load_local_model(self):
        """加载本地模型"""
        try:
            if self.local_model.type == "ollama":
                # 使用Ollama模型
                from .ollama import OllamaEmbedding

                # 构建Ollama配置
                ollama_config = {
                    'local_models': {
                        'ollama': {
                            'enabled': True,
                            'api_url': self.local_model.endpoint,
                            'default_model': self.local_model.model_id,
                            'models': [self.local_model.parameters]
                        }
                    }
                }

                self.ollama_embedder = OllamaEmbedding(ollama_config)
                self.model = None  # Ollama不需要本地模型对象
                self.tokenizer = None
                self.logger.info(f"成功加载Ollama模型: {self.local_model.model_id}")

            elif self.local_model.type == "huggingface":
                # 使用Hugging Face本地模型
                model_path = self.local_model.parameters.get('model_path', self.local_model.model_id)

                # 检查是否是Sentence Transformer
                if self.local_model.parameters.get('is_sentence_transformer', False):
                    self.model = SentenceTransformer(model_path, device=self.device)
                    self.tokenizer = None
                    self.model_type = ModelType.SENTENCE_TRANSFORMER
                else:
                    self.tokenizer = AutoTokenizer.from_pretrained(model_path)
                    self.model = AutoModel.from_pretrained(model_path).to(self.device)
                    self.pooling_method = "mean_pooling"
                    self.model_type = ModelType.TRANSFORMERS

                self.logger.info(f"成功加载Hugging Face本地模型: {model_path}")

            elif self.local_model.type == "openai_compatible":
                # 使用OpenAI兼容的API
                self.model = None
                self.tokenizer = None
                # 这里可以实现OpenAI兼容API的调用逻辑
                self.logger.info(f"成功配置OpenAI兼容模型: {self.local_model.model_id}")

            else:
                raise ValueError(f"不支持的本地模型类型: {self.local_model.type}")

        except Exception as e:
            self.logger.error(f"加载本地模型时出错: {e}")
            raise

    def _initialize_cache(self):
        """初始化向量缓存"""
        self.cache_file = self.cache_dir / 'vector_cache.pkl'
        self.vector_cache = {}

        # 加载现有缓存
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'rb') as f:
                    self.vector_cache = pickle.load(f)
                self.logger.info(f"加载了 {len(self.vector_cache)} 个缓存向量")
            except Exception as e:
                self.logger.warning(f"加载缓存文件时出错: {e}")

    def _save_cache(self):
        """保存向量缓存"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.vector_cache, f)
            self.logger.info(f"保存了 {len(self.vector_cache)} 个向量到缓存")
        except Exception as e:
            self.logger.error(f"保存缓存时出错: {e}")

    def _mean_pooling(self, model_output: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        对模型输出进行平均池化

        Args:
            model_output: 模型输出的token embeddings
            attention_mask: 注意力掩码

        Returns:
            torch.Tensor: 池化后的句子向量
        """
        token_embeddings = model_output[0]
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)

    def _cls_pooling(self, model_output: torch.Tensor) -> torch.Tensor:
        """
        使用CLS token进行池化

        Args:
            model_output: 模型输出的token embeddings

        Returns:
            torch.Tensor: 池化后的句子向量
        """
        return model_output[0][:, 0]

    def _last_pooling(self, model_output: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        使用最后一个token进行池化（适用于XLNet等模型）

        Args:
            model_output: 模型输出的token embeddings
            attention_mask: 注意力掩码

        Returns:
            torch.Tensor: 池化后的句子向量
        """
        token_embeddings = model_output[0]

        # 计算每个序列的长度
        seq_lengths = torch.sum(attention_mask, 1).long()

        # 获取每个序列的最后一个token的embedding
        batch_size = token_embeddings.shape[0]
        last_embeddings = torch.zeros(batch_size, token_embeddings.shape[-1], device=token_embeddings.device)
        for i in range(batch_size):
            last_embeddings[i] = token_embeddings[i, seq_lengths[i] - 1]

        return last_embeddings

    def _max_pooling(self, model_output: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        使用最大池化

        Args:
            model_output: 模型输出的token embeddings
            attention_mask: 注意力掩码

        Returns:
            torch.Tensor: 池化后的句子向量
        """
        token_embeddings = model_output[0]
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        token_embeddings = token_embeddings * input_mask_expanded

        # 将padding部分设为很小的值，确保不会被max选中
        token_embeddings[input_mask_expanded == 0] = -1e9

        # 在token维度上取最大值
        max_embeddings = torch.max(token_embeddings, dim=1)[0]
        return max_embeddings

    def _apply_pooling(self, model_output: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        根据配置的池化方法应用池化

        Args:
            model_output: 模型输出
            attention_mask: 注意力掩码

        Returns:
            torch.Tensor: 池化后的句子向量
        """
        if self.pooling_method == "mean_pooling":
            return self._mean_pooling(model_output, attention_mask)
        elif self.pooling_method == "cls_pooling":
            return self._cls_pooling(model_output)
        elif self.pooling_method == "last_pooling":
            return self._last_pooling(model_output, attention_mask)
        elif self.pooling_method == "max_pooling":
            return self._max_pooling(model_output, attention_mask)
        else:
            # 默认使用平均池化
            return self._mean_pooling(model_output, attention_mask)

    def _encode_with_transformers(self, texts: List[str]) -> np.ndarray:
        """
        使用Transformers模型进行编码

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量
        """
        # 分批处理
        all_embeddings = []
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]

            # 编码
            encoded_input = self.tokenizer(
                batch_texts,
                padding=True,
                truncation=True,
                max_length=512,
                return_tensors='pt'
            ).to(self.device)

            # 获取embeddings
            with torch.no_grad():
                model_output = self.model(**encoded_input)

                # 应用池化
                sentence_embeddings = self._apply_pooling(
                    model_output,
                    encoded_input['attention_mask']
                )

                if self.normalize_vectors:
                    sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)

                all_embeddings.append(sentence_embeddings.cpu().numpy())

        return np.vstack(all_embeddings) if all_embeddings else np.array([])

    def _encode_with_openai_compatible(self, texts: List[str]) -> np.ndarray:
        """
        使用OpenAI兼容API进行编码

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量
        """
        try:
            import requests

            # 构建请求
            url = f"{self.local_model.endpoint}/v1/embeddings"
            headers = {
                "Content-Type": "application/json"
            }

            # 添加API密钥（如果有）
            api_key = self.local_model.parameters.get('api_key')
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            data = {
                "model": self.local_model.model_id,
                "input": texts
            }

            response = requests.post(url, json=data, headers=headers, timeout=30)
            response.raise_for_status()

            result = response.json()
            embeddings = []

            for item in result.get('data', []):
                embedding = item.get('embedding', [])
                embeddings.append(np.array(embedding))

            return np.vstack(embeddings) if embeddings else np.zeros((len(texts), self.vector_dimension))

        except Exception as e:
            self.logger.error(f"使用OpenAI兼容API编码时出错: {e}")
            return np.zeros((len(texts), self.vector_dimension))

    def _encode_with_sentence_transformer(self, texts: List[str]) -> np.ndarray:
        """
        使用Sentence-Transformer模型进行编码

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量
        """
        embeddings = self.model.encode(
            texts,
            batch_size=self.batch_size,
            show_progress_bar=len(texts) > 10,  # 只在文本数量较多时显示进度条
            normalize_embeddings=self.normalize_vectors
        )
        return embeddings

    def encode_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化

        Args:
            text: 输入文本

        Returns:
            np.ndarray: 文本向量
        """
        if not text:
            return np.zeros(self.vector_dimension)

        # 检查缓存
        if text in self.vector_cache:
            return self.vector_cache[text]

        try:
            # 检查是否使用本地模型
            if self.use_local_model and self.local_model:
                if self.local_model.type == "ollama":
                    vector = self.ollama_embedder.encode_text(text)
                elif self.local_model.type == "huggingface":
                    if self.model_type == ModelType.SENTENCE_TRANSFORMER:
                        vector = self._encode_with_sentence_transformer([text])[0]
                    else:
                        vector = self._encode_with_transformers([text])[0]
                elif self.local_model.type == "openai_compatible":
                    vector = self._encode_with_openai_compatible([text])[0]
                else:
                    raise ValueError(f"不支持的本地模型类型: {self.local_model.type}")
            # 如果使用Ollama，调用Ollama API
            elif self.use_ollama:
                vector = self.ollama_embedder.encode_text(text)
            elif self.model_type == ModelType.SENTENCE_TRANSFORMER:
                vector = self._encode_with_sentence_transformer([text])[0]
            else:
                vector = self._encode_with_transformers([text])[0]

            # 更新缓存
            self.vector_cache[text] = vector

            return vector

        except Exception as e:
            self.logger.error(f"编码文本时出错: {e}")
            return np.zeros(self.vector_dimension)

    def encode_batch(self, texts: List[str], use_cache: bool = True) -> np.ndarray:
        """
        批量对文本进行向量化

        Args:
            texts: 文本列表
            use_cache: 是否使用缓存

        Returns:
            np.ndarray: 文本向量矩阵
        """
        if not texts:
            return np.array([])

        try:
            if use_cache:
                # 检查哪些文本需要编码
                uncached_texts = []
                uncached_indices = []
                vectors = np.zeros((len(texts), self.vector_dimension))

                for i, text in enumerate(texts):
                    if text in self.vector_cache:
                        vectors[i] = self.vector_cache[text]
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i)

                # 编码未缓存的文本
                if uncached_texts:
                    if self.use_local_model and self.local_model:
                        if self.local_model.type == "ollama":
                            new_vectors = self.ollama_embedder.encode_batch(uncached_texts)
                        elif self.local_model.type == "huggingface":
                            if self.model_type == ModelType.SENTENCE_TRANSFORMER:
                                new_vectors = self._encode_with_sentence_transformer(uncached_texts)
                            else:
                                new_vectors = self._encode_with_transformers(uncached_texts)
                        elif self.local_model.type == "openai_compatible":
                            new_vectors = self._encode_with_openai_compatible(uncached_texts)
                        else:
                            raise ValueError(f"不支持的本地模型类型: {self.local_model.type}")
                    elif self.use_ollama:
                        new_vectors = self.ollama_embedder.encode_batch(uncached_texts)
                    elif self.model_type == ModelType.SENTENCE_TRANSFORMER:
                        new_vectors = self._encode_with_sentence_transformer(uncached_texts)
                    else:
                        new_vectors = self._encode_with_transformers(uncached_texts)

                    # 更新向量和缓存
                    for i, text in enumerate(uncached_texts):
                        vectors[uncached_indices[i]] = new_vectors[i]
                        self.vector_cache[text] = new_vectors[i]

                return vectors

            else:
                # 直接编码所有文本
                if self.use_local_model and self.local_model:
                    if self.local_model.type == "ollama":
                        return self.ollama_embedder.encode_batch(texts)
                    elif self.local_model.type == "huggingface":
                        if self.model_type == ModelType.SENTENCE_TRANSFORMER:
                            return self._encode_with_sentence_transformer(texts)
                        else:
                            return self._encode_with_transformers(texts)
                    elif self.local_model.type == "openai_compatible":
                        return self._encode_with_openai_compatible(texts)
                    else:
                        raise ValueError(f"不支持的本地模型类型: {self.local_model.type}")
                elif self.use_ollama:
                    return self.ollama_embedder.encode_batch(texts)
                elif self.model_type == ModelType.SENTENCE_TRANSFORMER:
                    return self._encode_with_sentence_transformer(texts)
                else:
                    return self._encode_with_transformers(texts)

        except Exception as e:
            self.logger.error(f"批量编码文本时出错: {e}")
            return np.zeros((len(texts), self.vector_dimension))

    def build_index(self, vectors: np.ndarray) -> faiss.Index:
        """
        构建向量索引

        Args:
            vectors: 向量矩阵

        Returns:
            faiss.Index: Faiss索引对象
        """
        try:
            if self.normalize_vectors:
                index = faiss.IndexFlatIP(self.vector_dimension)  # 内积等价于余弦相似度
            else:
                index = faiss.IndexFlatL2(self.vector_dimension)  # 欧氏距离

            if vectors.size > 0:
                if self.normalize_vectors:
                    faiss.normalize_L2(vectors)  # 标准化向量
                index.add(vectors)

            return index

        except Exception as e:
            self.logger.error(f"构建索引时出错: {e}")
            return None

    def find_similar(self, query_vector: np.ndarray, index: faiss.Index, k: int = 5) -> tuple:
        """
        查找相似向量

        Args:
            query_vector: 查询向量
            index: Faiss索引对象
            k: 返回的最相似向量数量

        Returns:
            tuple: (距离数组, 索引数组)
        """
        try:
            if self.normalize_vectors:
                faiss.normalize_L2(query_vector.reshape(1, -1))
            return index.search(query_vector.reshape(1, -1), k)
        except Exception as e:
            self.logger.error(f"搜索相似向量时出错: {e}")
            return None

    def fine_tune(self, texts: List[str], labels: Optional[List[Any]] = None,
               save_path: Optional[str] = None, epochs: int = 3,
               learning_rate: float = 2e-5, batch_size: int = 16) -> bool:
        """
        使用提供的数据微调模型

        Args:
            texts: 训练文本列表
            labels: 标签列表（可选，用于有监督微调）
            save_path: 保存微调后模型的路径
            epochs: 训练轮数
            learning_rate: 学习率
            batch_size: 批处理大小

        Returns:
            bool: 微调是否成功
        """
        try:
            # 只有Sentence-Transformer支持简单的微调
            if self.model_type != ModelType.SENTENCE_TRANSFORMER:
                self.logger.warning(f"当前模型类型 {self.model_type.value} 不支持简单微调，请使用专业的训练脚本")
                return False

            if len(texts) < 100:
                self.logger.warning(f"训练数据太少 ({len(texts)} 条)，建议至少提供100条以上的数据")

            # 创建临时目录保存微调后的模型
            temp_model_dir = Path(tempfile.mkdtemp())
            self.logger.info(f"将在临时目录保存微调模型: {temp_model_dir}")

            # 准备训练数据
            from torch.utils.data import DataLoader, Dataset
            from sentence_transformers import InputExample, losses

            class TextDataset(Dataset):
                def __init__(self, texts, labels=None):
                    self.examples = []
                    if labels is not None:
                        # 有监督学习
                        for i, (text, label) in enumerate(zip(texts, labels)):
                            self.examples.append(InputExample(guid=str(i), texts=[text], label=label))
                    else:
                        # 无监督学习（使用文本本身作为标签）
                        for i, text in enumerate(texts):
                            self.examples.append(InputExample(guid=str(i), texts=[text]))

                def __len__(self):
                    return len(self.examples)

                def __getitem__(self, idx):
                    return self.examples[idx]

            # 创建数据集和数据加载器
            train_dataset = TextDataset(texts, labels)
            train_dataloader = DataLoader(train_dataset, shuffle=True, batch_size=batch_size)

            # 选择损失函数
            if labels is not None:
                # 有监督学习
                if isinstance(labels[0], (int, float)):
                    # 回归任务
                    train_loss = losses.CosineSimilarityLoss(self.model)
                else:
                    # 分类任务
                    train_loss = losses.SoftmaxLoss(self.model, len(set(labels)))
            else:
                # 无监督学习
                train_loss = losses.MultipleNegativesRankingLoss(self.model)

            # 训练模型
            self.model.fit(
                train_objectives=[(train_dataloader, train_loss)],
                epochs=epochs,
                warmup_steps=100,
                optimizer_params={'lr': learning_rate},
                output_path=str(temp_model_dir)
            )

            # 保存微调后的模型
            if save_path:
                save_dir = Path(save_path)
                save_dir.mkdir(parents=True, exist_ok=True)
                self.model.save(str(save_dir))
                self.logger.info(f"微调后的模型已保存到: {save_dir}")

            # 更新当前模型为微调后的模型
            self.model = SentenceTransformer(str(temp_model_dir))
            self.logger.info("已加载微调后的模型")

            # 清理临时目录
            import shutil
            shutil.rmtree(temp_model_dir)

            return True

        except Exception as e:
            self.logger.error(f"微调模型时出错: {e}")
            return False

    def export_model(self, export_path: str) -> bool:
        """
        导出当前模型

        Args:
            export_path: 导出路径

        Returns:
            bool: 是否成功导出
        """
        try:
            export_dir = Path(export_path)
            export_dir.mkdir(parents=True, exist_ok=True)

            if self.model_type == ModelType.SENTENCE_TRANSFORMER:
                # 导出Sentence-Transformer模型
                self.model.save(str(export_dir / "sentence_transformer"))

                # 保存模型类型信息
                with open(export_dir / "model_type.txt", "w", encoding="utf-8") as f:
                    f.write(self.model_type.value)

                self.logger.info(f"模型已导出到: {export_dir}")
                return True

            else:
                # 导出Transformers模型
                self.model.save_pretrained(str(export_dir))
                self.tokenizer.save_pretrained(str(export_dir))

                # 保存模型类型信息
                with open(export_dir / "model_type.txt", "w", encoding="utf-8") as f:
                    f.write(self.model_type.value)

                self.logger.info(f"模型已导出到: {export_dir}")
                return True

        except Exception as e:
            self.logger.error(f"导出模型时出错: {e}")
            return False

    def cleanup(self):
        """清理资源并保存缓存"""
        try:
            self._save_cache()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'vectorization': {
            'model_name': 'multilingual-minilm',  # 使用预定义的模型名称
            'vector_dimension': 384,
            'batch_size': 32,
            'device': 'cpu',  # 强制使用CPU
            'cache_dir': 'cache',
            'normalize_vectors': True
        }
    }

    embedder = TextEmbedding(test_config)

    test_texts = [
        "这是一个中文测试文本",
        "This is an English test text",
        "混合Chinese和English的Text",
        "这是一段医疗相关的文本，描述了患者的症状和治疗方案",
        "这是一段法律文档，包含了合同条款和法律术语"
    ]

    # 测试单个文本编码
    vector = embedder.encode_text(test_texts[0])
    print(f"单个文本向量维度: {vector.shape}")

    # 测试批量编码
    vectors = embedder.encode_batch(test_texts)
    print(f"批量文本向量维度: {vectors.shape}")

    # 测试索引构建和相似度搜索
    index = embedder.build_index(vectors)
    if index:
        distances, indices = embedder.find_similar(vector, index, k=2)
        print(f"最相似的文本索引: {indices}")
        print(f"相似度距离: {distances}")

    # 测试模型微调（实际使用时需要更多数据）
    # embedder.fine_tune(test_texts, save_path="models/fine_tuned", epochs=1)

    # 测试模型导出
    # embedder.export_model("models/exported_model")

    # 清理资源
    embedder.cleanup()
