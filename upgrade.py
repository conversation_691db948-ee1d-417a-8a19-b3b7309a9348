
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目升级脚本
用于安全地升级项目到新版本
"""

import os
import sys
import shutil
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime
import yaml
import semver

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('upgrade.log')
        ]
    )
    return logging.getLogger(__name__)

def get_current_version() -> str:
    """获取当前版本"""
    try:
        with open('src/version.py', 'r') as f:
            exec(f.read())
            return locals().get('__version__', '0.0.0')
    except:
        return '0.0.0'

def check_version_compatibility(current: str, target: str) -> bool:
    """检查版本兼容性"""
    current_ver = semver.VersionInfo.parse(current)
    target_ver = semver.VersionInfo.parse(target)
    
    # 主版本号不同表示不兼容
    if target_ver.major > current_ver.major:
        return False
    
    return True

def backup_data(backup_dir: Path):
    """备份数据"""
    # 创建备份目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = backup_dir / f'backup_{timestamp}'
    backup_path.mkdir(parents=True, exist_ok=True)
    
    # 备份数据目录
    if Path('data').exists():
        shutil.copytree('data', backup_path / 'data')
    
    # 备份配置文件
    if Path('config').exists():
        shutil.copytree('config', backup_path / 'config')
    
    # 备份数据库
    if Path('data/metadata.db').exists():
        shutil.copy2('data/metadata.db', backup_path / 'metadata.db')
    
    return backup_path

def update_dependencies():
    """更新依赖"""
    pip_cmd = os.path.join('venv', 'Scripts' if sys.platform == 'win32' else 'bin', 'pip')
    
    # 更新pip
    subprocess.run([pip_cmd, 'install', '--upgrade', 'pip'], check=True)
    
    # 更新依赖
    subprocess.run([pip_cmd, 'install', '--upgrade', '-r', 'requirements.txt'], check=True)
    subprocess.run([pip_cmd, 'install', '--upgrade', '-r', 'requirements-dev.txt'], check=True)

def update_configuration(backup_path: Path):
    """更新配置文件"""
    # 加载当前配置
    current_config = {}
    if Path('config/config.yaml').exists():
        with open('config/config.yaml', 'r') as f:
            current_config = yaml.safe_load(f)
    
    # 加载新配置模板
    with open('config/config.yaml.example', 'r') as f:
        new_config = yaml.safe_load(f)
    
    # 合并配置
    merged_config = merge_configs(new_config, current_config)
    
    # 保存合并后的配置
    with open('config/config.yaml', 'w') as f:
        yaml.safe_dump(merged_config, f, default_flow_style=False)

def merge_configs(new: Dict, current: Dict) -> Dict:
    """递归合并配置"""
    merged = new.copy()
    
    for key, value in current.items():
        if key in merged:
            if isinstance(value, dict) and isinstance(merged[key], dict):
                merged[key] = merge_configs(merged[key], value)
            else:
                merged[key] = value
        else:
            merged[key] = value
    
    return merged

def run_migrations():
    """运行数据迁移"""
    subprocess.run([sys.executable, 'migrate.py'], check=True)

def run_tests():
    """运行测试"""
    subprocess.run([sys.executable, '-m', 'pytest', 'tests'], check=True)

def update_documentation():
    """更新文档"""
    subprocess.run([
        'sphinx-build',
        '-b', 'html',
        'docs/source',
        'docs/build/html'
    ], check=True)

def save_upgrade_info(current_version: str, target_version: str,
                     backup_path: Path):
    """保存升级信息"""
    info = {
        'timestamp': datetime.now().isoformat(),
        'from_version': current_version,
        'to_version': target_version,
        'backup_path': str(backup_path),
        'upgraded_by': os.getenv('USER', 'unknown'),
        'platform': sys.platform,
        'python_version': sys.version
    }
    
    with open('upgrade_info.json', 'w') as f:
        json.dump(info, f, indent=2)

def rollback(backup_path: Path):
    """回滚到备份版本"""
    # 恢复数据目录
    if (backup_path / 'data').exists():
        shutil.rmtree('data', ignore_errors=True)
        shutil.copytree(backup_path / 'data', 'data')
    
    # 恢复配置文件
    if (backup_path / 'config').exists():
        shutil.rmtree('config', ignore_errors=True)
        shutil.copytree(backup_path / 'config', 'config')
    
    # 恢复数据库
    if (backup_path / 'metadata.db').exists():
        shutil.copy2(backup_path / 'metadata.db', 'data/metadata.db')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MD Vector Processor升级工具')
    parser.add_argument('--target-version', type=str, required=True,
                       help='目标版本')
    parser.add_argument('--skip-backup', action='store_true',
                       help='跳过备份')
    parser.add_argument('--skip-tests', action='store_true',
                       help='跳过测试')
    parser.add_argument('--force', action='store_true',
                       help='强制升级，忽略版本兼容性检查')
    args = parser.parse_args()
    
    logger = setup_logging()
    logger.info("开始升级...")
    
    try:
        # 获取当前版本
        current_version = get_current_version()
        logger.info(f"当前版本: {current_version}")
        logger.info(f"目标版本: {args.target_version}")
        
        # 检查版本兼容性
        if not args.force and not check_version_compatibility(
            current_version, args.target_version):
            logger.error("版本不兼容，请参考升级指南进行手动升级")
            sys.exit(1)
        
        # 创建备份
        backup_path = None
        if not args.skip_backup:
            logger.info("创建备份...")
            backup_path = backup_data(Path('backups'))
            logger.info(f"备份已保存到: {backup_path}")
        
        try:
            # 更新依赖
            logger.info("更新依赖...")
            update_dependencies()
            
            # 更新配置
            logger.info("更新配置...")
            update_configuration(backup_path)
            
            # 运行数据迁移
            logger.info("运行数据迁移...")
            run_migrations()
            
            # 运行测试
            if not args.skip_tests:
                logger.info("运行测试...")
                run_tests()
            
            # 更新文档
            logger.info("更新文档...")
            update_documentation()
            
            # 保存升级信息
            save_upgrade_info(current_version, args.target_version, backup_path)
            
            logger.info("升级完成！")
            
        except Exception as e:
            logger.error(f"升级失败: {e}")
            if backup_path:
                logger.info("正在回滚到备份版本...")
                rollback(backup_path)
                logger.info("回滚完成")
            raise
        
    except Exception as e:
        logger.error(f"升级过程中出错: {e}")
        sys.exit(1)
    
    # 打印升级后说明
    print("\n=== 升级后说明 ===")
    print("1. 请查看CHANGELOG.md了解新版本的变更")
    print("2. 检查配置文件是否需要额外调整")
    print("3. 运行测试确保功能正常")
    print("4. 如果遇到问题，可以使用以下命令回滚:")
    if backup_path:
        print(f"   python upgrade.py --rollback {backup_path}")

if __name__ == '__main__':
    main()