#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复效果

验证向量化、搜索和AI对话功能是否正常工作
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_vectorization():
    """测试向量化功能"""
    print("\n=== 测试向量化功能 ===")

    try:
        from src.vectorizer import TextEmbedding
        from src.indexer import IndexBuilder
        from src.storage import MetadataManager, VectorStore

        # 配置
        config = {
            'indexing': {
                'index_type': 'flat',
                'metric': 'cosine',
                'quantization': 'none'  # 禁用量化
            },
            'vectorization': {
                'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                'vector_dimension': 384,
                'batch_size': 32,
                'device': 'cpu',
                'normalize_vectors': True
            },
            'storage': {
                'base_dir': 'data/vectors'
            }
        }

        # 测试文本
        test_texts = [
            "这是第一个测试文档，包含一些技术内容。",
            "这是第二个测试文档，讨论不同的主题。",
            "第三个文档涉及人工智能和机器学习。",
            "最后一个文档是关于向量数据库的应用。"
        ]

        print("1. 创建文本嵌入器...")
        embedder = TextEmbedding(config)

        print("2. 生成向量...")
        vectors = []
        for i, text in enumerate(test_texts):
            vector = embedder.encode_text(text)
            vectors.append(vector)
            print(f"   文档 {i+1}: 向量维度 {vector.shape}, 范围 [{vector.min():.4f}, {vector.max():.4f}]")

        print("3. 创建索引...")
        builder = IndexBuilder(config)

        # 添加向量到索引
        import numpy as np
        vectors_array = np.array(vectors)
        doc_ids = list(range(len(test_texts)))

        # 首先创建索引
        builder.create_index(vectors_array.shape[1])

        # 然后添加向量
        builder.index.add(vectors_array)
        builder.total_vectors = len(vectors)

        print(f"   索引类型: {type(builder.index).__name__}")
        print(f"   向量数量: {builder.total_vectors}")
        print(f"   向量维度: {vectors_array.shape[1]}")

        print("4. 保存索引...")
        index_path = Path("data/indices/test_index.idx")
        index_path.parent.mkdir(parents=True, exist_ok=True)

        if builder.save_index(index_path):
            print(f"   索引已保存: {index_path}")
        else:
            print("   索引保存失败")
            return False

        print("5. 保存元数据...")
        metadata_manager = MetadataManager(config)

        for i, text in enumerate(test_texts):
            metadata = {
                'text': text,
                'source': f'test_doc_{i+1}',
                'type': 'test',
                'length': len(text)
            }
            metadata_manager.store_metadata(i, metadata)

        print("6. 保存向量存储...")
        vector_store = VectorStore(config)
        vector_store.store_vectors(vectors_array, np.array(doc_ids))

        print("7. 测试搜索...")
        # 重新加载索引进行搜索测试
        builder2 = IndexBuilder(config)
        if builder2.load_index(index_path):
            print("   索引重新加载成功")

            # 测试搜索
            query_text = "人工智能技术"
            query_vector = embedder.encode_text(query_text)

            # 执行搜索
            distances, indices = builder2.index.search(query_vector.reshape(1, -1), k=3)

            print(f"   查询: {query_text}")
            print(f"   搜索结果:")
            for i, (dist, idx) in enumerate(zip(distances[0], indices[0])):
                if idx >= 0:
                    print(f"     {i+1}. 文档ID: {idx}, 距离: {dist:.4f}")
                    if idx < len(test_texts):
                        print(f"        内容: {test_texts[idx]}")
                else:
                    print(f"     {i+1}. 无效结果")
        else:
            print("   索引重新加载失败")
            return False

        # 清理资源
        embedder.cleanup()

        print("✅ 向量化功能测试通过")
        return True

    except Exception as e:
        print(f"❌ 向量化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_stats():
    """测试索引统计信息"""
    print("\n=== 测试索引统计信息 ===")

    try:
        from src.gui.state_manager import get_state_manager

        state_manager = get_state_manager()

        print("1. 获取可用索引...")
        indices = state_manager.get_available_indices()

        print(f"   找到 {len(indices)} 个索引:")
        for path, info in indices.items():
            print(f"     - {info['name']}")
            print(f"       路径: {path}")
            print(f"       向量数: {info['stats'].get('vector_count', 0)}")
            print(f"       维度: {info['stats'].get('dimension', 0)}")
            print(f"       类型: {info['stats'].get('index_type', 'unknown')}")
            print(f"       可加载: {info['stats'].get('loadable', False)}")

        print("2. 获取可用模型...")
        models = state_manager.get_available_models()

        print(f"   找到 {len(models)} 个模型:")
        for name, info in models.items():
            print(f"     - {name} ({info['type']})")
            print(f"       支持嵌入: {info['supports_embedding']}")
            print(f"       支持对话: {info['supports_chat']}")
            print(f"       可用: {info['is_available']}")

        print("✅ 索引统计信息测试通过")
        return True

    except Exception as e:
        print(f"❌ 索引统计信息测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_chat_search():
    """测试AI对话的搜索功能"""
    print("\n=== 测试AI对话搜索功能 ===")

    try:
        # 创建QApplication（如果不存在）
        from PyQt6.QtWidgets import QApplication
        import sys

        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 这里只测试搜索部分，不测试实际的AI对话
        from src.gui.widgets.ai_chat_dialog import AIChatDialog
        from src.gui.i18n import Translator

        # 创建翻译器
        from src.gui.i18n.translator import Language
        translator = Translator()
        translator.set_language(Language.CHINESE)

        # 创建对话窗口（不显示）
        dialog = AIChatDialog(translator)

        print("1. 加载索引库...")
        dialog._load_indices()

        index_count = dialog.index_combo.count()
        print(f"   找到 {index_count} 个选项")

        if index_count > 2:  # 除了默认选项
            print("2. 自动选择最新索引...")
            dialog._auto_select_latest_index()

            if hasattr(dialog, 'current_index_path') and dialog.current_index_path:
                print(f"   已选择索引: {dialog.current_index_path}")

                print("3. 测试搜索功能...")
                query = "测试查询"
                results = dialog._perform_search(query)

                print(f"   查询: {query}")
                print(f"   结果数量: {len(results)}")

                for i, result in enumerate(results[:3]):
                    print(f"     {i+1}. {result[:100]}...")

                print("✅ AI对话搜索功能测试通过")
                return True
            else:
                print("   未能选择索引")
                return False
        else:
            print("   没有可用的索引")
            return False

    except Exception as e:
        print(f"❌ AI对话搜索功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复效果...")

    results = []

    # 测试向量化功能
    results.append(test_vectorization())

    # 测试索引统计信息
    results.append(test_index_stats())

    # 测试AI对话搜索功能
    results.append(test_ai_chat_search())

    # 总结
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)

    print(f"通过: {passed}/{total}")

    if passed == total:
        print("🎉 所有测试通过！修复效果良好。")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
