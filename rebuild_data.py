#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据重建脚本

用于重建索引和元数据，解决ID映射不一致问题
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_rebuild.log')
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("=" * 60)
    print("数据重建工具")
    print("=" * 60)
    print()

    # 检查现有数据
    print("1. 检查现有数据状态...")

    vectors_dir = Path('data/vectors')
    indices_dir = Path('data/indices')

    has_vectors = vectors_dir.exists() and any(vectors_dir.iterdir())
    has_indices = indices_dir.exists() and any(indices_dir.iterdir())

    print(f"   向量数据目录: {'存在' if has_vectors else '不存在'}")
    print(f"   索引数据目录: {'存在' if has_indices else '不存在'}")

    if not (has_vectors or has_indices):
        print("❌ 没有找到现有数据，无需重建")
        return

    # 确认重建
    print("\n2. 确认重建操作...")
    print("⚠️  警告：此操作将：")
    print("   - 备份现有数据")
    print("   - 清空当前索引和向量数据")
    print("   - 从备份的元数据重新构建向量和索引")
    print("   - 使用连续的ID重新映射所有数据")
    print()

    # 检查是否有自动模式参数
    auto_mode = len(sys.argv) > 1 and sys.argv[1] == '--auto'

    if auto_mode:
        print("自动模式：继续重建...")
        confirm = 'y'
    else:
        confirm = input("是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return

    # 执行重建
    print("\n3. 开始数据重建...")

    try:
        from src.utils.data_rebuilder import create_data_rebuilder

        # 配置
        config = {
            'storage': {
                'base_dir': 'data/vectors'
            }
        }

        # 进度回调
        def progress_callback(current, total, message):
            if total > 0:
                percent = (current / total) * 100
                print(f"   进度: {percent:.1f}% - {message}")
            else:
                print(f"   {message}")

        # 创建重建器
        rebuilder = create_data_rebuilder(config, progress_callback)

        # 执行重建
        success = rebuilder.rebuild_all_data(backup_existing=True)

        if success:
            print("\n4. 验证重建结果...")

            # 验证数据
            verification = rebuilder.verify_rebuilt_data()

            print(f"   索引文件: {'✅ 存在' if verification['index_exists'] else '❌ 不存在'}")
            print(f"   元数据记录: {verification['metadata_count']} 条")
            print(f"   向量数量: {verification['vector_count']} 个")
            print(f"   ID一致性: {'✅ 一致' if verification['id_consistency'] else '❌ 不一致'}")
            print(f"   数据完整性: {'✅ 完整' if verification['data_consistent'] else '❌ 不完整'}")

            if verification['data_consistent']:
                print("\n🎉 数据重建成功！")
                print("   现在可以正常使用搜索功能了。")

                # 运行快速测试
                print("\n5. 运行快速测试...")
                test_success = run_quick_test()

                if test_success:
                    print("✅ 快速测试通过")
                else:
                    print("⚠️ 快速测试失败，可能需要进一步检查")

            else:
                print("\n❌ 数据重建失败")
                print("   请检查日志文件 data_rebuild.log 获取详细信息")
        else:
            print("\n❌ 数据重建失败")
            print("   请检查日志文件 data_rebuild.log 获取详细信息")

    except Exception as e:
        logger.error(f"重建过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        print(f"\n❌ 重建过程中出错: {e}")
        print("   请检查日志文件 data_rebuild.log 获取详细信息")

def run_quick_test():
    """运行快速测试"""
    try:
        print("   测试索引加载...")

        # 查找重建的索引
        indices_dir = Path('data/indices')
        index_files = list(indices_dir.glob('*.idx'))

        if not index_files:
            print("   ❌ 没有找到索引文件")
            return False

        index_file = index_files[0]
        print(f"   使用索引文件: {index_file}")

        # 测试索引加载
        from src.indexer import IndexBuilder
        config = {'indexing': {'index_type': 'flat', 'metric': 'cosine'}}
        builder = IndexBuilder(config)

        if not builder.load_index(index_file):
            print("   ❌ 索引加载失败")
            return False

        print(f"   ✅ 索引加载成功，包含 {builder.total_vectors} 个向量")

        # 测试搜索
        print("   测试搜索功能...")

        from src.vectorizer import TextEmbedding
        vectorization_config = {
            'vectorization': {
                'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                'vector_dimension': 384,
                'device': 'cpu'
            }
        }
        embedder = TextEmbedding(vectorization_config)

        # 生成测试查询向量
        query_vector = embedder.encode_text("测试查询")

        # 执行搜索
        distances, indices = builder.search(query_vector.reshape(1, -1), k=3)

        if len(indices[0]) > 0:
            print(f"   ✅ 搜索成功，返回 {len(indices[0])} 个结果")

            # 测试元数据获取
            print("   测试元数据获取...")

            from src.storage import MetadataManager
            config = {'storage': {'base_dir': 'data/vectors'}}
            metadata_manager = MetadataManager(config)

            # 尝试获取第一个结果的元数据
            first_idx = int(indices[0][0])
            metadata = metadata_manager.get_metadata(first_idx)

            if metadata and 'text' in metadata:
                print(f"   ✅ 元数据获取成功，文本长度: {len(metadata['text'])} 字符")
                return True
            else:
                print("   ❌ 元数据获取失败或没有文本内容")
                return False
        else:
            print("   ❌ 搜索没有返回结果")
            return False

        # 清理资源
        embedder.cleanup()

    except Exception as e:
        print(f"   ❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    main()
