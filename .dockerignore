
# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Test
.pytest_cache/
.coverage
htmlcov/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/
docs/api/

# Data and Logs
data/
logs/
cache/
*.log
*.h5
*.npy
*.npz
*.pkl
*.db

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Development
*.md
LICENSE
Makefile
requirements-dev.txt
tests/
examples/
*.sh
*.bat
*.ps1

# OS
.DS_Store
Thumbs.db

# Editor
.editorconfig

# CI/CD
.travis.yml
.github/
.gitlab-ci.yml