
.PHONY: help clean test lint format check build docs install dev-install

# 显示帮助信息
help:
	@echo "可用的命令："
	@echo "make clean      - 清理临时文件和构建产物"
	@echo "make test      - 运行测试"
	@echo "make lint      - 运行代码检查"
	@echo "make format    - 格式化代码"
	@echo "make check     - 运行所有检查（lint + test）"
	@echo "make build     - 构建项目"
	@echo "make docs      - 构建文档"
	@echo "make install   - 安装项目"
	@echo "make dev-install - 安装开发依赖"

# 清理临时文件和构建产物
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".coverage" -exec rm -rf {} +
	find . -type d -name "build" -exec rm -rf {} +
	find . -type d -name "dist" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".DS_Store" -delete
	find . -type f -name "*.coverage" -delete
	find . -type f -name "coverage.xml" -delete

# 运行测试
test:
	python -m pytest tests/ --cov=src --cov-report=term-missing --cov-report=html

# 运行代码检查
lint:
	flake8 src tests
	mypy src
	pylint src tests

# 格式化代码
format:
	isort src tests
	black src tests

# 运行所有检查
check: lint test

# 构建项目
build: clean
	python setup.py sdist bdist_wheel

# 构建文档
docs:
	$(MAKE) -C docs html

# 安装项目
install:
	pip install .

# 安装开发依赖
dev-install:
	pip install -e ".[dev]"

# 创建虚拟环境
venv:
	python -m venv venv
	@echo "请运行 'source venv/bin/activate' (Linux/Mac) 或 'venv\\Scripts\\activate' (Windows) 来激活虚拟环境"

# 更新依赖
update-deps:
	pip install --upgrade pip
	pip install --upgrade -r requirements.txt
	pip install --upgrade -r requirements-dev.txt

# 运行类型检查
type-check:
	mypy src --strict

# 运行安全检查
security-check:
	bandit -r src

# 运行性能测试
benchmark:
	python tests/benchmark.py

# 生成依赖图
deps-graph:
	pipdeptree > docs/dependencies.txt

# 检查依赖更新
check-deps:
	pip list --outdated

# 运行示例
run-example:
	python examples/basic_usage.py

# 创建新版本
version:
	@read -p "输入新版本号 (例如 1.0.1): " version; \
	echo "更新版本号为 $$version"
	@echo "请记得更新 CHANGELOG.md"

# 发布到PyPI
publish: clean build
	twine upload dist/*

# 运行开发服务器（如果适用）
serve:
	python -m md_vector_processor --debug

# 生成代码覆盖率报告
coverage:
	coverage run -m pytest tests/
	coverage report
	coverage html

# 检查代码复杂度
complexity:
	radon cc src -a -nb
	radon mi src -na

# 生成API文档
api-docs:
	pdoc --html --output-dir docs/api src/

# Docker相关命令
docker-build:
	docker build -t md-vector-processor .

docker-run:
	docker run -it md-vector-processor

# 持续集成检查
ci-check: clean lint test security-check type-check
