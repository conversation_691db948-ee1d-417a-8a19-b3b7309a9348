{"app_title": "MD Vector Processor", "file_menu": "File", "edit_menu": "Edit", "view_menu": "View", "tools_menu": "Tools", "help_menu": "Help", "language_menu": "Language", "open_file": "Open File", "save_file": "Save File", "save_as": "Save As", "exit": "Exit", "settings": "Settings", "about": "About", "vector_dimension": "Vector Dimension", "index_type": "Index Type", "metric": "Metric", "quantization": "Quantization", "process": "Process", "search": "Search", "visualize": "Visualize", "status_ready": "Ready", "status_processing": "Processing...", "status_completed": "Completed", "status_error": "Error", "confirm": "Confirm", "cancel": "Cancel", "apply": "Apply", "reset": "Reset", "add": "Add", "remove": "Remove", "edit": "Edit", "import": "Import", "export": "Export", "theme": "Theme", "dark_mode": "Dark Mode", "light_mode": "Light Mode", "system_theme": "System Theme", "welcome_message": "Welcome to MD Vector Processor", "getting_started": "Getting Started", "dashboard": "Dashboard", "index": "Index", "vectorize": "Vectorize", "language": "Language:", "tools": "Tools", "total_vectors": "Total Vectors", "total_indices": "Total Indices", "total_models": "Total Models", "total_queries": "Total Queries", "statistics": "Statistics", "quick_actions": "Quick Actions", "create_index": "Create Index", "create_index_desc": "Create a new vector index with various index types and quantization methods", "vectorize_data": "Vectorize Data", "vectorize_data_desc": "Convert text, images, or other data into vector representations", "search_vectors": "Search Vectors", "search_vectors_desc": "Search for similar content in vector indices", "visualize_vectors": "Visualize Vectors", "visualize_vectors_desc": "Visualize vector space and explore data distribution", "create": "Create", "index_management": "Index Management", "manage_indices": "Manage Indices", "index_name": "Index Name:", "dimension": "Dimension:", "advanced_options": "Advanced Options", "n_lists": "Number of Clusters:", "n_probes": "Number of Probes:", "ef_construction": "Construction Accuracy:", "ef_search": "Search Accuracy:", "refresh": "Refresh", "name": "Name", "type": "Type", "vectors": "Vectors", "actions": "Actions", "text_vectorization": "Text Vectorization", "file_vectorization": "File Vectorization", "model": "Model:", "batch_size": "<PERSON><PERSON> Size:", "input_text": "Input Text", "enter_text_here": "Enter text here...", "load_file": "Load File", "file_type": "File Type:", "select_files": "Select Files", "select_folder": "Select Folder", "selected_files_shown_here": "Selected files will be shown here...", "clear": "Clear", "vector_search": "Vector Search", "select_index": "Select Index:", "k_nearest": "Number of Results (k):", "query_input": "Query Input", "enter_query_here": "Enter query text here...", "search_results": "Search Results", "rank": "Rank", "similarity": "Similarity", "content": "Content", "vector_visualization": "Vector Visualization", "data_source": "Data Source", "max_points": "Max Points:", "visualization_settings": "Visualization Settings", "method": "Method:", "dimensions": "Dimensions:", "perplexity": "Perplexity:", "iterations": "Iterations:", "show_labels": "Show Labels", "color_settings": "Color Settings", "color_by": "Color By:", "color_map": "Color Map:", "point_size": "Point Size:", "none": "None", "cluster": "Cluster", "category": "Category", "custom": "Custom", "matplotlib_not_found": "Matplotlib library not found. Please install it: pip install matplotlib", "language_settings": "Language Settings", "theme_settings": "Theme Settings", "interface_language": "Interface Language:", "interface_theme": "Interface Theme:", "model_settings": "Model Settings", "processing_settings": "Processing Settings", "default_model": "Default Model:", "model_cache_dir": "Model Cache Directory:", "use_gpu": "Use GPU:", "index_settings": "Index Settings", "default_index_type": "Default Index Type:", "default_metric": "Default <PERSON>:", "default_quantization": "Default Quantization:", "index_directory": "Index Directory:", "browse": "Browse", "general": "General", "vectorization": "Vectorization", "indexing": "Indexing", "version": "Version", "about_description": "MD Vector Processor is a powerful vector processing tool that supports various index types and quantization methods.", "about_features": "Main Features:", "feature_1": "Support for various index types (Flat, IVF, HNSW, Hybrid)", "feature_2": "Support for various quantization methods (PQ, OPQ, SQ)", "feature_3": "Support for multilingual text vectorization", "feature_4": "Support for vector visualization", "feature_5": "Support for GPU acceleration", "about_license": "This software is open source under the MIT license.", "github_repo": "GitHub Repository", "github": "GitHub", "documentation": "Documentation", "license": "License", "all_rights_reserved": "All rights reserved."}