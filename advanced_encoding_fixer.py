#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级编码修复工具
专门处理文件名和内容中的编码问题
"""

import json
import os
import re
from pathlib import Path
import chardet
import argparse

class AdvancedEncodingFixer:
    """高级编码修复器"""

    def __init__(self):
        # 常见的编码错误映射
        self.encoding_fixes = {
            # 中文字符的常见编码错误
            '绾挎潫': '线束',
            '姊呰禌寰锋柉濂旈┌': '梅赛德斯奔驰',
            '濂旈┌': '奔驰',
            '绂忕壒': '福特',
            '鐢ㄦ埛鎻愪緵': '用户提供',
            '鍒堕€犳爣鍑?': '制造标准',
            '鎬绘垚': '总成',
            '鐒婃帴': '焊接',
            '娴嬭瘯': '测试',
            '鏍囧噯': '标准',
            '姹借溅': '汽车',
            '鍥剧焊': '图纸',
            '瑕佹眰': '要求',
            # 新增的映射
            '濂旈': '奔驰',
            '鍒堕€?': '制造',
            '鎬绘垚鍒堕€犳爣鍑?': '总成制造标准',
            '绾挎潫鎬绘垚鍒堕€犳爣鍑?': '线束总成制造标准',
            '绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '线束焊接测试标准',
            '濂旈┌绾挎潫鐒婃帴娴嬭瘯鏍囧噯': '奔驰线束焊接测试标准',
            '鐢靛瓙': '电子',
            '鐢垫皵': '电气',
            '閮ㄤ欢': '部件',
            '缁勪欢': '组件',
            '杩炴帴鍣?': '连接器',
            '绔瓙': '端子',
            '鎺ユ彃浠?': '接插件',
            '淇濋櫓涓?': '保险丝',
            '缁ф斁鍣?': '继电器',
            '浼犳劅鍣?': '传感器',
            '鎵ц鍣?': '执行器',
            '鎺у埗鍣?': '控制器',
            '妯″潡': '模块',
            '绯荤粺': '系统',
            '缃戠粶': '网络',
            '閫氫俊': '通信',
            '鍗忚': '协议',
            '鏁版嵁': '数据',
            '淇″彿': '信号',
            '鐢垫簮': '电源',
            '鎺ュ湴': '接地',
            '灞忚斀': '屏蔽',
            '缁濈紭': '绝缘',
            '闃叉按': '防水',
            '闃插皹': '防尘',
            '鑰愭俯': '耐温',
            '鑰愮(': '耐磨',
            '鑰愯厫铓?': '耐腐蚀',
            '鑰愭补': '耐油',
            '鑰愮噧': '耐燃',
            '闃荤噧': '阻燃',
            '鐜繚': '环保',
            '瀹夊叏': '安全',
            '鍙潬鎬?': '可靠性',
            '鑰愪箙鎬?': '耐久性',
            '绋冲畾鎬?': '稳定性',
            '鍏煎鎬?': '兼容性',
            '鎬ц兘': '性能',
            '鍔熻兘': '功能',
            '鍙傛暟': '参数',
            '瑙勬牸': '规格',
            '灏哄': '尺寸',
            '閲嶉噺': '重量',
            '鏉愭枡': '材料',
            '宸ヨ壓': '工艺',
            '鍒堕€?': '制造',
            '鐢熶骇': '生产',
            '瑁呴厤': '装配',
            '瀹夎': '安装',
            '璋冭瘯': '调试',
            '妫€娴?': '检测',
            '娴嬭瘯': '测试',
            '楠岃瘉': '验证',
            '璁よ瘉': '认证',
            '瀹℃牳': '审核',
            '鎵瑰噯': '批准',
            '鍙戝竷': '发布',
            '瀹炴柦': '实施',
            '鎵ц': '执行',
            '绠＄悊': '管理',
            '鎺у埗': '控制',
            '鐩戞帶': '监控',
            '缁存姢': '维护',
            '淇濆吇': '保养',
            '缁翠慨': '维修',
            '鏇存崲': '更换',
            '鍗囩骇': '升级',
            '鏀硅繘': '改进',
            '浼樺寲': '优化',
            '鍒涙柊': '创新',
            '鍙戝睍': '发展',
            '搴旂敤': '应用',
            '瀹炵幇': '实现',
            '瀹炵幇': '实现'
        }

        # 更复杂的编码错误模式
        self.complex_patterns = [
            # UTF-8被错误解释为其他编码的情况
            (r'绾挎潫', '线束'),
            (r'姊呰禌寰锋柉濂旈┌', '梅赛德斯奔驰'),
            (r'濂旈┌', '奔驰'),
            (r'绂忕壒', '福特'),
            (r'鐢ㄦ埛鎻愪緵', '用户提供'),
            (r'鍒堕€犳爣鍑?', '制造标准'),
            (r'鎬绘垚', '总成'),
            (r'鐒婃帴', '焊接'),
            (r'娴嬭瘯', '测试'),
            (r'鏍囧噯', '标准'),
            (r'姹借溅', '汽车'),
            (r'鍥剧焊', '图纸'),
            (r'瑕佹眰', '要求'),
        ]

    def detect_and_fix_encoding(self, text: str) -> str:
        """检测并修复编码问题"""
        if not text:
            return ""

        # 首先尝试简单的字符替换
        fixed_text = text
        for wrong, correct in self.encoding_fixes.items():
            fixed_text = fixed_text.replace(wrong, correct)

        # 如果还有问题，尝试更复杂的修复
        for pattern, replacement in self.complex_patterns:
            fixed_text = re.sub(pattern, replacement, fixed_text)

        # 尝试编码转换修复
        if any(char in fixed_text for char in ['绾', '姊', '濂', '绂', '鐢', '鍒', '鎬', '鐒', '娴', '鏍', '姹', '鍥', '瑕']):
            fixed_text = self.try_encoding_conversion(fixed_text)

        return fixed_text

    def try_encoding_conversion(self, text: str) -> str:
        """尝试编码转换修复"""
        try:
            # 尝试不同的编码转换
            encodings_to_try = [
                ('utf-8', 'gbk'),
                ('utf-8', 'gb2312'),
                ('utf-8', 'gb18030'),
                ('latin1', 'utf-8'),
                ('cp1252', 'utf-8'),
            ]

            for from_enc, to_enc in encodings_to_try:
                try:
                    # 先编码为bytes，再用正确编码解码
                    bytes_data = text.encode(from_enc, errors='ignore')
                    decoded = bytes_data.decode(to_enc, errors='ignore')

                    # 检查是否包含正常的中文字符
                    if any('\u4e00' <= char <= '\u9fff' for char in decoded):
                        return decoded
                except:
                    continue

        except Exception:
            pass

        return text

    def fix_metadata_file(self, metadata_file: Path) -> bool:
        """修复单个元数据文件"""
        try:
            # 读取原始文件
            with open(metadata_file, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()

            # 解析JSON
            metadata = json.loads(content)

            # 检查是否需要修复
            needs_fix = False

            # 修复各个字段
            if 'title' in metadata:
                original_title = metadata['title']
                fixed_title = self.detect_and_fix_encoding(original_title)
                if fixed_title != original_title:
                    metadata['title'] = fixed_title
                    needs_fix = True

            if 'source' in metadata:
                original_source = metadata['source']
                fixed_source = self.detect_and_fix_encoding(original_source)
                if fixed_source != original_source:
                    metadata['source'] = fixed_source
                    needs_fix = True

            if 'file_path' in metadata:
                original_path = metadata['file_path']
                fixed_path = self.detect_and_fix_encoding(original_path)
                if fixed_path != original_path:
                    metadata['file_path'] = fixed_path
                    needs_fix = True

            if 'keywords' in metadata and isinstance(metadata['keywords'], list):
                original_keywords = metadata['keywords']
                fixed_keywords = [self.detect_and_fix_encoding(kw) for kw in original_keywords]
                if fixed_keywords != original_keywords:
                    metadata['keywords'] = fixed_keywords
                    needs_fix = True

            if 'abstract' in metadata:
                original_abstract = metadata['abstract']
                fixed_abstract = self.detect_and_fix_encoding(original_abstract)
                if fixed_abstract != original_abstract:
                    metadata['abstract'] = fixed_abstract
                    needs_fix = True

            # 如果需要修复，保存文件
            if needs_fix:
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                return True

            return False

        except Exception as e:
            print(f"修复文件失败 {metadata_file}: {e}")
            return False

    def fix_directory(self, directory: Path) -> dict:
        """修复目录下的所有元数据文件"""
        metadata_files = list(directory.rglob("*_metadata.json"))

        stats = {
            'total_files': len(metadata_files),
            'fixed_files': 0,
            'skipped_files': 0,
            'error_files': 0
        }

        print(f"找到 {len(metadata_files)} 个元数据文件")

        for i, metadata_file in enumerate(metadata_files, 1):
            print(f"[{i}/{len(metadata_files)}] 处理: {metadata_file.name}")

            try:
                if self.fix_metadata_file(metadata_file):
                    stats['fixed_files'] += 1
                    print(f"  ✅ 修复完成")
                else:
                    stats['skipped_files'] += 1
                    print(f"  ⏭️ 无需修复")

            except Exception as e:
                stats['error_files'] += 1
                print(f"  ❌ 处理失败: {e}")

        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='高级编码修复工具')
    parser.add_argument('directory', help='要处理的目录路径')
    parser.add_argument('--test', action='store_true', help='测试模式，只显示会修复的内容')

    args = parser.parse_args()

    directory = Path(args.directory)
    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        return

    print("🚀 开始高级编码修复")
    print("="*80)
    print(f"📁 处理目录: {directory}")

    fixer = AdvancedEncodingFixer()

    if args.test:
        print("🧪 测试模式 - 只显示会修复的内容")
        # 测试几个文件
        test_files = list(directory.rglob("*_metadata.json"))[:5]
        for test_file in test_files:
            print(f"\n📄 测试文件: {test_file.name}")
            try:
                with open(test_file, 'r', encoding='utf-8', errors='replace') as f:
                    content = f.read()
                metadata = json.loads(content)

                print(f"  原标题: {metadata.get('title', 'N/A')}")
                fixed_title = fixer.detect_and_fix_encoding(metadata.get('title', ''))
                print(f"  修复后: {fixed_title}")

                print(f"  原来源: {metadata.get('source', 'N/A')}")
                fixed_source = fixer.detect_and_fix_encoding(metadata.get('source', ''))
                print(f"  修复后: {fixed_source}")

            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
    else:
        # 实际修复
        stats = fixer.fix_directory(directory)

        print(f"\n📊 修复完成:")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  修复文件: {stats['fixed_files']}")
        print(f"  跳过文件: {stats['skipped_files']}")
        print(f"  错误文件: {stats['error_files']}")

        if stats['total_files'] > 0:
            success_rate = (stats['fixed_files'] + stats['skipped_files']) / stats['total_files'] * 100
            print(f"  成功率: {success_rate:.1f}%")

    print("\n🎯 高级编码修复完成")
    print("="*80)

if __name__ == "__main__":
    main()
