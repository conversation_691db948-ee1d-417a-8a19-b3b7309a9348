
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量存储器模块
负责向量数据的持久化管理
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import h5py
from pathlib import Path
import json
import shutil
import time
from datetime import datetime
import zlib
from tqdm import tqdm
import threading
import hashlib

class VectorStore:
    """向量存储器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化向量存储器

        Args:
            config: 配置字典，包含存储相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.storage_config = config.get('storage', {})
        self.vector_format = self.storage_config.get('vector_format', 'numpy')
        self.compression = self.storage_config.get('compression', True)
        self.backup_enabled = self.storage_config.get('backup_enabled', True)
        self.backup_frequency = self.storage_config.get('backup_frequency', 'daily')

        # 存储路径
        self.base_dir = Path(self.storage_config.get('base_dir', 'data/vectors'))
        self.backup_dir = self.base_dir / 'backups'

        # 创建必要的目录
        self.base_dir.mkdir(parents=True, exist_ok=True)
        if self.backup_enabled:
            self.backup_dir.mkdir(parents=True, exist_ok=True)

        # 版本管理
        self.version_file = self.base_dir / 'version.json'
        self.current_version = self._load_version()

        # 线程锁
        self.store_lock = threading.Lock()

    def _load_version(self) -> Dict[str, Any]:
        """
        加载版本信息

        Returns:
            Dict[str, Any]: 版本信息字典
        """
        if self.version_file.exists():
            try:
                with open(self.version_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载版本信息时出错: {e}")

        # 创建新的版本信息
        version_info = {
            'version': 1,
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'total_vectors': 0,
            'checksum': ''
        }

        self._save_version(version_info)
        return version_info

    def _save_version(self, version_info: Dict[str, Any]) -> None:
        """
        保存版本信息

        Args:
            version_info: 版本信息字典
        """
        try:
            with open(self.version_file, 'w') as f:
                json.dump(version_info, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存版本信息时出错: {e}")

    def _compress_vectors(self, vectors: np.ndarray) -> bytes:
        """
        压缩向量数据

        Args:
            vectors: 向量数据

        Returns:
            bytes: 压缩后的数据
        """
        try:
            # 将数组转换为字节
            vector_bytes = vectors.tobytes()

            # 使用zlib压缩
            compressed = zlib.compress(vector_bytes)

            return compressed
        except Exception as e:
            self.logger.error(f"压缩向量数据时出错: {e}")
            return vectors.tobytes()

    def _decompress_vectors(self, compressed_data: bytes, shape: Tuple[int, ...],
                          dtype: np.dtype) -> np.ndarray:
        """
        解压缩向量数据

        Args:
            compressed_data: 压缩的数据
            shape: 向量数组的形状
            dtype: 数据类型

        Returns:
            np.ndarray: 解压缩后的向量数组
        """
        try:
            # 解压缩数据
            decompressed = zlib.decompress(compressed_data)

            # 重建numpy数组
            vectors = np.frombuffer(decompressed, dtype=dtype).reshape(shape)

            return vectors
        except Exception as e:
            self.logger.error(f"解压缩向量数据时出错: {e}")
            return np.zeros(shape, dtype=dtype)

    def _calculate_checksum(self, vectors: np.ndarray) -> str:
        """
        计算向量数据的校验和

        Args:
            vectors: 向量数据

        Returns:
            str: 校验和字符串
        """
        try:
            return hashlib.md5(vectors.tobytes()).hexdigest()
        except Exception as e:
            self.logger.error(f"计算校验和时出错: {e}")
            return ""

    def _create_backup(self) -> bool:
        """
        创建数据备份

        Returns:
            bool: 是否成功创建备份
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_dir / f'vectors_backup_{timestamp}'

            # 复制当前数据
            shutil.copytree(self.base_dir, backup_path, ignore=shutil.ignore_patterns('backups'))

            # 清理旧备份
            self._cleanup_old_backups()

            self.logger.info(f"创建了新的备份: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建备份时出错: {e}")
            return False

    def _cleanup_old_backups(self) -> None:
        """清理旧的备份文件"""
        try:
            backups = sorted(self.backup_dir.glob('vectors_backup_*'))

            # 根据备份频率保留的数量
            if self.backup_frequency == 'daily':
                max_backups = 7
            elif self.backup_frequency == 'weekly':
                max_backups = 4
            else:
                max_backups = 30

            # 删除多余的备份
            while len(backups) > max_backups:
                oldest = backups.pop(0)
                shutil.rmtree(oldest)
                self.logger.info(f"删除了旧备份: {oldest}")

        except Exception as e:
            self.logger.error(f"清理旧备份时出错: {e}")

    def store_vectors(self, vectors: np.ndarray, ids: Optional[np.ndarray] = None,
                     batch_size: int = 1000) -> bool:
        """
        存储向量数据

        Args:
            vectors: 向量数据
            ids: 向量ID数组（可选）
            batch_size: 批处理大小

        Returns:
            bool: 是否成功存储
        """
        try:
            if ids is None:
                ids = np.arange(vectors.shape[0])

            store_path = self.base_dir / 'vectors.h5'

            with self.store_lock:
                with h5py.File(store_path, 'a') as f:
                    # 获取当前时间戳作为唯一标识
                    import time
                    timestamp = int(time.time() * 1000)

                    # 存储向量数据
                    for i in tqdm(range(0, len(vectors), batch_size), desc="Storing vectors"):
                        batch_vectors = vectors[i:i + batch_size]
                        batch_ids = ids[i:i + batch_size]

                        # 使用时间戳创建唯一的数据集名称
                        unique_suffix = f"_{timestamp}_{i}"

                        # 压缩数据（如果启用）
                        if self.compression:
                            compressed_data = self._compress_vectors(batch_vectors)
                            dset_name = f'compressed{unique_suffix}'
                            f.create_dataset(dset_name, data=np.void(compressed_data))
                        else:
                            dset_name = f'vectors{unique_suffix}'
                            f.create_dataset(dset_name, data=batch_vectors)

                        # 存储ID映射
                        f.create_dataset(f'ids{unique_suffix}', data=batch_ids)

                    # 存储元信息
                    f.attrs['total_vectors'] = len(vectors)
                    f.attrs['vector_dim'] = vectors.shape[1]
                    f.attrs['compression'] = self.compression
                    f.attrs['dtype'] = str(vectors.dtype)

                # 更新版本信息
                self.current_version['version'] += 1
                self.current_version['last_updated'] = datetime.now().isoformat()
                self.current_version['total_vectors'] = len(vectors)
                self.current_version['checksum'] = self._calculate_checksum(vectors)
                self._save_version(self.current_version)

                # 创建备份（如果启用）
                if self.backup_enabled:
                    self._create_backup()

                return True

        except Exception as e:
            self.logger.error(f"存储向量数据时出错: {e}")
            return False

    def load_vectors(self, ids: Optional[np.ndarray] = None) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        加载向量数据

        Args:
            ids: 要加载的向量ID数组（可选）

        Returns:
            Tuple[Optional[np.ndarray], Optional[np.ndarray]]: (向量数组, ID数组)
        """
        try:
            store_path = self.base_dir / 'vectors.h5'

            if not store_path.exists():
                raise FileNotFoundError("向量存储文件不存在")

            with self.store_lock:
                with h5py.File(store_path, 'r') as f:
                    total_vectors = f.attrs['total_vectors']
                    vector_dim = f.attrs['vector_dim']
                    compression = f.attrs['compression']
                    dtype = np.dtype(f.attrs['dtype'])

                    # 准备结果数组
                    vectors = np.zeros((total_vectors, vector_dim), dtype=dtype)
                    all_ids = np.zeros(total_vectors, dtype=np.int64)

                    # 加载数据
                    current_idx = 0

                    # 获取所有数据集名称
                    dataset_names = list(f.keys())
                    compressed_datasets = sorted([name for name in dataset_names if name.startswith('compressed_')])
                    vector_datasets = sorted([name for name in dataset_names if name.startswith('vectors_')])
                    id_datasets = sorted([name for name in dataset_names if name.startswith('ids_')])

                    # 添加新格式的数据集名称
                    compressed_datasets.extend(sorted([name for name in dataset_names if name.startswith('compressed_') and '_' in name]))
                    vector_datasets.extend(sorted([name for name in dataset_names if name.startswith('vectors_') and '_' in name]))
                    id_datasets.extend(sorted([name for name in dataset_names if name.startswith('ids_') and '_' in name]))

                    # 确保数据集列表长度一致
                    if compression:
                        dataset_pairs = list(zip(compressed_datasets, id_datasets))
                    else:
                        dataset_pairs = list(zip(vector_datasets, id_datasets))

                    # 加载每个数据集
                    for data_name, id_name in dataset_pairs:
                        if compression:
                            compressed_data = f[data_name][()]
                            # 估计批次大小
                            batch_size = min(1000, total_vectors - current_idx)
                            batch_vectors = self._decompress_vectors(
                                compressed_data,
                                (batch_size, vector_dim),
                                dtype
                            )
                        else:
                            batch_vectors = f[data_name][:]

                        batch_ids = f[id_name][:]

                        # 确保不会超出数组边界
                        if current_idx + len(batch_vectors) <= total_vectors:
                            vectors[current_idx:current_idx + len(batch_vectors)] = batch_vectors
                            all_ids[current_idx:current_idx + len(batch_ids)] = batch_ids
                            current_idx += len(batch_vectors)
                        else:
                            # 如果超出边界，只复制能容纳的部分
                            remaining = total_vectors - current_idx
                            vectors[current_idx:] = batch_vectors[:remaining]
                            all_ids[current_idx:] = batch_ids[:remaining]
                            current_idx = total_vectors
                            break

                    # 如果指定了ID，只返回这些ID的向量
                    if ids is not None:
                        mask = np.isin(all_ids, ids)
                        vectors = vectors[mask]
                        all_ids = all_ids[mask]

                    return vectors, all_ids

        except Exception as e:
            self.logger.error(f"加载向量数据时出错: {e}")
            return None, None

    def delete_vectors(self, ids: np.ndarray) -> bool:
        """
        删除指定ID的向量

        Args:
            ids: 要删除的向量ID数组

        Returns:
            bool: 是否成功删除
        """
        try:
            # 加载所有向量
            vectors, all_ids = self.load_vectors()
            if vectors is None or all_ids is None:
                return False

            # 创建掩码
            mask = ~np.isin(all_ids, ids)

            # 保存剩余的向量
            remaining_vectors = vectors[mask]
            remaining_ids = all_ids[mask]

            # 存储更新后的数据
            return self.store_vectors(remaining_vectors, remaining_ids)

        except Exception as e:
            self.logger.error(f"删除向量时出错: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """
        获取向量存储统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            store_path = self.base_dir / 'vectors.h5'

            if not store_path.exists():
                return {
                    'total_vectors': 0,
                    'vector_dimension': 0,
                    'file_size_bytes': 0,
                    'file_size_mb': 0,
                    'status': 'empty'
                }

            with h5py.File(store_path, 'r') as f:
                total_vectors = f.attrs.get('total_vectors', 0)
                vector_dim = f.attrs.get('vector_dim', 0)
                file_size = store_path.stat().st_size

                return {
                    'total_vectors': total_vectors,
                    'vector_dimension': vector_dim,
                    'file_size_bytes': file_size,
                    'file_size_mb': file_size / (1024 * 1024),
                    'compression': f.attrs.get('compression', False),
                    'dtype': f.attrs.get('dtype', 'unknown'),
                    'status': 'active'
                }

        except Exception as e:
            self.logger.error(f"获取统计信息时出错: {e}")
            return {
                'total_vectors': 0,
                'vector_dimension': 0,
                'file_size_bytes': 0,
                'file_size_mb': 0,
                'status': 'error',
                'error': str(e)
            }

    def get_store_info(self) -> Dict[str, Any]:
        """
        获取存储信息

        Returns:
            Dict[str, Any]: 存储信息字典
        """
        try:
            store_path = self.base_dir / 'vectors.h5'

            if not store_path.exists():
                return {
                    'status': 'empty',
                    'version': self.current_version
                }

            with h5py.File(store_path, 'r') as f:
                info = {
                    'status': 'active',
                    'version': self.current_version,
                    'total_vectors': f.attrs['total_vectors'],
                    'vector_dim': f.attrs['vector_dim'],
                    'compression': f.attrs['compression'],
                    'dtype': f.attrs['dtype'],
                    'file_size': store_path.stat().st_size,
                    'last_modified': datetime.fromtimestamp(store_path.stat().st_mtime).isoformat()
                }

                return info

        except Exception as e:
            self.logger.error(f"获取存储信息时出错: {e}")
            return {'status': 'error', 'error': str(e)}

    def restore_from_backup(self, backup_timestamp: Optional[str] = None) -> bool:
        """
        从备份恢复数据

        Args:
            backup_timestamp: 备份时间戳（可选，默认使用最新的备份）

        Returns:
            bool: 是否成功恢复
        """
        try:
            if not self.backup_enabled:
                raise ValueError("备份功能未启用")

            # 获取备份列表
            backups = sorted(self.backup_dir.glob('vectors_backup_*'))
            if not backups:
                raise ValueError("没有可用的备份")

            # 选择要恢复的备份
            if backup_timestamp:
                backup_path = self.backup_dir / f'vectors_backup_{backup_timestamp}'
                if not backup_path.exists():
                    raise ValueError(f"找不到指定的备份: {backup_timestamp}")
            else:
                backup_path = backups[-1]  # 使用最新的备份

            # 创建当前数据的备份
            current_backup = self.backup_dir / f'pre_restore_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            shutil.copytree(self.base_dir, current_backup, ignore=shutil.ignore_patterns('backups'))

            # 恢复数据
            for item in backup_path.iterdir():
                if item.name != 'backups':
                    target = self.base_dir / item.name
                    if target.exists():
                        if target.is_file():
                            target.unlink()
                        else:
                            shutil.rmtree(target)
                    if item.is_file():
                        shutil.copy2(item, target)
                    else:
                        shutil.copytree(item, target)

            # 重新加载版本信息
            self.current_version = self._load_version()

            self.logger.info(f"成功从备份恢复: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"从备份恢复时出错: {e}")
            return False

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'storage': {
            'base_dir': 'data/vectors',
            'vector_format': 'numpy',
            'compression': True,
            'backup_enabled': True,
            'backup_frequency': 'daily'
        }
    }

    store = VectorStore(test_config)

    # 生成测试向量
    n_vectors = 1000
    dimension = 384
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')
    test_ids = np.arange(n_vectors)

    # 测试存储向量
    print("存储向量...")
    success = store.store_vectors(test_vectors, test_ids)
    print(f"存储{'成功' if success else '失败'}")

    # 测试加载向量
    print("\n加载向量...")
    loaded_vectors, loaded_ids = store.load_vectors()
    if loaded_vectors is not None:
        print(f"加载了 {len(loaded_vectors)} 个向量")

    # 测试删除向量
    delete_ids = np.array([0, 1, 2])
    print(f"\n删除向量 {delete_ids}...")
    success = store.delete_vectors(delete_ids)
    print(f"删除{'成功' if success else '失败'}")

    # 显示存储信息
    print("\n存储信息:")
    info = store.get_store_info()
    print(json.dumps(info, indent=2))