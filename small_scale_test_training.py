#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
小规模测试训练
选择100个高质量文档进行测试训练
"""

import json
import shutil
from pathlib import Path
from typing import List, Dict
import time
import random

class SmallScaleTestTraining:
    """小规模测试训练"""

    def __init__(self):
        self.test_dir = Path("test_training_data")
        self.original_dir = Path("training_data")
        self.selected_files = []

    def select_high_quality_documents(self, target_count: int = 100) -> List[Dict]:
        """选择高质量文档"""

        print(f"🔍 选择 {target_count} 个高质量文档")
        print("-" * 60)

        metadata_dir = self.original_dir / "raw_documents" / "enterprise_standards" / "metadata"
        metadata_files = list(metadata_dir.glob("*_metadata.json"))

        # 评估每个文档的质量
        document_scores = []

        for file_path in metadata_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

                # 计算质量评分
                score = self._calculate_quality_score(metadata)

                document_scores.append({
                    'metadata_file': file_path,
                    'metadata': metadata,
                    'quality_score': score
                })

            except Exception as e:
                print(f"❌ 读取文件失败 {file_path.name}: {e}")

        # 按质量评分排序
        document_scores.sort(key=lambda x: x['quality_score'], reverse=True)

        # 选择前N个高质量文档
        selected = document_scores[:target_count]

        print(f"📊 文档质量分析:")
        print(f"  总文档数: {len(document_scores)}")
        print(f"  选择文档数: {len(selected)}")
        print(f"  平均质量评分: {sum(d['quality_score'] for d in selected) / len(selected):.3f}")
        print(f"  最高质量评分: {selected[0]['quality_score']:.3f}")
        print(f"  最低质量评分: {selected[-1]['quality_score']:.3f}")

        return selected

    def _calculate_quality_score(self, metadata: Dict) -> float:
        """计算文档质量评分"""
        score = 0.0

        # 基础信息完整性 (30%)
        if metadata.get('standard_number'):
            score += 0.1
        if metadata.get('title'):
            score += 0.1
        if metadata.get('keywords') and len(metadata['keywords']) > 0:
            score += 0.1

        # PDF内容质量 (40%)
        page_count = metadata.get('page_count', 0)
        word_count = metadata.get('word_count', 0)
        abstract = metadata.get('abstract', '')

        if page_count and page_count > 5:
            score += 0.1
        if word_count and word_count > 1000:
            score += 0.1
        if abstract and len(abstract) > 50:
            score += 0.1
        if metadata.get('pdf_extraction_method'):
            score += 0.1

        # 汽车相关性 (20%)
        automotive_relevance = metadata.get('automotive_relevance', 0)
        if automotive_relevance >= 0.6:
            score += 0.2
        elif automotive_relevance >= 0.3:
            score += 0.1

        # 技术专业度 (10%)
        technical_domains = metadata.get('technical_domains', [])
        if technical_domains and technical_domains != ['general']:
            score += 0.1

        return score

    def create_test_dataset(self, selected_documents: List[Dict]) -> bool:
        """创建测试数据集"""

        print(f"\n📁 创建测试数据集")
        print("-" * 60)

        # 清理并创建测试目录
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

        # 创建目录结构
        test_raw_dir = self.test_dir / "raw_documents" / "enterprise_standards"
        test_metadata_dir = test_raw_dir / "metadata"
        test_pdfs_dir = test_raw_dir / "pdfs"

        test_metadata_dir.mkdir(parents=True, exist_ok=True)
        test_pdfs_dir.mkdir(parents=True, exist_ok=True)

        copied_count = 0
        failed_count = 0

        for doc in selected_documents:
            try:
                metadata_file = doc['metadata_file']
                metadata = doc['metadata']

                # 复制元数据文件
                dest_metadata = test_metadata_dir / metadata_file.name
                shutil.copy2(metadata_file, dest_metadata)

                # 复制PDF文件
                pdf_path = Path(metadata.get('file_path', ''))
                if pdf_path.exists():
                    dest_pdf = test_pdfs_dir / pdf_path.name
                    shutil.copy2(pdf_path, dest_pdf)

                    # 更新元数据中的文件路径
                    metadata['file_path'] = str(dest_pdf)
                    with open(dest_metadata, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, ensure_ascii=False, indent=2)

                    copied_count += 1
                else:
                    print(f"⚠️ PDF文件不存在: {pdf_path}")
                    failed_count += 1

            except Exception as e:
                print(f"❌ 复制文件失败: {e}")
                failed_count += 1

        print(f"✅ 测试数据集创建完成:")
        print(f"  成功复制: {copied_count} 个文档")
        print(f"  复制失败: {failed_count} 个文档")
        print(f"  测试目录: {self.test_dir}")

        return copied_count > 0

    def run_test_training(self) -> Dict:
        """运行测试训练"""

        print(f"\n🚀 开始测试训练")
        print("=" * 80)

        start_time = time.time()

        # 导入训练模块
        try:
            from training_automation import AutomotiveTrainingPipeline

            # 创建训练实例
            trainer = AutomotiveTrainingPipeline()

            # 临时修改数据目录配置
            original_raw_dir = trainer.config["data_directories"]["raw_documents"]
            trainer.config["data_directories"]["raw_documents"] = str(self.test_dir / "raw_documents")

            # 运行训练流程
            print("📊 开始文档扫描...")
            documents = trainer.scan_documents()

            if not any(documents.values()):
                return {
                    'success': False,
                    'error': '没有找到可处理的文档'
                }

            print("📄 开始文档处理...")
            chunks = trainer.process_documents(documents)

            if not chunks:
                return {
                    'success': False,
                    'error': '文档处理失败，没有生成文本块'
                }

            print("🔢 开始向量化...")
            vectors, vector_ids, processed_chunks = trainer.vectorize_chunks(chunks)

            print("🏗️ 开始索引训练...")
            trained_indices = trainer.train_indices(vectors, vector_ids)

            print("💾 保存训练数据...")
            trainer.save_training_data(processed_chunks, vectors, vector_ids)

            # 恢复原始配置
            trainer.config["data_directories"]["raw_documents"] = original_raw_dir

            training_time = time.time() - start_time

            return {
                'success': True,
                'training_time': training_time,
                'documents_found': sum(len(files) for files in documents.values()),
                'chunks_generated': len(chunks),
                'vectors_generated': len(vectors),
                'indices_trained': trained_indices,
                'processed_chunks': len(processed_chunks)
            }

        except ImportError as e:
            return {
                'success': False,
                'error': f'无法导入训练模块: {e}',
                'suggestion': '请确保 training_automation.py 文件存在'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'训练过程出错: {e}'
            }

    def analyze_test_results(self, results: Dict) -> None:
        """分析测试结果"""

        print(f"\n📊 测试结果分析")
        print("=" * 80)

        if not results.get('success', False):
            print(f"❌ 测试失败: {results.get('error', '未知错误')}")
            if 'details' in results:
                print(f"详细信息: {results['details']}")
            if 'suggestion' in results:
                print(f"建议: {results['suggestion']}")
            return

        training_time = results.get('training_time', 0)
        documents_found = results.get('documents_found', 0)
        chunks_generated = results.get('chunks_generated', 0)
        vectors_generated = results.get('vectors_generated', 0)
        indices_trained = results.get('indices_trained', {})
        processed_chunks = results.get('processed_chunks', 0)

        print(f"✅ 测试训练成功完成!")
        print(f"⏱️ 总训练时间: {training_time:.1f} 秒")

        # 文档处理结果
        print(f"\n📈 文档处理结果:")
        print(f"  发现文档: {documents_found}")
        print(f"  生成文本块: {chunks_generated}")
        print(f"  处理文本块: {processed_chunks}")
        if documents_found > 0:
            print(f"  平均每文档块数: {chunks_generated/documents_found:.1f}")

        # 向量化结果
        print(f"\n🔢 向量化结果:")
        print(f"  生成向量: {vectors_generated}")
        if chunks_generated > 0:
            print(f"  向量化成功率: {vectors_generated/chunks_generated*100:.1f}%")

        # 索引结果
        print(f"\n🔍 索引构建结果:")
        if indices_trained:
            for index_type, index_path in indices_trained.items():
                print(f"  {index_type.upper()}索引: {Path(index_path).name}")
            print(f"  训练索引数: {len(indices_trained)}")
        else:
            print(f"  未训练索引 (可能向量数不足)")

        # 全量训练预估
        print(f"\n🔮 全量训练预估:")
        if training_time > 0:
            # 基于100个文档的训练时间预估825个文档的时间
            estimated_full_time = training_time * (825 / 100)
            print(f"  预估全量训练时间: {estimated_full_time:.1f} 秒 ({estimated_full_time/60:.1f} 分钟)")
            print(f"  预估处理速度: {100/training_time:.1f} 文档/秒")

        # 建议
        print(f"\n💡 建议:")
        if results.get('success', False):
            print("  ✅ 测试训练成功，可以进行全量训练")
            print("  📊 建议监控全量训练过程中的内存使用")
            print("  🔍 可以根据测试结果调整训练参数")
        else:
            print("  ❌ 需要解决测试中发现的问题后再进行全量训练")

    def cleanup_test_data(self) -> None:
        """清理测试数据"""

        print(f"\n🧹 清理测试数据")
        print("-" * 60)

        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
            print(f"✅ 已删除测试目录: {self.test_dir}")
        else:
            print(f"ℹ️ 测试目录不存在: {self.test_dir}")

def main():
    """主函数"""

    print("🧪 小规模测试训练")
    print("=" * 80)
    print("目标: 选择100个高质量文档进行测试训练")
    print()

    trainer = SmallScaleTestTraining()

    try:
        # 1. 选择高质量文档
        selected_docs = trainer.select_high_quality_documents(100)

        if not selected_docs:
            print("❌ 没有找到合适的文档进行测试")
            return

        # 2. 创建测试数据集
        if not trainer.create_test_dataset(selected_docs):
            print("❌ 测试数据集创建失败")
            return

        # 3. 运行测试训练
        results = trainer.run_test_training()

        # 4. 分析结果
        trainer.analyze_test_results(results)

        # 5. 询问是否清理测试数据
        print(f"\n❓ 是否清理测试数据？")
        print("1. 保留测试数据 (可以手动检查)")
        print("2. 清理测试数据 (节省空间)")

        choice = input("请选择 (1/2): ").strip()

        if choice == '2':
            trainer.cleanup_test_data()
        else:
            print(f"ℹ️ 测试数据保留在: {trainer.test_dir}")

        # 6. 询问是否进行全量训练
        if results.get('success', False):
            print(f"\n🚀 测试成功! 是否进行全量训练？")
            print("1. 是，立即开始全量训练")
            print("2. 否，稍后手动启动")

            choice = input("请选择 (1/2): ").strip()

            if choice == '1':
                print(f"\n🎯 启动全量训练...")
                import subprocess
                subprocess.run(['python', 'training_automation.py'])
            else:
                print(f"\n💡 稍后可以运行以下命令进行全量训练:")
                print(f"python training_automation.py")

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        trainer.cleanup_test_data()
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        trainer.cleanup_test_data()

if __name__ == "__main__":
    main()
