"""
索引修复小部件

提供索引诊断和修复功能的GUI界面
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QProgressBar, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from ..i18n import Translator
from ...utils.index_repair import IndexRepairTool


class RepairWorker(QThread):
    """修复工作线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    diagnosis_completed = pyqtSignal(dict)
    repair_completed = pyqtSignal(bool)
    
    def __init__(self, repair_tool, index_path, operation):
        super().__init__()
        self.repair_tool = repair_tool
        self.index_path = index_path
        self.operation = operation
    
    def run(self):
        try:
            if self.operation == 'diagnose':
                self.status_updated.emit("正在诊断索引...")
                self.progress_updated.emit(25)
                
                diagnosis = self.repair_tool.diagnose_index_issues(self.index_path)
                
                self.progress_updated.emit(100)
                self.status_updated.emit("诊断完成")
                self.diagnosis_completed.emit(diagnosis)
                
            elif self.operation == 'repair':
                self.status_updated.emit("正在重建索引...")
                self.progress_updated.emit(25)
                
                success = self.repair_tool.rebuild_index(self.index_path)
                
                self.progress_updated.emit(100)
                if success:
                    self.status_updated.emit("索引重建成功")
                else:
                    self.status_updated.emit("索引重建失败")
                
                self.repair_completed.emit(success)
                
        except Exception as e:
            self.status_updated.emit(f"操作失败: {str(e)}")
            if self.operation == 'diagnose':
                self.diagnosis_completed.emit({'error': str(e)})
            else:
                self.repair_completed.emit(False)


class RepairWidget(QWidget):
    """索引修复小部件"""
    
    def __init__(self, translator: Translator):
        super().__init__()
        self.translator = translator
        self.repair_tool = None
        self.worker = None
        
        self._init_ui()
        self._init_repair_tool()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("索引修复工具")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 诊断组
        diagnosis_group = QGroupBox("索引诊断")
        diagnosis_layout = QVBoxLayout(diagnosis_group)
        
        # 诊断按钮
        self.diagnose_button = QPushButton("诊断索引")
        self.diagnose_button.clicked.connect(self._on_diagnose)
        diagnosis_layout.addWidget(self.diagnose_button)
        
        # 诊断结果
        self.diagnosis_text = QTextEdit()
        self.diagnosis_text.setMaximumHeight(200)
        self.diagnosis_text.setReadOnly(True)
        diagnosis_layout.addWidget(self.diagnosis_text)
        
        layout.addWidget(diagnosis_group)
        
        # 修复组
        repair_group = QGroupBox("索引修复")
        repair_layout = QVBoxLayout(repair_group)
        
        # 修复按钮
        self.repair_button = QPushButton("重建索引")
        self.repair_button.clicked.connect(self._on_repair)
        self.repair_button.setEnabled(False)
        repair_layout.addWidget(self.repair_button)
        
        # 优化按钮
        self.optimize_button = QPushButton("优化相似度计算")
        self.optimize_button.clicked.connect(self._on_optimize)
        repair_layout.addWidget(self.optimize_button)
        
        layout.addWidget(repair_group)
        
        # 进度组
        progress_group = QGroupBox("操作进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        progress_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_group)
        
        # 结果组
        result_group = QGroupBox("操作结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
    
    def _init_repair_tool(self):
        """初始化修复工具"""
        try:
            config = {'storage': {'base_dir': 'data/vectors'}}
            self.repair_tool = IndexRepairTool(config)
        except Exception as e:
            self.result_text.append(f"初始化修复工具失败: {str(e)}")
    
    def _get_index_path(self):
        """获取索引路径"""
        from pathlib import Path
        index_dir = Path('data/indices')
        if not index_dir.exists():
            return None
        
        index_files = list(index_dir.glob('*.idx'))
        if not index_files:
            return None
        
        # 返回最新的索引文件
        return str(max(index_files, key=lambda x: x.stat().st_mtime))
    
    def _on_diagnose(self):
        """诊断按钮点击处理"""
        if not self.repair_tool:
            QMessageBox.warning(self, "错误", "修复工具未初始化")
            return
        
        index_path = self._get_index_path()
        if not index_path:
            QMessageBox.warning(self, "错误", "未找到索引文件")
            return
        
        # 禁用按钮
        self.diagnose_button.setEnabled(False)
        self.repair_button.setEnabled(False)
        
        # 清空结果
        self.diagnosis_text.clear()
        self.result_text.clear()
        
        # 启动工作线程
        self.worker = RepairWorker(self.repair_tool, index_path, 'diagnose')
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.diagnosis_completed.connect(self._on_diagnosis_completed)
        self.worker.start()
    
    def _on_repair(self):
        """修复按钮点击处理"""
        if not self.repair_tool:
            QMessageBox.warning(self, "错误", "修复工具未初始化")
            return
        
        index_path = self._get_index_path()
        if not index_path:
            QMessageBox.warning(self, "错误", "未找到索引文件")
            return
        
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认", 
            "重建索引将删除现有索引并重新创建，这可能需要一些时间。是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 禁用按钮
        self.diagnose_button.setEnabled(False)
        self.repair_button.setEnabled(False)
        
        # 清空结果
        self.result_text.clear()
        
        # 启动工作线程
        self.worker = RepairWorker(self.repair_tool, index_path, 'repair')
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.repair_completed.connect(self._on_repair_completed)
        self.worker.start()
    
    def _on_optimize(self):
        """优化按钮点击处理"""
        if not self.repair_tool:
            QMessageBox.warning(self, "错误", "修复工具未初始化")
            return
        
        try:
            self.status_label.setText("正在优化相似度计算...")
            self.progress_bar.setValue(50)
            
            result = self.repair_tool.optimize_similarity_calculation()
            
            self.progress_bar.setValue(100)
            self.status_label.setText("优化完成")
            
            # 显示结果
            self.result_text.clear()
            self.result_text.append("=== 相似度计算优化结果 ===\n")
            
            if result.get('optimizations_applied'):
                self.result_text.append("已应用的优化:")
                for opt in result['optimizations_applied']:
                    self.result_text.append(f"✓ {opt}")
                self.result_text.append("")
            
            if result.get('recommendations'):
                self.result_text.append("建议:")
                for rec in result['recommendations']:
                    self.result_text.append(f"• {rec}")
            
        except Exception as e:
            self.result_text.append(f"优化过程中出错: {str(e)}")
            self.status_label.setText("优化失败")
    
    def _on_diagnosis_completed(self, diagnosis):
        """诊断完成处理"""
        # 重新启用按钮
        self.diagnose_button.setEnabled(True)
        
        if 'error' in diagnosis:
            self.diagnosis_text.append(f"诊断失败: {diagnosis['error']}")
            return
        
        # 显示诊断结果
        self.diagnosis_text.append("=== 索引诊断结果 ===\n")
        self.diagnosis_text.append(f"索引文件存在: {'是' if diagnosis['index_exists'] else '否'}")
        self.diagnosis_text.append(f"索引向量数量: {diagnosis['index_size']}")
        self.diagnosis_text.append(f"元数据数量: {diagnosis['metadata_count']}")
        self.diagnosis_text.append(f"向量存储数量: {diagnosis['vector_count']}")
        
        if diagnosis['mapping_issues']:
            self.diagnosis_text.append("\n发现的问题:")
            for issue in diagnosis['mapping_issues']:
                self.diagnosis_text.append(f"⚠ {issue}")
            
            # 启用修复按钮
            self.repair_button.setEnabled(True)
        else:
            self.diagnosis_text.append("\n✓ 未发现问题")
        
        if diagnosis['recommendations']:
            self.diagnosis_text.append("\n建议:")
            for rec in diagnosis['recommendations']:
                self.diagnosis_text.append(f"• {rec}")
    
    def _on_repair_completed(self, success):
        """修复完成处理"""
        # 重新启用按钮
        self.diagnose_button.setEnabled(True)
        self.repair_button.setEnabled(True)
        
        if success:
            self.result_text.append("✓ 索引重建成功！")
            self.result_text.append("建议重新运行诊断以验证修复结果。")
            QMessageBox.information(self, "成功", "索引重建成功！")
        else:
            self.result_text.append("✗ 索引重建失败")
            QMessageBox.critical(self, "失败", "索引重建失败，请查看日志了解详细信息。")
    
    def on_language_changed(self):
        """语言变更回调"""
        # 更新UI文本
        pass
