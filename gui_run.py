#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MD向量处理器GUI启动脚本
"""

import sys
import os
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        # 确保src目录在Python路径中
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))

        print("正在导入GUI模块...")

        # 检查 PyQt 安装情况
        try:
            print("检查 PyQt 安装情况...")
            try:
                import PyQt6
                print(f"找到 PyQt6，版本: {PyQt6.__version__ if hasattr(PyQt6, '__version__') else '未知'}")
                qt_version = 6
            except ImportError:
                print("未找到 PyQt6，尝试导入 PyQt5...")
                import PyQt5
                print(f"找到 PyQt5，版本: {PyQt5.__version__ if hasattr(PyQt5, '__version__') else '未知'}")
                qt_version = 5

            # 修改 src/gui 中的导入语句
            if qt_version == 5:
                print("检测到 PyQt5，修改导入语句...")
                gui_files = list(Path("src/gui").glob("**/*.py"))
                for file_path in gui_files:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()

                    # 替换 PyQt6 为 PyQt5
                    if "PyQt6" in content:
                        content = content.replace("PyQt6", "PyQt5")
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write(content)
                        print(f"已修改: {file_path}")
        except Exception as e:
            print(f"检查 PyQt 安装情况时出错: {e}")
            import traceback
            traceback.print_exc()

        # 导入GUI模块
        print("正在导入 src.gui 模块...")

        # 检查 QActionGroup 的位置
        print("检查 QActionGroup 的位置...")
        try:
            from PyQt6.QtWidgets import QActionGroup
            print("QActionGroup 在 PyQt6.QtWidgets 中")
        except ImportError:
            try:
                from PyQt6.QtGui import QActionGroup
                print("QActionGroup 在 PyQt6.QtGui 中")
            except ImportError:
                print("无法导入 QActionGroup")

        # 修复 app.py 中的 QActionGroup 导入
        try:
            print("修复 app.py 中的 QActionGroup 导入...")
            app_path = Path("src/gui/app.py")
            with open(app_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "from PyQt6.QtWidgets import (" in content and "QActionGroup" in content:
                # 从 QtWidgets 移除 QActionGroup
                content = content.replace(
                    "    QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog,\n    QActionGroup",
                    "    QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog"
                )
                # 添加到 QtGui
                content = content.replace(
                    "from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction",
                    "from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction, QActionGroup"
                )
                with open(app_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 app.py 中的 QActionGroup 导入")
        except Exception as e:
            print(f"修复 app.py 中的 QActionGroup 导入时出错: {e}")

        # 修复 theme_manager.py 中的 QActionGroup 导入
        try:
            print("修复 theme_manager.py 中的 QActionGroup 导入...")
            theme_manager_path = Path("src/gui/theme/theme_manager.py")
            if theme_manager_path.exists():
                with open(theme_manager_path, "r", encoding="utf-8") as f:
                    content = f.read()

                if "from PyQt6.QtGui import QPalette, QColor" in content:
                    content = content.replace(
                        "from PyQt6.QtGui import QPalette, QColor",
                        "from PyQt6.QtGui import QPalette, QColor, QActionGroup"
                    )
                    with open(theme_manager_path, "w", encoding="utf-8") as f:
                        f.write(content)
                    print("已修复 theme_manager.py 中的 QActionGroup 导入")
        except Exception as e:
            print(f"修复 theme_manager.py 中的 QActionGroup 导入时出错: {e}")

        # 1. 修复 src/gui/i18n/translator.py 中的 set_language 方法
        try:
            print("修复 translator.py 中的 set_language 方法...")
            translator_path = Path("src/gui/i18n/translator.py")
            with open(translator_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "self._notify_language_changed()" in content:
                content = content.replace(
                    "self._notify_language_changed()",
                    "# self._notify_language_changed() # 禁用观察者通知，避免可能的循环引用问题"
                )
                with open(translator_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 translator.py 中的 set_language 方法")
        except Exception as e:
            print(f"修复 translator.py 时出错: {e}")

        # 2. 修复 src/gui/app.py 中的 _on_language_changed 方法
        try:
            print("修复 app.py 中的 _on_language_changed 方法...")
            app_path = Path("src/gui/app.py")
            with open(app_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "self.on_language_changed()" in content:
                content = content.replace(
                    "self.on_language_changed()",
                    "# self.on_language_changed() # 禁用语言变更回调，避免可能的循环引用问题"
                )
                with open(app_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 app.py 中的 _on_language_changed 方法")
        except Exception as e:
            print(f"修复 app.py 时出错: {e}")

        # 尝试导入 src.gui
        try:
            print("尝试导入 src.gui...")
            import src.gui
            print("成功导入 src.gui 模块")

            print("正在导入 run_gui 函数...")
            from src.gui import run_gui
            print("成功导入 run_gui 函数")

            print("正在运行GUI...")
            # 运行GUI
            run_gui()
        except Exception as e:
            print(f"导入或运行 GUI 时出错: {e}")
            import traceback
            traceback.print_exc()

    except ImportError as e:
        logger.error(f"导入错误: {e}")
        print(f"错误: 无法导入必要的模块。请确保已安装所有依赖项:")
        print("pip install -r requirements-gui.txt")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    except Exception as e:
        import traceback
        logger.error(f"启动GUI时出错: {e}")
        print(f"错误: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
